'use client';

import React, { ReactNode } from 'react';
import FeatureGate from '@/components/subscription/FeatureGate';
import FeatureButton from '@/components/subscription/FeatureButton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from 'lucide-react';
import { Crown, Lock, AlertTriangle } from 'lucide-react';

interface SubscriptionAwareWrapperProps {
  children: ReactNode;
  featureId: string;
  requiredPlan?: string;
  fallbackComponent?: ReactNode;
  showUpgradePrompt?: boolean;
  className?: string;
}

interface FeatureCardProps {
  title: string;
  description: string;
  featureId: string;
  requiredPlan?: string;
  children: ReactNode;
  icon?: ReactNode;
  className?: string;
}

interface SubscriptionBannerProps {
  currentPlan: string;
  requiredPlan: string;
  featureName: string;
  onUpgrade?: () => void;
}

/**
 * Wrapper principal pour rendre les composants subscription-aware
 */
export function SubscriptionAwareWrapper({
  children,
  featureId,
  requiredPlan = 'premium',
  fallbackComponent,
  showUpgradePrompt = true,
  className = ''
}: SubscriptionAwareWrapperProps) {
  return (
    <FeatureGate
      featureId={featureId}
      fallback={fallbackComponent}
      showUpgradePrompt={showUpgradePrompt}
      className={className}
    >
      {children}
    </FeatureGate>
  );
}

/**
 * Card avec gestion automatique des fonctionnalités premium
 */
export function FeatureCard({
  title,
  description,
  featureId,
  requiredPlan = 'premium',
  children,
  icon,
  className = ''
}: FeatureCardProps) {
  return (
    <FeatureGate
      featureId={featureId}
      fallback={
        <Card className={`relative overflow-hidden border-dashed border-gray-300 ${className}`}>
          <div className="absolute inset-0 bg-gray-50 bg-opacity-80 flex items-center justify-center z-10">
            <div className="text-center p-6">
              <Lock className="w-8 h-8 mx-auto mb-3 text-gray-400" />
              <h3 className="font-semibold text-gray-600 mb-2">Fonctionnalité Premium</h3>
              <p className="text-sm text-gray-500 mb-4">
                Mettez à niveau vers {requiredPlan} pour débloquer cette fonctionnalité
              </p>
              <FeatureButton
                featureId={featureId}
                size="sm"
                className="bg-purple-600 hover:bg-purple-700"
              >
                <Crown className="w-4 h-4 mr-2" />
                Mettre à niveau
              </FeatureButton>
            </div>
          </div>
          <CardHeader className="opacity-50">
            <CardTitle className="flex items-center">
              {icon}
              <span className="ml-2">{title}</span>
              <Badge  className="ml-auto">
                {requiredPlan}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="opacity-50">
            <p className="text-gray-600 mb-4">{description}</p>
            <div className="h-32 bg-gray-100 rounded"></div>
          </CardContent>
        </Card>
      }
    >
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center">
            {icon}
            <span className="ml-2">{title}</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-gray-600 mb-4">{description}</p>
          {children}
        </CardContent>
      </Card>
    </FeatureGate>
  );
}

/**
 * Banner d'information sur l'abonnement
 */
export function SubscriptionBanner({
  currentPlan,
  requiredPlan,
  featureName,
  onUpgrade
}: SubscriptionBannerProps) {
  const isUpgradeNeeded = currentPlan !== requiredPlan;

  if (!isUpgradeNeeded) return null;

  return (
    <Card className="border-yellow-200 bg-yellow-50 mb-6">
      <CardContent className="p-4">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600" />
            <div>
              <h4 className="font-semibold text-yellow-800">
                Mise à niveau requise
              </h4>
              <p className="text-sm text-yellow-700">
                La fonctionnalité "{featureName}" nécessite un plan {requiredPlan}. 
                Votre plan actuel : {currentPlan}
              </p>
            </div>
          </div>
          <FeatureButton
            featureId={featureName}
            onClick={onUpgrade}
            className="bg-yellow-600 hover:bg-yellow-700 text-white"
          >
            <Crown className="w-4 h-4 mr-2" />
            Mettre à niveau
          </FeatureButton>
        </div>
      </CardContent>
    </Card>
  );
}

/**
 * Hook pour vérifier l'accès aux fonctionnalités
 * Maintenant utilise le contexte de subscription
 */
export function useFeatureAccess(featureId: string, requiredPlan: string = 'premium') {
  // Importé depuis le contexte de subscription
  const { useFeatureAccess: useContextFeatureAccess } = require('@/context/SubscriptionContext');
  return useContextFeatureAccess(featureId, requiredPlan);
}

/**
 * Composant pour afficher les limites d'utilisation
 */
interface UsageLimitProps {
  current: number;
  limit: number;
  label: string;
  featureId: string;
  className?: string;
}

export function UsageLimit({
  current,
  limit,
  label,
  featureId,
  className = ''
}: UsageLimitProps) {
  const percentage = Math.min((current / limit) * 100, 100);
  const isNearLimit = percentage >= 80;
  const isAtLimit = percentage >= 100;

  return (
    <div className={`p-4 border rounded-lg ${className}`}>
      <div className="flex justify-between items-center mb-2">
        <span className="text-sm font-medium">{label}</span>
        <span className="text-sm text-gray-600">
          {current} / {limit}
        </span>
      </div>
      
      <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
        <div
          className={`h-2 rounded-full transition-all duration-300 ${
            isAtLimit 
              ? 'bg-red-500' 
              : isNearLimit 
                ? 'bg-yellow-500' 
                : 'bg-green-500'
          }`}
          style={{ width: `${percentage}%` }}
        />
      </div>
      
      {isAtLimit && (
        <div className="flex items-center justify-between text-sm">
          <span className="text-red-600 flex items-center">
            <AlertTriangle className="w-4 h-4 mr-1" />
            Limite atteinte
          </span>
          <FeatureButton
            featureId={featureId}
            size="sm"
            variant="outline"
          >
            Augmenter la limite
          </FeatureButton>
        </div>
      )}
      
      {isNearLimit && !isAtLimit && (
        <div className="text-sm text-yellow-600 flex items-center">
          <AlertTriangle className="w-4 h-4 mr-1" />
          Proche de la limite
        </div>
      )}
    </div>
  );
}

export default SubscriptionAwareWrapper;
