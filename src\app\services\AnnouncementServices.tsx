import { getTokenFromCookie } from "@/app/services/UserServices";
import { BASE_API_URL } from "./AuthContext";
import ApiInterceptorService, { SubscriptionRequiredError, FeatureAccessDeniedError } from "./ApiInterceptorService";

/**
 * Helper pour gérer les erreurs de subscription de manière cohérente
 */
const handleSubscriptionError = (error: any, defaultMessage: string): never => {
  console.error('Announcement service error:', error);

  if (error instanceof SubscriptionRequiredError) {
    throw new Error(`Mise à niveau requise: ${error.subscriptionError.subscription_required}`);
  }

  if (error instanceof FeatureAccessDeniedError) {
    throw new Error(`Accès refusé: Cette fonctionnalité nécessite un abonnement ${error.subscriptionError.subscription_required}`);
  }

  throw new Error(defaultMessage);
};

export interface AnnouncementSchema extends Record<string, unknown> {
  _id: string;
  announcement_id: string;
  title: string;
  content: string;
  school_id: string;
  author_id: string;
  target_audience: 'all' | 'teachers' | 'parents' | 'students';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  is_published: boolean;
  published_at?: Date;
  expires_at?: Date;
  created_at: Date;
  updated_at: Date;
}

export interface AnnouncementCreateSchema {
  title: string;
  content: string;
  school_id: string;
  target_audience: 'all' | 'teachers' | 'parents' | 'students';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  is_published: boolean;
  expires_at?: Date;
}

// Get all announcements
export async function getAnnouncements(): Promise<AnnouncementSchema[]> {
  try {
    return await ApiInterceptorService.get('/announcement/get-announcements');
  } catch (error) {
    return handleSubscriptionError(error, "Failed to fetch announcements");
  }
}

// Get announcement by ID
export async function getAnnouncementById(announcementId: string): Promise<AnnouncementSchema> {
  try {
    return await ApiInterceptorService.get(`/announcement/get-announcement/${announcementId}`);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to fetch announcement");
  }
}

// Create a new announcement
export async function createAnnouncement(announcementData: AnnouncementCreateSchema): Promise<AnnouncementSchema> {
  try {
    return await ApiInterceptorService.post('/announcement/create-announcement', announcementData);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to create announcement");
  }
}

// Update an announcement
export async function updateAnnouncement(announcementId: string, announcementData: Partial<AnnouncementCreateSchema>): Promise<AnnouncementSchema> {
  try {
    return await ApiInterceptorService.put(`/announcement/update-announcement/${announcementId}`, announcementData);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to update announcement");
  }
}

// Delete an announcement
export async function deleteAnnouncement(announcementId: string): Promise<{ message: string }> {
  try {
    return await ApiInterceptorService.delete(`/announcement/delete-announcement/${announcementId}`);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to delete announcement");
  }
}

// Delete multiple announcements
export async function deleteMultipleAnnouncements(announcementIds: string[]): Promise<{ message: string }> {
  try {
    return await ApiInterceptorService.post('/announcement/delete-announcements', { ids: announcementIds });
  } catch (error) {
    return handleSubscriptionError(error, "Failed to delete multiple announcements");
  }
}

// Delete all announcements
export async function deleteAllAnnouncements(): Promise<{ message: string; deletedCount: number }> {
  try {
    return await ApiInterceptorService.delete('/announcement/delete-all-announcements');
  } catch (error) {
    return handleSubscriptionError(error, "Failed to delete all announcements");
  }
}

// Get announcements by school
export async function getAnnouncementsBySchool(schoolId: string): Promise<AnnouncementSchema[]> {
  try {
    return await ApiInterceptorService.get(`/announcement/get-announcements-by-school/${schoolId}`);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to fetch announcements by school");
  }
}

// Publish/unpublish announcement
export async function toggleAnnouncementPublication(announcementId: string, isPublished: boolean): Promise<AnnouncementSchema> {
  try {
    return await ApiInterceptorService.put(`/announcement/toggle-publication/${announcementId}`, { is_published: isPublished });
  } catch (error) {
    return handleSubscriptionError(error, "Failed to toggle announcement publication");
  }
}

// Get recent announcements by school (last 5 published announcements)
export async function getRecentAnnouncementsBySchool(schoolId: string): Promise<AnnouncementSchema[]> {
  try {
    const allAnnouncements = await getAnnouncementsBySchool(schoolId);

    // Filter published announcements and sort by creation date (most recent first)
    const recentAnnouncements = allAnnouncements
      .filter(announcement => announcement.is_published)
      .sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
      .slice(0, 5); // Get only the 5 most recent

    return recentAnnouncements;
  } catch (error) {
    console.error("Error fetching recent announcements by school:", error);
    throw new Error("Failed to fetch recent announcements by school");
  }
}

// Get announcements by school sorted by priority (max 5 for dashboard)
export async function getAnnouncementsByPriority(schoolId: string, limit: number = 5): Promise<AnnouncementSchema[]> {
  try {
    const allAnnouncements = await getAnnouncementsBySchool(schoolId);

    // Priority order: urgent > high > medium > low
    const priorityOrder = { 'urgent': 4, 'high': 3, 'medium': 2, 'low': 1 };

    // Filter published announcements and sort by priority, then by creation date
    const prioritizedAnnouncements = allAnnouncements
      .filter(announcement => announcement.is_published)
      .sort((a, b) => {
        // First sort by priority
        const priorityA = priorityOrder[a.priority as keyof typeof priorityOrder] || 0;
        const priorityB = priorityOrder[b.priority as keyof typeof priorityOrder] || 0;

        if (priorityA !== priorityB) {
          return priorityB - priorityA; // Higher priority first
        }

        // If same priority, sort by creation date (most recent first)
        return new Date(b.created_at).getTime() - new Date(a.created_at).getTime();
      })
      .slice(0, limit); // Get only the specified number

    return prioritizedAnnouncements;
  } catch (error) {
    console.error("Error fetching announcements by priority:", error);
    throw new Error("Failed to fetch announcements by priority");
  }
}
