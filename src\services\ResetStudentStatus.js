
const Student = require('../models/Student');
const AcademicYear = require('../models/AcademicYear');

const resetStudentStatusAtYearEnd = async () => {
  try {
    const today = new Date();
    const currentMonth = today.getMonth(); // 0 = Jan, 5 = June
    const currentDay = today.getDate();
    
    // Only run on June 30th (end of academic year)
    if (currentMonth !== 5 || currentDay !== 30) {
      console.log("📅 Student status reset only runs on June 30th (end of academic year).");
      return;
    }

    // Get the current academic year that's ending
    const currentYear = today.getFullYear();
    const endingAcademicYear = `${currentYear - 1}/${currentYear}`;
    
    // Verify this academic year exists
    const academicYear = await AcademicYear.findOne({ 
      academic_year: endingAcademicYear 
    });
    
    if (!academicYear) {
      console.log(`❌ Academic year ${endingAcademicYear} not found`);
      return;
    }

    console.log(`🔄 Starting student status reset for academic year: ${endingAcademicYear}`);

    // Reset all enrolled students to "not enrolled" for the new academic year
    const result = await Student.updateMany(
      { 
        status: "enrolled"
      },
      { 
        $set: { 
          status: "not enrolled",
          class_id: null,
          class_level: null,
          enrollement_date: null,
          registration_date: null,
          registered: false,          
        }
      }
    );

    // Update class_history to mark previous enrollments as completed
    // await Student.updateMany(
    //   {
    //     "class_history.academic_year": endingAcademicYear,
    //     "class_history.status": "enrolled"
    //   },
    //   {
    //     $set: {
    //       "class_history.$.status": "graduated"
    //     }
    //   }
    // );

    console.log(`✅ Reset ${result.modifiedCount} students for new academic year`);
    // console.log(`📚 Updated class history for academic year: ${endingAcademicYear}`);
    
    return {
      success: true,
      studentsReset: result.modifiedCount,
      academicYear: endingAcademicYear
    };

  } catch (error) {
    console.error('❌ Error resetting student status:', error);
    throw error;
  }
};

module.exports = { resetStudentStatusAtYearEnd };

