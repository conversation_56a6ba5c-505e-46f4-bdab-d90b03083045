import { getTokenFromCookie } from './UserServices';
import { useTranslation } from '@/hooks/useTranslation';

export interface ApiErrorData {
  message: string;
  error_code: string;
  status: number;
  data?: any;
}

export interface SubscriptionErrorData extends ApiErrorData {
  feature_id?: string;
  subscription_required?: string;
  current_subscription?: string;
  upgrade_url?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error_code?: string;
  subscription_required?: string;
  current_subscription?: string;
  feature_id?: string;
}

/**
 * Service pour intercepter et gérer les appels API avec gestion des erreurs d'abonnement
 */
export class ApiInterceptorService {
  private static baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api';

  /**
   * Effectue un appel API avec gestion automatique des erreurs d'abonnement
   */
  static async apiCall<T = any>(
    endpoint: string,
    options: RequestInit = {},
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    try {
      const token = getTokenFromCookie('idToken');
      
      const defaultHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      };

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: defaultHeaders
      });

      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        await this.handleApiError(response, data, showUpgradePrompt);
      }

      if (!data.success) {
        throw new Error(data.message || 'API call failed');
      }

      return data.data as T;
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    }
  }

  /**
   * Gère les erreurs d'API avec traitement spécial pour les erreurs d'abonnement
   */
  private static async handleApiError(
    response: Response,
    data: ApiResponse,
    showUpgradePrompt: boolean
  ): Promise<void> {
    // Fonction helper pour convertir ApiResponse en SubscriptionErrorData
    const createSubscriptionError = (apiResponse: ApiResponse): SubscriptionErrorData => ({
      message: apiResponse.message || 'An error occurred',
      error_code: apiResponse.error_code || 'UNKNOWN_ERROR',
      status: response.status,
      feature_id: apiResponse.feature_id,
      subscription_required: apiResponse.subscription_required,
      current_subscription: apiResponse.current_subscription,
      data: apiResponse.data
    });

    // Fonction helper pour convertir ApiResponse en ApiErrorData
    const createApiError = (apiResponse: ApiResponse): ApiErrorData => ({
      message: apiResponse.message || 'An error occurred',
      error_code: apiResponse.error_code || 'UNKNOWN_ERROR',
      status: response.status,
      data: apiResponse.data
    });

    switch (response.status) {
      case 402: // Payment Required - Subscription issue
        const subscriptionError = createSubscriptionError(data);
        if (showUpgradePrompt) {
          await this.handleSubscriptionError(subscriptionError);
        }
        throw new SubscriptionRequiredError(subscriptionError);

      case 403: // Forbidden - Feature access denied
        const accessError = createSubscriptionError(data);
        if (data.error_code === 'FEATURE_ACCESS_DENIED' && showUpgradePrompt) {
          await this.handleFeatureAccessDenied(accessError);
        }
        throw new FeatureAccessDeniedError(accessError);

      case 404:
        if (data.error_code === 'FEATURE_NOT_FOUND') {
          throw new FeatureNotFoundError(data.message || 'Feature not found');
        }
        throw new NotFoundError(data.message || 'Resource not found');

      case 429: // Too Many Requests - Feature limit exceeded
        const limitError = createSubscriptionError(data);
        if (data.error_code === 'FEATURE_LIMIT_EXCEEDED') {
          await this.handleFeatureLimitExceeded(limitError);
        }
        throw new FeatureLimitExceededError(limitError);

      default:
        throw new ApiError(data.message || 'An error occurred');
    }
  }

  /**
   * Gère les erreurs d'abonnement insuffisant
   */
  private static async handleSubscriptionError(error: SubscriptionErrorData): Promise<void> {
    const { showUpgradeModal } = await import('@/components/subscription/UpgradeModal');

    showUpgradeModal({
      currentPlan: error.current_subscription || 'basic',
      requiredPlan: error.subscription_required || 'standard',
      featureId: error.feature_id,
      message: error.message
    });
  }

  /**
   * Gère les erreurs d'accès aux fonctionnalités
   */
  private static async handleFeatureAccessDenied(error: SubscriptionErrorData): Promise<void> {
    const { showFeatureLockedModal } = await import('@/components/subscription/FeatureLockedModal');

    showFeatureLockedModal({
      featureId: error.feature_id || '',
      requiredPlan: error.subscription_required || 'standard',
      currentPlan: error.current_subscription || 'basic'
    });
  }

  /**
   * Gère les erreurs de limite de fonctionnalité dépassée
   */
  private static async handleFeatureLimitExceeded(error: SubscriptionErrorData): Promise<void> {
    const { showLimitExceededModal } = await import('@/components/subscription/LimitExceededModal');

    showLimitExceededModal({
      featureId: error.feature_id || '',
      limitDetails: error.data || {},
      upgradeUrl: error.upgrade_url
    });
  }

  /**
   * Appel API GET avec gestion d'erreurs
   */
  static async get<T = any>(endpoint: string, showUpgradePrompt: boolean = true): Promise<T> {
    return this.apiCall<T>(endpoint, { method: 'GET' }, showUpgradePrompt);
  }

  /**
   * Appel API POST avec gestion d'erreurs
   */
  static async post<T = any>(
    endpoint: string, 
    data?: any, 
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    return this.apiCall<T>(
      endpoint, 
      { 
        method: 'POST', 
        body: data ? JSON.stringify(data) : undefined 
      }, 
      showUpgradePrompt
    );
  }

  /**
   * Appel API PUT avec gestion d'erreurs
   */
  static async put<T = any>(
    endpoint: string, 
    data?: any, 
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    return this.apiCall<T>(
      endpoint, 
      { 
        method: 'PUT', 
        body: data ? JSON.stringify(data) : undefined 
      }, 
      showUpgradePrompt
    );
  }

  /**
   * Appel API DELETE avec gestion d'erreurs
   */
  static async delete<T = any>(endpoint: string, showUpgradePrompt: boolean = true): Promise<T> {
    return this.apiCall<T>(endpoint, { method: 'DELETE' }, showUpgradePrompt);
  }

  /**
   * Upload de fichier avec gestion d'erreurs
   */
  static async uploadFile<T = any>(
    endpoint: string,
    file: File,
    fieldName: string = 'file',
    additionalData?: Record<string, string>,
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    const token = getTokenFromCookie('idToken');
    const formData = new FormData();
    
    formData.append(fieldName, file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    return this.apiCall<T>(
      endpoint,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
          // Ne pas définir Content-Type pour FormData
        },
        body: formData
      },
      showUpgradePrompt
    );
  }

  /**
   * Vérification silencieuse d'accès à une fonctionnalité
   */
  static async checkFeatureAccess(featureId: string, schoolId: string): Promise<boolean> {
    try {
      await this.get(`/feature-registry/check/${featureId}/school/${schoolId}`, false);
      return true;
    } catch (error) {
      if (error instanceof SubscriptionRequiredError || error instanceof FeatureAccessDeniedError) {
        return false;
      }
      throw error;
    }
  }
}

// Classes d'erreur personnalisées
export class SubscriptionRequiredError extends Error {
  public readonly subscriptionError: SubscriptionErrorData;

  constructor(error: SubscriptionErrorData) {
    super(error.message);
    this.name = 'SubscriptionRequiredError';
    this.subscriptionError = error;
  }
}

export class FeatureAccessDeniedError extends Error {
  public readonly subscriptionError: SubscriptionErrorData;

  constructor(error: SubscriptionErrorData) {
    super(error.message);
    this.name = 'FeatureAccessDeniedError';
    this.subscriptionError = error;
  }
}

export class FeatureLimitExceededError extends Error {
  public readonly subscriptionError: SubscriptionErrorData;

  constructor(error: SubscriptionErrorData) {
    super(error.message);
    this.name = 'FeatureLimitExceededError';
    this.subscriptionError = error;
  }
}

export class FeatureNotFoundError extends Error {
  public readonly errorData: ApiErrorData;

  constructor(message: string, errorData?: ApiErrorData) {
    super(message);
    this.name = 'FeatureNotFoundError';
    this.errorData = errorData || {
      message,
      error_code: 'FEATURE_NOT_FOUND',
      status: 404
    };
  }
}

export class NotFoundError extends Error {
  public readonly errorData: ApiErrorData;

  constructor(message: string, errorData?: ApiErrorData) {
    super(message);
    this.name = 'NotFoundError';
    this.errorData = errorData || {
      message,
      error_code: 'NOT_FOUND',
      status: 404
    };
  }
}

export class ApiError extends Error {
  public readonly errorData: ApiErrorData;

  constructor(message: string, errorData?: ApiErrorData) {
    super(message);
    this.name = 'ApiError';
    this.errorData = errorData || {
      message,
      error_code: 'UNKNOWN_ERROR',
      status: 500
    };
  }
}

export default ApiInterceptorService;
