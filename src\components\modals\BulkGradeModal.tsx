"use client";

import React, { useState, useEffect } from "react";
import { X, Users, Save, Search, ChevronDown, Calendar, AlertCircle, CheckCircle } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import { getAvailableTerms, getGradeRecords, GradeTerm, GradeSequence, GradeRecord } from "@/app/services/GradeServices";
import useAuth from "@/app/hooks/useAuth";

interface BulkGradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit: (data: any) => Promise<void>;
  students: any[];
  subjects: any[];
  examTypes: any[];
  loading?: boolean;
  classData?: any;
  subjectData?: any;
  classId?: string; // Add the actual class_id used for API calls
  subjectId?: string; // Add the actual subject_id used for API calls
  selectedAcademicYear?: string; // Add academic year prop
}

interface StudentGrade {
  student_id: string;
  student_name: string;
  score: string;
  grade: string;
  comments: string;
  hasError: boolean;
  errorMessage: string;
  existingGradeId?: string; // For updating existing grades
}

export default function BulkGradeModal({
  isOpen,
  onClose,
  onSubmit,
  students,
  subjects,
  examTypes,
  loading = false,
  classData,
  subjectData,
  classId,
  subjectId,
  selectedAcademicYear
}: BulkGradeModalProps) {
  const { user } = useAuth();
  const schoolId: any = user?.school_ids?.[0] || user?.school_id;

  // Form data for exam details
  const [examData, setExamData] = useState({
    exam_type: "",
    term_id: "",
    sequence_number: 1,
    academic_year: "",
  });

  // Student grades data
  const [studentGrades, setStudentGrades] = useState<StudentGrade[]>([]);

  // Debug: Watch studentGrades changes
  useEffect(() => {
    console.log('🔄 studentGrades state changed:', studentGrades.map(s => ({
      name: s.student_name,
      score: s.score,
      grade: s.grade,
      existingId: s.existingGradeId
    })));
  }, [studentGrades]);
  const [searchTerm, setSearchTerm] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Terms and sequences
  const [availableTerms, setAvailableTerms] = useState<GradeTerm[]>([]);
  const [availableSequences, setAvailableSequences] = useState<GradeSequence[]>([]);
  const [filteredSequences, setFilteredSequences] = useState<GradeSequence[]>([]);
  const [loadingTerms, setLoadingTerms] = useState(false);
  const [loadingExistingGrades, setLoadingExistingGrades] = useState(false);
  const [forceRender, setForceRender] = useState(0);

  // Initialize student grades when modal opens
  useEffect(() => {
    if (isOpen && students.length > 0) {
      console.log('🎓 Initializing bulk grade modal with:', {
        students: students.length,
        examTypes: examTypes?.length || 0,
        subjects: subjects?.length || 0,
        classData,
        subjectData
      });
      console.log('📋 Exam types data:', examTypes);
      console.log('🏫 Class data:', classData);
      console.log('📚 Subject data:', subjectData);
      console.log('👥 Student objects:', students.map(s => ({
        _id: s._id,
        student_id: s.student_id,
        name: s.name,
        first_name: s.first_name,
        last_name: s.last_name
      })));

      const initialGrades: StudentGrade[] = students.map(student => ({
        student_id: student.student_id || student._id, // Use student_id first, fallback to _id
        student_name: student.name || `${student.first_name} ${student.last_name}`,
        score: "",
        grade: "",
        comments: "",
        hasError: false,
        errorMessage: ""
      }));
      setStudentGrades(initialGrades);
    }
  }, [isOpen, students, examTypes, subjects]);

  // Fetch terms when modal opens or academic year changes
  useEffect(() => {
    const fetchTerms = async () => {
      if (!isOpen || !schoolId) return;

      try {
        setLoadingTerms(true);
        console.log('🔍 BulkGradeModal - Fetching terms for school:', schoolId, 'academic year:', selectedAcademicYear);
        // Pass selected academic year to filter terms
        const response = await getAvailableTerms(schoolId, selectedAcademicYear);
        console.log('📋 BulkGradeModal - Available terms response for academic year:', selectedAcademicYear, response);
        setAvailableTerms(response.terms || []);

        // Reset exam data when academic year changes
        if (selectedAcademicYear) {
          setExamData(prev => ({
            ...prev,
            term_id: "",
            sequence_number: 1,
            academic_year: selectedAcademicYear
          }));
        }

        // Sequences are available within each term, not at the response level
      } catch (error) {
        console.error("Error fetching terms:", error);
        setAvailableTerms([]);
      } finally {
        setLoadingTerms(false);
      }
    };

    fetchTerms();
  }, [isOpen, schoolId, selectedAcademicYear]);

  // Filter sequences when term changes
  useEffect(() => {
    if (examData.term_id && availableTerms.length > 0) {
      const selectedTerm = availableTerms.find(term => term._id === examData.term_id);
      if (selectedTerm && selectedTerm.sequences) {
        console.log(`🔍 Selected term: ${selectedTerm.name}, sequences:`, selectedTerm.sequences);
        setFilteredSequences(selectedTerm.sequences);
        // Reset sequence selection to first available sequence
        if (selectedTerm.sequences.length > 0) {
          setExamData(prev => ({
            ...prev,
            sequence_number: selectedTerm.sequences[0].sequence_number,
            academic_year: selectedTerm.academic_year
          }));
        }
      } else {
        setFilteredSequences([]);
      }
    } else {
      // No term selected, show default sequences or empty
      setFilteredSequences([]);
      setExamData(prev => ({ ...prev, sequence_number: 1 }));
    }
  }, [examData.term_id, availableTerms]);

  // Fetch existing grades when term, sequence, or exam type changes
  useEffect(() => {
    const actualSubjectId = subjectId || subjectData?._id;
    const actualClassId = classId || classData?._id;

    console.log('🔍 Auto-fill useEffect triggered with:', {
      term_id: examData.term_id,
      exam_type: examData.exam_type,
      sequence_number: examData.sequence_number,
      actualSubjectId,
      actualClassId,
      studentGrades_length: studentGrades.length
    });

    if (examData.term_id && examData.exam_type && examData.sequence_number && actualSubjectId && actualClassId && studentGrades.length > 0) {
      console.log('🔄 Triggering auto-fill due to form changes:', {
        term_id: examData.term_id,
        sequence_number: examData.sequence_number,
        exam_type: examData.exam_type,
        students_count: studentGrades.length
      });

      fetchExistingGrades();
    } else {
      console.log('❌ Auto-fill conditions not met');
    }
  }, [examData.term_id, examData.sequence_number, examData.exam_type, subjectId, classId, subjectData, classData]);

  // Reset form when modal closes
  useEffect(() => {
    if (!isOpen) {
      setExamData({
        exam_type: "",
        term_id: "",
        sequence_number: 1,
        academic_year: "",
      });
      setStudentGrades([]);
      setSearchTerm("");
      setErrors({});
      setFilteredSequences([]);
    }
  }, [isOpen]);

  // Auto-calculate grade based on score (/20 system) with French/English mentions
  const calculateGrade = (score: string): string => {
    if (!score.trim()) return "";

    const numScore = parseFloat(score);
    if (isNaN(numScore)) return "";

    // French/English grading system based on /20 score
    if (numScore >= 18) return "A+/Excellent";
    else if (numScore >= 16) return "A/Très bien";
    else if (numScore >= 14) return "B+/Bien";
    else if (numScore >= 12) return "B/Assez bien";
    else if (numScore >= 10) return "C+/Passable";
    else if (numScore >= 8) return "C/Insuffisant";
    else if (numScore >= 6) return "D+/Médiocre";
    else if (numScore >= 4) return "D/Très insuffisant";
    else return "F/Nulle";
  };

  // Get color classes for grade display
  const getGradeColor = (grade: string | undefined | null) => {
    if (!grade) {
      return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }

    const splitGrade = grade.split("/")[0];
    switch (splitGrade) {
      case 'A+':
      case 'A':
        return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      case 'B+':
      case 'B':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
      case 'C+':
      case 'C':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'D+':
      case 'D':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'F':
        return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const validateScore = (score: string): { isValid: boolean; message: string } => {
    if (!score.trim()) {
      return { isValid: false, message: "Score is required" };
    }

    const numScore = parseFloat(score);
    if (isNaN(numScore)) {
      return { isValid: false, message: "Score must be a number" };
    }

    if (numScore < 0 || numScore > 20) {
      return { isValid: false, message: "Score must be between 0 and 20" };
    }

    return { isValid: true, message: "" };
  };

  const handleScoreChange = (studentId: string, score: string) => {
    console.log('🔄 handleScoreChange called:', { studentId, score });
    setStudentGrades(prev => prev.map(student => {
      console.log('🔍 Checking student:', { student_id: student.student_id, studentId, match: student.student_id === studentId });
      if (student.student_id === studentId) {
        const validation = validateScore(score);
        const calculatedGrade = calculateGrade(score);
        console.log('✅ Updating student score:', { studentId, score, calculatedGrade });
        return {
          ...student,
          score,
          grade: calculatedGrade,
          hasError: !validation.isValid,
          errorMessage: validation.message
        };
      }
      return student;
    }));
  };

  const handleCommentsChange = (studentId: string, comments: string) => {
    setStudentGrades(prev => prev.map(student =>
      student.student_id === studentId ? { ...student, comments } : student
    ));
  };

  // Fetch existing grades for the selected term, sequence, and exam type
  const fetchExistingGrades = async () => {
    // Use direct IDs if available, fallback to data objects
    const actualSubjectId = subjectId || subjectData?._id;
    const actualClassId = classId || classData?._id;

    if (!schoolId || !examData.term_id || !examData.exam_type || !actualSubjectId || !actualClassId) {
      console.log('❌ Missing required data for fetching existing grades:', {
        schoolId: !!schoolId,
        term_id: !!examData.term_id,
        exam_type: !!examData.exam_type,
        subject_id: !!actualSubjectId,
        class_id: !!actualClassId,
        subjectId_prop: !!subjectId,
        classId_prop: !!classId,
        subjectData_id: !!subjectData?._id,
        classData_id: !!classData?._id
      });
      return;
    }

    try {
      setLoadingExistingGrades(true);
      console.log('🔍 Fetching existing grades for auto-fill:', {
        schoolId,
        term_id: examData.term_id,
        sequence_number: examData.sequence_number,
        exam_type_id: examData.exam_type,
        subject_id: actualSubjectId,
        class_id: actualClassId
      });

      const response = await getGradeRecords(schoolId, {
        term_id: examData.term_id,
        sequence_number: examData.sequence_number,
        exam_type_id: examData.exam_type,
        subject_id: actualSubjectId,
        class_id: actualClassId
      });

      console.log('📋 Existing grades response:', response);
      console.log('📊 Found', response.grade_records?.length || 0, 'existing grades');

      if (response.grade_records && response.grade_records.length > 0) {
        // Create a map of existing grades by student_id
        const existingGradesMap = new Map<string, GradeRecord>();
        response.grade_records.forEach(grade => {
          console.log('📝 Mapping grade for student:', grade.student_id, 'Score:', grade.score, 'Grade:', grade.grade);
          existingGradesMap.set(grade.student_id, grade);
        });

        console.log('🗺️ Existing grades map:', Array.from(existingGradesMap.entries()));
        console.log('👥 Current student IDs in state:', studentGrades.map(s => ({ name: s.student_name, id: s.student_id })));

        // Update student grades with existing data
        setStudentGrades(prev => {
          console.log('🔄 Current studentGrades before update:', prev.map(s => ({ name: s.student_name, id: s.student_id, score: s.score })));

          const updated = prev.map(student => {
            console.log('🔍 Processing student:', student.student_name, 'ID:', student.student_id);
            const existingGrade = existingGradesMap.get(student.student_id);
            console.log('🔎 Looking for existing grade:', {
              student_id: student.student_id,
              found: !!existingGrade,
              existingGrade: existingGrade ? { id: existingGrade.student_id, score: existingGrade.score } : null
            });

            if (existingGrade) {
              console.log('✅ Auto-filling grade for student:', student.student_name, 'Score:', existingGrade.score, 'Grade:', existingGrade.grade);
              const updatedStudent = {
                ...student,
                score: existingGrade.score.toString(),
                grade: existingGrade.grade || calculateGrade(existingGrade.score.toString()),
                comments: existingGrade.comments || "",
                existingGradeId: existingGrade._id,
                hasError: false,
                errorMessage: ""
              };
              console.log('📝 Updated student data:', updatedStudent);
              return updatedStudent;
            }
            // Reset students without existing grades
            console.log('🔄 Resetting student without existing grade:', student.student_name, 'ID:', student.student_id);
            return {
              ...student,
              score: "",
              grade: "",
              comments: "",
              existingGradeId: undefined,
              hasError: false,
              errorMessage: ""
            };
          });

          console.log('🔄 Final updated studentGrades:', updated.map(s => ({ name: s.student_name, id: s.student_id, score: s.score, grade: s.grade })));
          return updated;
        });
      } else {
        // No existing grades found, reset all students
        console.log('📭 No existing grades found, resetting all students');
        setStudentGrades(prev => prev.map(student => ({
          ...student,
          score: "",
          grade: "",
          comments: "",
          existingGradeId: undefined,
          hasError: false,
          errorMessage: ""
        })));
      }

    } catch (error) {
      console.error("❌ Error fetching existing grades:", error);
    } finally {
      setLoadingExistingGrades(false);
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!examData.term_id) {
      newErrors.term_id = "Term is required";
    }

    if (!examData.term_id) {
      newErrors.sequence_number = "Please select a term first";
    } else if (filteredSequences.length === 0) {
      newErrors.sequence_number = "Selected term has no sequences available";
    } else if (!examData.sequence_number) {
      newErrors.sequence_number = "Sequence is required";
    }

    if (!examData.exam_type) {
      newErrors.exam_type = "Exam type is required";
    }

    // Check if at least one student has a valid score
    const studentsWithScores = studentGrades.filter(student => student.score.trim() !== "");
    if (studentsWithScores.length === 0) {
      newErrors.students = "At least one student must have a score";
    }

    // Check for validation errors in student scores
    const studentsWithErrors = studentGrades.filter(student => student.hasError);
    if (studentsWithErrors.length > 0) {
      newErrors.students = "Please fix score validation errors";
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Filter students with valid scores and separate new vs existing grades
      const studentsWithValidScores = studentGrades.filter(student => student.score.trim() !== "" && !student.hasError);

      const gradesData = studentsWithValidScores.map(student => {
        const isUpdate = !!student.existingGradeId;
        console.log(`💾 Preparing grade data for student ${student.student_name}:`, {
          student_id: student.student_id,
          score: student.score,
          grade: student.grade,
          operation: isUpdate ? 'UPDATE' : 'CREATE',
          existingGradeId: student.existingGradeId
        });
        return {
          student_id: student.student_id,
          score: parseFloat(student.score),
          grade: student.grade,
          comments: student.comments || "",
          existingGradeId: student.existingGradeId // Include for updates
        };
      });

      // Count operations
      const createCount = gradesData.filter(g => !g.existingGradeId).length;
      const updateCount = gradesData.filter(g => g.existingGradeId).length;
      console.log(`📊 Operation summary: ${createCount} creates, ${updateCount} updates`);

      const bulkData = {
        ...examData,
        grades: gradesData
      };

      console.log('🚀 Submitting bulk data:', bulkData);
      await onSubmit(bulkData);
      onClose();
    } catch (error) {
      console.error("Error submitting bulk grades:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Filter students based on search
  const filteredStudents = studentGrades.filter(student =>
    student.student_name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const studentsWithScores = studentGrades.filter(student => student.score.trim() !== "").length;
  const studentsWithErrors = studentGrades.filter(student => student.hasError).length;

  // Debug: Log current state
  console.log('🎯 Current render state:', {
    studentGrades_count: studentGrades.length,
    studentsWithScores,
    first_student_score: studentGrades[0]?.score,
    first_student_name: studentGrades[0]?.student_name
  });

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        exit={{ opacity: 0 }}
        className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
        onClick={onClose}
      >
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col"
          onClick={(e) => e.stopPropagation()}
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <div className="w-10 h-10 bg-teal-500 rounded-lg flex items-center justify-center">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                  Bulk Add Grades
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {classData?.name} - {subjectData?.name}
                </p>
              </div>
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="h-6 w-6" />
            </button>
          </div>

          {/* Content */}
          <div className="flex-1 overflow-y-auto custom-scrollbar">
            <div className="p-6 space-y-6">
              {/* Exam Details */}
              <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-lg border border-teal-200 dark:border-teal-700 p-6">
                <div className="flex items-center mb-4">
                  <Calendar className="w-5 h-5 text-teal-600 dark:text-teal-400 mr-2" />
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white">Exam Configuration</h4>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Term *
                  </label>
                  <select
                    value={examData.term_id}
                    onChange={(e) => {
                      console.log('🔄 Term changed to:', e.target.value);
                      setExamData(prev => ({ ...prev, term_id: e.target.value }));
                    }}
                    disabled={loadingTerms}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-600 dark:text-white ${
                      errors.term_id ? "border-red-500" : "border-gray-300 dark:border-gray-500"
                    }`}
                  >
                    <option value="">{loadingTerms ? "Loading terms..." : "Select Term"}</option>
                    {availableTerms.map((term) => (
                      <option key={term._id} value={term._id}>
                        {term.name} ({term.academic_year})
                        {term.is_current && " - Current"}
                        {term.sequences && ` (${term.sequences.length} sequences)`}
                      </option>
                    ))}
                  </select>
                  {errors.term_id && <p className="mt-1 text-sm text-red-500">{errors.term_id}</p>}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Sequence *
                  </label>
                  <select
                    value={examData.sequence_number}
                    onChange={(e) => setExamData(prev => ({ ...prev, sequence_number: parseInt(e.target.value) }))}
                    disabled={!examData.term_id || filteredSequences.length === 0}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-600 dark:text-white ${
                      errors.sequence_number ? "border-red-500" : "border-gray-300 dark:border-gray-500"
                    } ${(!examData.term_id || filteredSequences.length === 0) ? "opacity-50 cursor-not-allowed" : ""}`}
                  >
                    {!examData.term_id ? (
                      <option value="">Select a term first</option>
                    ) : filteredSequences.length === 0 ? (
                      <option value="">No sequences available</option>
                    ) : (
                      filteredSequences.map((seq) => (
                        <option key={seq.sequence_number} value={seq.sequence_number}>
                          {seq.sequence_name}
                        </option>
                      ))
                    )}
                  </select>
                  {errors.sequence_number && <p className="mt-1 text-sm text-red-500">{errors.sequence_number}</p>}
                  {!examData.term_id && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      Please select a term to see available sequences
                    </p>
                  )}
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Exam Type *
                  </label>
                  <select
                    value={examData.exam_type}
                    onChange={(e) => {
                      console.log('🔄 Exam type changed to:', e.target.value);
                      setExamData(prev => ({ ...prev, exam_type: e.target.value }));
                    }}
                    className={`w-full px-3 py-2 border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-600 dark:text-white ${
                      errors.exam_type ? "border-red-500" : "border-gray-300 dark:border-gray-500"
                    }`}
                  >
                    <option value="">Select Exam Type</option>
                    {examTypes && examTypes.length > 0 ? (
                      examTypes.map((type) => (
                        <option key={type._id} value={type._id}>
                          {type.type}
                        </option>
                      ))
                    ) : (
                      <option value="" disabled>No exam types available</option>
                    )}
                  </select>
                  {errors.exam_type && <p className="mt-1 text-sm text-red-500">{errors.exam_type}</p>}
                  {examTypes && examTypes.length === 0 && (
                    <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                      No exam types configured for this school
                    </p>
                  )}
                </div>
                </div>
              </div>

              {/* Summary */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg border border-blue-200 dark:border-blue-700 p-4">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
                  <div className="flex items-center space-x-4 text-sm">
                    <span className="flex items-center text-blue-800 dark:text-blue-200 font-medium">
                      <CheckCircle className="w-4 h-4 mr-1" />
                      {studentsWithScores} students with scores
                    </span>
                    {studentsWithErrors > 0 && (
                      <span className="flex items-center text-red-600 dark:text-red-400 font-medium">
                        <AlertCircle className="w-4 h-4 mr-1" />
                        {studentsWithErrors} validation errors
                      </span>
                    )}
                    {loadingExistingGrades && (
                      <span className="flex items-center text-teal-600 dark:text-teal-400 font-medium">
                        <div className="w-4 h-4 border-2 border-teal-600 border-t-transparent rounded-full animate-spin mr-1"></div>
                        Loading existing grades...
                      </span>
                    )}
                    <span className="text-gray-600 dark:text-gray-400">
                      Total: {filteredStudents.length} students
                    </span>
                    {/* Debug buttons */}
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🔧 Manual auto-fill trigger - Current studentGrades:', studentGrades.length);
                        console.log('🔧 Current form data:', examData);
                        fetchExistingGrades();
                      }}
                      className="px-2 py-1 text-xs bg-blue-500 text-white rounded hover:bg-blue-600 mr-2"
                    >
                      Test Auto-fill
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🧪 Testing direct state update');
                        setStudentGrades(prev => prev.map((student, index) => ({
                          ...student,
                          score: (index + 10).toString(),
                          grade: 'TEST',
                          comments: 'Test comment'
                        })));
                        setForceRender(prev => prev + 1);
                      }}
                      className="px-2 py-1 text-xs bg-green-500 text-white rounded hover:bg-green-600 mr-2"
                    >
                      Test State
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🔄 Force re-render');
                        setForceRender(prev => prev + 1);
                      }}
                      className="px-2 py-1 text-xs bg-purple-500 text-white rounded hover:bg-purple-600 mr-2"
                    >
                      Force Render
                    </button>
                    <button
                      type="button"
                      onClick={() => {
                        console.log('🧪 Testing handleScoreChange directly');
                        if (studentGrades.length > 0) {
                          const firstStudent = studentGrades[0];
                          console.log('First student:', firstStudent);
                          handleScoreChange(firstStudent.student_id, '15');
                        }
                      }}
                      className="px-2 py-1 text-xs bg-orange-500 text-white rounded hover:bg-orange-600"
                    >
                      Test Score Change
                    </button>
                  </div>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                    <input
                      type="text"
                      placeholder="Search students..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-teal-500 w-full sm:w-64"
                    />
                  </div>
                </div>
              </div>

              {errors.students && (
                <div className="p-3 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg">
                  <p className="text-sm text-red-600 dark:text-red-400">{errors.students}</p>
                </div>
              )}

              {/* Students List */}
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h4 className="text-lg font-semibold text-gray-900 dark:text-white flex items-center">
                    <Users className="w-5 h-5 mr-2 text-teal-600 dark:text-teal-400" />
                    Student Grades
                  </h4>
                  <span className="text-sm text-gray-500 dark:text-gray-400">
                    {filteredStudents.length} student{filteredStudents.length !== 1 ? 's' : ''}
                  </span>
                </div>

                {/* Table Header */}
                <div className="flex gap-4 p-3 bg-gray-100 dark:bg-gray-700 rounded-lg text-sm font-medium text-gray-700 dark:text-gray-300">
                  <div className="flex-1">Student Name</div>
                  <div className="w-40 text-center">Score & Grade</div>
                  <div className="flex-1 text-center">Comments</div>
                </div>

                <div className="space-y-2 max-h-96 overflow-y-auto custom-scrollbar">
                {filteredStudents.map((student, index) => {
                  console.log(`🎯 Rendering student ${student.student_name}: score=${student.score}, grade=${student.grade}`);
                  return (
                  <div
                    key={`${student.student_id}-${forceRender}`}
                    className={`flex gap-4 items-center p-3 border rounded-lg transition-colors ${
                      student.hasError
                        ? "border-red-300 bg-red-50 dark:border-red-700 dark:bg-red-900/20"
                        : "border-gray-200 dark:border-gray-600 bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600"
                    }`}
                  >
                    {/* Student Name */}
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <span className="flex-shrink-0 w-6 h-6 bg-teal-100 dark:bg-teal-900 text-teal-800 dark:text-teal-200 rounded-full flex items-center justify-center text-xs font-medium">
                          {index + 1}
                        </span>
                        <div className="flex flex-col">
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {student.student_name}
                          </p>
                          {student.existingGradeId && (
                            <span className="text-xs text-blue-600 dark:text-blue-400">
                              Existing grade: {student.grade} ({student.score}/20)
                            </span>
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Score Input */}
                    <div className="w-40">
                      <div className="space-y-1">
                        <input
                          type="number"
                          min="0"
                          max="20"
                          step="0.01"
                          placeholder="Score"
                          value={student.score}
                          onChange={(e) => handleScoreChange(student.student_id, e.target.value)}
                          className={`w-full px-3 py-2 text-sm border rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-600 dark:text-white ${
                            student.hasError
                              ? "border-red-500 dark:border-red-500"
                              : "border-gray-300 dark:border-gray-500"
                          }`}
                        />
                        {student.hasError && (
                          <p className="text-xs text-red-500">{student.errorMessage}</p>
                        )}
                        {student.grade && !student.hasError && (
                          <div className="text-center">
                            <span className={`inline-block px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(student.grade)}`}>
                              {student.grade}
                            </span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Comments Input */}
                    <div className="flex-1">
                      <input
                        type="text"
                        placeholder="Optional comments"
                        value={student.comments}
                        onChange={(e) => handleCommentsChange(student.student_id, e.target.value)}
                        className="w-full px-3 py-2 text-sm border border-gray-300 dark:border-gray-500 rounded-md focus:outline-none focus:ring-2 focus:ring-teal-500 dark:bg-gray-600 dark:text-white"
                      />
                    </div>
                  </div>
                  );
                })}
                </div>
              </div>
            </div>
          </div>

          {/* Footer - Fixed at bottom */}
          <div className="flex-shrink-0 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800 px-6 py-4">
            <form onSubmit={handleSubmit}>
              <div className="flex items-center justify-end space-x-3">
                <button
                  type="button"
                  onClick={onClose}
                  disabled={isSubmitting}
                  className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-700 border border-gray-300 dark:border-gray-600 rounded-md hover:bg-gray-50 dark:hover:bg-gray-600 focus:outline-none focus:ring-2 focus:ring-teal-500 disabled:opacity-50 transition-colors"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isSubmitting || studentsWithScores === 0 || studentsWithErrors > 0}
                  className="px-4 py-2 bg-purple-500 text-white rounded-md hover:bg-purple-600 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                >
                  {isSubmitting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin"></div>
                      <span>Saving...</span>
                    </>
                  ) : (
                    <>
                      <Save className="w-4 h-4" />
                      <span>Save Grades ({studentsWithScores})</span>
                    </>
                  )}
                </button>
              </div>
            </form>
          </div>
        </motion.div>
      </motion.div>
    </AnimatePresence>
  );
}
