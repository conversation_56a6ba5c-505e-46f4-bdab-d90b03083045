/**
 * Exemple d'utilisation du middleware de contrôle d'accès basé sur les fonctionnalités
 * 
 * Ce fichier montre comment protéger les endpoints existants avec le nouveau système
 * de contrôle d'accès basé sur les abonnements.
 */

const express = require('express');
const { authenticate, authorize } = require('../middleware/middleware');
const SubscriptionMiddleware = require('../middleware/subscriptionMiddleware');
const ResponseFormatter = require('../utils/responseFormatter');

const router = express.Router();

// ============================================================================
// EXEMPLES D'ENDPOINTS PROTÉGÉS PAR FONCTIONNALITÉ
// ============================================================================

/**
 * Exemple 1: Protection d'un endpoint d'import CSV (fonctionnalité Standard)
 * Route: POST /api/student/import-csv-students/:schoolId
 */
router.post('/student/import-csv-students/:schoolId',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  SubscriptionMiddleware.checkFeatureAccess('students_bulk_operations'),
  async (req, res) => {
    try {
      // Logique d'import CSV ici
      // req.feature contient les informations de la fonctionnalité
      // req.subscription contient les informations d'abonnement
      
      console.log('Feature accessed:', req.feature.name);
      console.log('Subscription level:', req.subscription.plan_type);
      
      return ResponseFormatter.success(res, {
        message: 'Import CSV autorisé',
        feature: req.feature.name,
        subscription: req.subscription.plan_type
      }, 'Accès à la fonctionnalité accordé');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de l\'import CSV', 'IMPORT_ERROR', 500);
    }
  }
);

/**
 * Exemple 2: Protection d'un endpoint de génération de cartes d'identité (fonctionnalité Premium)
 * Route: POST /api/student/generate-id-cards
 */
router.post('/student/generate-id-cards',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  SubscriptionMiddleware.checkFeatureAccess('students_id_card_generation'),
  async (req, res) => {
    try {
      // Logique de génération de cartes d'identité ici
      
      return ResponseFormatter.success(res, {
        message: 'Génération de cartes d\'identité autorisée',
        feature: req.feature.name,
        subscription: req.subscription.plan_type
      }, 'Fonctionnalité premium accessible');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de la génération', 'ID_CARD_ERROR', 500);
    }
  }
);

/**
 * Exemple 3: Protection avec vérification de plusieurs fonctionnalités
 * L'utilisateur doit avoir accès à au moins une des fonctionnalités listées
 * Route: GET /api/reports/advanced
 */
router.get('/reports/advanced',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  SubscriptionMiddleware.checkAnyFeatureAccess([
    'usage_analytics',
    'advanced_reporting',
    'grades_export'
  ]),
  async (req, res) => {
    try {
      // Logique de génération de rapports avancés
      // req.accessibleFeatures contient la liste des fonctionnalités accessibles
      
      return ResponseFormatter.success(res, {
        accessible_features: req.accessibleFeatures.map(f => f.name),
        subscription: req.subscription?.plan_type
      }, 'Accès aux rapports avancés accordé');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de la génération de rapports', 'REPORT_ERROR', 500);
    }
  }
);

/**
 * Exemple 4: Endpoint avec gestion personnalisée des erreurs d'abonnement
 * Route: POST /api/announcements/targeted
 */
router.post('/announcements/targeted',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  SubscriptionMiddleware.checkFeatureAccess('announcements_targeted_messaging'),
  async (req, res) => {
    try {
      // Logique d'envoi d'annonces ciblées
      
      return ResponseFormatter.success(res, {
        message: 'Annonce ciblée envoyée avec succès',
        target_audience: req.body.target_audience,
        feature: req.feature.name
      }, 'Envoi d\'annonce ciblée réussi');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de l\'envoi', 'ANNOUNCEMENT_ERROR', 500);
    }
  }
);

/**
 * Exemple 5: Endpoint avec options de middleware personnalisées
 * Route: PUT /api/staff/permissions/:staffId
 */
router.put('/staff/permissions/:staffId',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  SubscriptionMiddleware.checkFeatureAccess('staff_permission_management', {
    enforceForSuperAdmin: true // Force la vérification même pour les super admins
  }),
  async (req, res) => {
    try {
      // Logique de gestion des permissions du personnel
      
      return ResponseFormatter.success(res, {
        staff_id: req.params.staffId,
        updated_permissions: req.body.permissions,
        feature: req.feature.name
      }, 'Permissions mises à jour avec succès');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de la mise à jour', 'PERMISSION_ERROR', 500);
    }
  }
);

// ============================================================================
// EXEMPLES D'ENDPOINTS AVEC GESTION CONDITIONNELLE
// ============================================================================

/**
 * Exemple 6: Endpoint avec fonctionnalités conditionnelles
 * Certaines fonctionnalités sont disponibles selon l'abonnement
 * Route: GET /api/dashboard/analytics
 */
router.get('/dashboard/analytics',
  authenticate,
  authorize(['school_admin', 'admin', 'super']),
  async (req, res) => {
    try {
      // Vérification manuelle des fonctionnalités pour un contrôle plus fin
      const schoolId = SubscriptionMiddleware.extractSchoolId(req.user, req);
      
      const basicAnalytics = await SubscriptionMiddleware.validateFeatureAccess(
        'dashboard_stats_overview', 
        schoolId, 
        req.user
      );
      
      const advancedAnalytics = await SubscriptionMiddleware.validateFeatureAccess(
        'usage_analytics', 
        schoolId, 
        req.user
      );
      
      const response = {
        basic_stats: basicAnalytics.hasAccess ? {
          total_students: 150,
          total_teachers: 25,
          total_classes: 12
        } : null,
        
        advanced_analytics: advancedAnalytics.hasAccess ? {
          usage_trends: [/* données avancées */],
          performance_metrics: [/* métriques */]
        } : {
          upgrade_required: advancedAnalytics.required_level,
          current_level: advancedAnalytics.current_level
        }
      };
      
      return ResponseFormatter.success(res, response, 'Données d\'analyse récupérées');
      
    } catch (error) {
      return ResponseFormatter.error(res, 'Erreur lors de la récupération', 'ANALYTICS_ERROR', 500);
    }
  }
);

// ============================================================================
// EXEMPLES D'UTILISATION DANS LES SERVICES
// ============================================================================

/**
 * Exemple d'utilisation dans un service
 */
class StudentService {
  
  /**
   * Méthode avec vérification d'accès intégrée
   */
  static async importStudentsWithFeatureCheck(csvData, schoolId, userId) {
    try {
      // Vérification d'accès à la fonctionnalité
      const accessResult = await SubscriptionMiddleware.validateFeatureAccess(
        'students_bulk_operations',
        schoolId,
        { id: userId }
      );
      
      if (!accessResult.hasAccess) {
        throw new Error(`Accès refusé: ${accessResult.reason}`);
      }
      
      // Logique d'import si l'accès est autorisé
      console.log(`Import autorisé pour l'abonnement ${accessResult.subscription.plan_type}`);
      
      // Traitement des données CSV...
      return { success: true, imported: csvData.length };
      
    } catch (error) {
      throw error;
    }
  }
  
  /**
   * Méthode avec vérification de limites d'utilisation
   */
  static async generateIDCardsWithLimits(studentIds, schoolId) {
    try {
      const accessResult = await SubscriptionMiddleware.validateFeatureAccess(
        'students_id_card_generation',
        schoolId,
        null
      );
      
      if (!accessResult.hasAccess) {
        return {
          success: false,
          error: 'Fonctionnalité non disponible',
          upgrade_required: accessResult.required_level
        };
      }
      
      // Vérification des limites d'utilisation
      if (accessResult.limits && accessResult.limits.daily_limit) {
        const dailyUsage = accessResult.limits.daily_usage || 0;
        const dailyLimit = accessResult.limits.daily_limit;
        
        if (dailyUsage + studentIds.length > dailyLimit) {
          return {
            success: false,
            error: 'Limite quotidienne dépassée',
            daily_limit: dailyLimit,
            current_usage: dailyUsage
          };
        }
      }
      
      // Génération des cartes d'identité...
      return { success: true, generated: studentIds.length };
      
    } catch (error) {
      throw error;
    }
  }
}

module.exports = {
  router,
  StudentService
};
