"use client";

import React, { useRef, useState, useEffect } from "react";
import { X, ChevronDown } from "lucide-react";
import CustomInput from "@/components/inputs/CustomInput";
import CustomPhoneInput from "@/components/inputs/CustomPhoneInput";
import { getSchoolBy_id } from "@/app/services/SchoolServices";
import { getStudents } from "@/app/services/StudentServices";
import { StudentSchema } from "@/app/models/StudentModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import { UserSchema } from "@/app/models/UserModel";
import ActionButton from "@/components/ActionButton";
import useAuth from "@/app/hooks/useAuth";
import { getClassLevelById } from "@/app/services/ClassLevels"; // <--- Import your getClassLevelById

interface CreateInvitationModalProps {
  onClose: () => void;
  onSave: (invitationData: UserSchema) => Promise<void>;
  submitStatus: "success" | "failure" | null;
  isSubmitting: boolean;
}

const CreateInvitationModal: React.FC<CreateInvitationModalProps> = ({
  onClose,
  onSave,
  submitStatus,
  isSubmitting,
}) => {
  const [formData, setFormData] = useState<UserSchema>({
    _id: "",
    user_id: "",
    role: "parent",
    email: "",
    phone: "",
    name: "",
    school_ids: [],
    student_ids: [] as string[],
  });

  const [countryCode, setCountryCode] = useState("+237");
  const [schools, setSchools] = useState<SchoolSchema[]>([]);
  const [selectedSchoolName, setSelectedSchoolName] = useState<string | null>(null);
  const [allChildren, setAllChildren] = useState<StudentSchema[]>([]);
  const [children, setChildren] = useState<StudentSchema[]>([]);
  const [classLevelNames, setClassLevelNames] = useState<Record<string, string>>({}); // New state to store class level names
  const [searchTerm, setSearchTerm] = useState("");
  const [showChildrenDropdown, setShowChildrenDropdown] = useState(false);
  const { user } = useAuth();
  const schoolId = user?.school_ids?.[0] ?? null;
  const childrenDropdownRef = useRef<HTMLDivElement>(null);

  // Fetch schools and students
  useEffect(() => {
    if (schoolId) {
      getSchoolBy_id(schoolId as string)
        .then((data) => {
          setSchools([data]);
          setFormData((prev) => ({ ...prev, school_ids: [data._id] }));
          setSelectedSchoolName(data.name);
        })
        .catch((error) => console.error("Error fetching schools:", error));
    }

    getStudents()
      .then(async (data) => {
        const studentsWithId = data.map((student: any) => ({
          ...student,
          id: student.id || student._id,
        }));
        setAllChildren(studentsWithId);

        // Fetch unique class level names
        const uniqueClassIds = Array.from(new Set(studentsWithId.map(s => s.class_level).filter(Boolean)));
        const fetchedClassLevelNames: Record<string, string> = {};
        await Promise.all(
          uniqueClassIds.map(async (classId) => {
            try {
              const classLevel = await getClassLevelById(classId);
              fetchedClassLevelNames[classId] = classLevel.name;
            } catch (error) {
              console.error(`Error fetching class level ${classId}:`, error);
              fetchedClassLevelNames[classId] = "Unknown Class"; // Fallback
            }
          })
        );
        setClassLevelNames(fetchedClassLevelNames);
      })
      .catch((error) => console.error("Error fetching children:", error));
  }, [schoolId]);

  // Update children when school selection changes
  useEffect(() => {
    if ((formData.school_ids ?? []).length === 0) {
      setChildren([]);
    } else {
      const filtered = allChildren.filter((child) =>
        (formData.school_ids ?? []).includes(child.school_id)
      );
      setChildren(filtered);
    }
  }, [formData.school_ids, allChildren]);

  // Close dropdowns on outside click - only for children dropdown now
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        childrenDropdownRef.current &&
        !childrenDropdownRef.current.contains(event.target as Node)
      ) {
        setShowChildrenDropdown(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = (e: React.FormEvent) => {
    const fullPhone = `${countryCode}${(formData.phone ?? "").replace(/^0+/, "")}`;
    e.preventDefault();
    onSave({
      ...formData,
      phone: fullPhone,
    });
  };

  const toggleChildSelection = (childId: string, isChecked: boolean) => {
    setFormData((prev) => {
      const updatedChildren = isChecked
        ? [...(prev.student_ids ?? []), childId]
        : (prev.student_ids ?? []).filter((id) => id !== childId);
      return {
        ...prev,
        student_ids: Array.from(new Set(updatedChildren)),
      };
    });
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="h-[550px] bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-4xl mx-4 sm:mx-6 md:mx-0 relative flex flex-col md:flex-row">
        {/* Left Image */}
        <div className="hidden md:block md:w-1/2 h-full">
          <img
            src="/assets/images/parent1.png"
            alt="Parent Invite"
            className="w-full h-full object-cover rounded-lg"
            draggable={false}
          />
        </div>

        {/* Form Section */}
        <div className="w-full md:w-1/2 p-6 overflow-y-auto custom-scrollbar">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-lg font-semibold text-foreground">Send Invitation</h2>
            <button onClick={onClose} className="text-gray-500 hover:text-gray-700">
              <X size={20} />
            </button>
          </div>
          {submitStatus ? (
            <SubmissionFeedback status={submitStatus}
              message={
                submitStatus === "success"
                  ? "Invitation has been sent Successfully!"
                  : "There was an error sending this invitation. Try again and if this persist contact support!"
              } />
          ) : (
            <form onSubmit={handleSubmit}>
              <CustomInput
                label="Full Name"
                id="name"
                name="name"
                value={formData.name}
                onChange={handleChange}
                required
              />

              <CustomInput
                label="Email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleChange}
                required
              />

              <CustomPhoneInput
                label="Phone Number"
                id="phone"
                name="phone"
                value={formData.phone ?? ""}
                onChange={handleChange}
                countryCode={countryCode}
                onCountryCodeChange={(e) => setCountryCode(e.target.value)}
                required
                countryCodeName={""}
              />
              <CustomInput
                label="Address"
                id="address"
                name="address"
                value={formData.address ?? ""}
                onChange={handleChange}
                required
              />

              {/* School Display (Disabled) */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">Schools</label>
                <div
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-gray-100 dark:text-gray-400 text-gray-500 cursor-not-allowed flex items-center justify-between"
                >
                  <span>
                    {selectedSchoolName || "Loading school..."}
                  </span>
                </div>
              </div>

              {/* Children Dropdown */}
              <div className="mb-4 flex flex-col relative">
                <label className="text-sm font-semibold mb-1">Children</label>
                <div
                  onClick={() => setShowChildrenDropdown((prev) => !prev)}
                  className="w-full px-3 py-2 border rounded-md text-sm dark:bg-gray-700 bg-white dark:text-white cursor-pointer flex items-center justify-between"
                >
                  <span>
                    {(formData.student_ids ?? []).length > 0
                      ? children
                        .filter((child) =>
                          (formData.student_ids ?? []).includes(child.id as string)
                        )
                        .map((child) => child.name)
                        .join(", ")
                      : "Select children"}
                  </span>
                  <ChevronDown className="w-4 h-4 text-gray-500" />
                </div>

                {showChildrenDropdown && (
                  <div
                    ref={childrenDropdownRef}
                    className="absolute z-10 bg-white dark:bg-gray-700 mt-1 rounded-md border max-h-48 overflow-y-auto p-2 shadow-lg w-[calc(100%-2px)] left-0"
                  >
                    <input
                      type="text"
                      placeholder="Search children..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full mb-2 px-2 py-1 border rounded-md text-sm dark:bg-gray-600"
                    />
                    {children
                      .filter((child) =>
                        child.name.toLowerCase().includes(searchTerm.toLowerCase())
                      )
                      .map((child) => (
                        <label
                          key={child.id as string}
                          className="flex items-center justify-between gap-2 px-2 py-1 text-sm"
                        >
                          <div className="flex items-center gap-2">
                            <input
                              type="checkbox"
                              checked={(formData.student_ids ?? []).includes(child.id as string)}
                              onChange={(e) =>
                                toggleChildSelection(child.id as string, e.target.checked)
                              }
                            />
                            <span>{child.name}</span>
                          </div>
                          <div className="text-gray-500 dark:text-gray-400 text-xs text-right"> {/* Added text-right for alignment */}
                            {child.student_id} - {
                              child.class_level
                                ? classLevelNames[child.class_level] || "Loading..."
                                : "N/A" // Fallback if class_level is undefined
                            }
                          </div>
                        </label>
                      ))}
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-2 mt-6">
                <ActionButton
                  action="cancel"
                  label="Cancel"
                  onClick={onClose}
                  type="button"
                  disabled={isSubmitting}
                />

                <ActionButton
                  action="sendCredit"
                  type="submit"
                  isLoading={isSubmitting}
                  disabled={isSubmitting}
                  label={isSubmitting ? 'Sending...' : 'Send Invitation'}
                />

              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CreateInvitationModal;