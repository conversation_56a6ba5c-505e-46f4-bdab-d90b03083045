const ClassSchedule = require("../models/ClassSchedule");

/**
 * Fetches **all** ClassSchedule records for a given schoolId & classId,
 * populates every field the front‑end might need, then splits them into
 * three arrays by schedule_type: Normal, Exam, Event.
 *
 * @param {ObjectId} schoolId  
 * @param {ObjectId} classId   
 * @returns {Promise<{ classes: Array, exams: Array, events: Array }>}
 */
async function buildScheduleBuckets(schoolId, classId) {
  // Fetch & populate everything
  const raws = await ClassSchedule.find({
    school_id: schoolId,
    class_id: classId,
  })
    .select([
      "_id",
      "schedule_type",
      "priority",
      "status",
      "day_of_week",
      "specific_date",
      "notes",
      "period_id",
      "subject_id",
      "teacher_id",
      "exam_period_id",
      "academic_year",
      "session_year",
    ])
    .populate("period_id", "period_number start_time end_time")
    .populate("subject_id", "name subject_code")
    .populate("teacher_id", "first_name last_name")
    .populate({
      path: "exam_period_id",
      select:
        "exam_period_id name exam_type status priority start_date end_date",
      populate: { path: "term_id", select: "name term_type" },
    })
    .lean();

  // Normalize each record, now including academic & session year
  const all = raws.map((r) => ({
    id: r._id,
    type: r.schedule_type, // "Normal" | "Exam" | "Event"
    priority: r.priority,
    status: r.status, 
    dayOfWeek: r.day_of_week, 
    specificDate: r.specific_date, // Date or null
    notes: r.notes || "",

    // —— new fields for front‑end use:
    academicYear: r.academic_year, 
    sessionYear: r.session_year, 

    subject: {
      id: r.subject_id._id,
      name: r.subject_id.name,
      code: r.subject_id.subject_code,
    },

    period: {
      id: r.period_id._id,
      number: r.period_id.period_number,
      startTime: r.period_id.start_time,
      endTime: r.period_id.end_time,
    },

    teacher: r.teacher_id
      ? {
          id: r.teacher_id._id,
          name: `${r.teacher_id.first_name} ${r.teacher_id.last_name}`.trim(),
        }
      : null,

    examPeriod:
      r.schedule_type === "Exam" && r.exam_period_id
        ? {
            id: r.exam_period_id._id,
            code: r.exam_period_id.exam_period_id,
            name: r.exam_period_id.name,
            type: r.exam_period_id.exam_type,
            status: r.exam_period_id.status,
            priority: r.exam_period_id.priority,
            startDate: r.exam_period_id.start_date,
            endDate: r.exam_period_id.end_date,
            term: r.exam_period_id.term_id.name,
          }
        : null,
  }));

  // 3️⃣ Bucket by type
  return {
    classes: all.filter((s) => s.type === "Normal"),
    exams: all.filter((s) => s.type === "Exam"),
    events: all.filter((s) => s.type === "Event"),
  };
}

module.exports = { buildScheduleBuckets };
