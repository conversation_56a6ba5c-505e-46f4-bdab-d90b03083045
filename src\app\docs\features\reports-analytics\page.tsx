"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  BarChart3,
  TrendingUp,
  FileText,
  CheckCircle,
  ArrowRight,
  Download,
  Calendar,
  Settings,
  Eye,
  Edit,
  Plus,
  Filter,
  Search,
  PieChart,
  LineChart,
  Activity
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function ReportsAnalyticsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'reports', name: 'Reports', icon: FileText },
    { id: 'analytics', name: 'Analytics', icon: BarChart3 },
    { id: 'dashboards', name: 'Dashboards', icon: Activity },
    { id: 'export', name: 'Export', icon: Download },
    { id: 'scheduling', name: 'Scheduling', icon: Calendar },
  ];

  const features = [
    {
      title: 'Custom Report Generation',
      description: 'Create comprehensive reports with customizable parameters and data visualization.',
      icon: FileText,
      benefits: [
        'Drag-and-drop report builder',
        'Custom data filters and criteria',
        'Multiple chart types and visualizations',
        'Report templates and presets',
        'Real-time data integration',
        'Scheduled report generation'
      ]
    },
    {
      title: 'Performance Analytics',
      description: 'Advanced analytics for student performance, attendance, and academic trends.',
      icon: TrendingUp,
      benefits: [
        'Student performance tracking',
        'Class and subject analytics',
        'Attendance trend analysis',
        'Grade distribution analysis',
        'Progress monitoring',
        'Predictive analytics'
      ]
    },
    {
      title: 'Interactive Dashboards',
      description: 'Real-time dashboards with customizable widgets and live data updates.',
      icon: Activity,
      benefits: [
        'Customizable dashboard layouts',
        'Real-time data updates',
        'Interactive charts and graphs',
        'Drill-down capabilities',
        'Mobile-responsive design',
        'Role-based dashboards'
      ]
    },
    {
      title: 'Data Export & Sharing',
      description: 'Comprehensive export capabilities with multiple formats and sharing options.',
      icon: Download,
      benefits: [
        'Multiple export formats (PDF, Excel, CSV)',
        'Bulk data export',
        'Automated report delivery',
        'Secure file sharing',
        'Export scheduling',
        'Data backup and archiving'
      ]
    },
    {
      title: 'Advanced Filtering',
      description: 'Powerful filtering and search capabilities for detailed data analysis.',
      icon: Filter,
      benefits: [
        'Multi-dimensional filtering',
        'Advanced search functionality',
        'Date range filtering',
        'Custom filter combinations',
        'Saved filter presets',
        'Filter performance optimization'
      ]
    },
    {
      title: 'Scheduled Reporting',
      description: 'Automated report generation and delivery with customizable schedules.',
      icon: Calendar,
      benefits: [
        'Automated report generation',
        'Custom delivery schedules',
        'Email and notification delivery',
        'Report distribution lists',
        'Delivery confirmation tracking',
        'Schedule management tools'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Create Report',
      description: 'Generate a new custom report',
      icon: Plus,
      action: () => console.log('Create report')
    },
    {
      title: 'View Analytics',
      description: 'Access performance analytics',
      icon: BarChart3,
      action: () => console.log('View analytics')
    },
    {
      title: 'Export Data',
      description: 'Export data in various formats',
      icon: Download,
      action: () => console.log('Export data')
    },
    {
      title: 'Schedule Reports',
      description: 'Set up automated report delivery',
      icon: Calendar,
      action: () => console.log('Schedule reports')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Features" />
      
      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-4 sm:mb-6">
              <div className="p-3 sm:p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <BarChart3 className="w-8 h-8 sm:w-12 sm:h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
              Reports & Analytics
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4">
              Advanced reporting and analytics for data-driven decision making. 
              Transform your school data into actionable insights with powerful reporting tools.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-6 sm:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-8 sm:space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 sm:p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3 sm:mr-4">
                            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-1 sm:space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-teal-500 mr-2 flex-shrink-0" />
                              <span className="line-clamp-2">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-4 sm:p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-2 sm:mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-2 sm:mr-3">
                            <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 text-sm sm:text-base">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Report Generation
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Report Types
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Student performance reports</li>
                      <li>• Attendance reports</li>
                      <li>• Financial reports</li>
                      <li>• Academic analytics</li>
                      <li>• Custom reports</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Report Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Drag-and-drop builder</li>
                      <li>• Custom data filters</li>
                      <li>• Multiple chart types</li>
                      <li>• Report templates</li>
                      <li>• Scheduled generation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Performance Analytics
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Analytics Types
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Student performance tracking</li>
                      <li>• Class comparison analytics</li>
                      <li>• Attendance trend analysis</li>
                      <li>• Grade distribution analysis</li>
                      <li>• Predictive analytics</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Analytics Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Real-time data processing</li>
                      <li>• Advanced filtering</li>
                      <li>• Trend identification</li>
                      <li>• Performance benchmarks</li>
                      <li>• Data visualization</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'dashboards' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Interactive Dashboards
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Dashboard Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Customizable layouts</li>
                      <li>• Real-time data updates</li>
                      <li>• Interactive charts</li>
                      <li>• Drill-down capabilities</li>
                      <li>• Mobile-responsive design</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Dashboard Types
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Executive dashboards</li>
                      <li>• Teacher dashboards</li>
                      <li>• Administrative dashboards</li>
                      <li>• Role-based dashboards</li>
                      <li>• Custom dashboards</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'export' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Data Export & Sharing
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Export Formats
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• PDF reports</li>
                      <li>• Excel spreadsheets</li>
                      <li>• CSV data files</li>
                      <li>• JSON data export</li>
                      <li>• Image exports</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Export Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Bulk data export</li>
                      <li>• Automated delivery</li>
                      <li>• Secure file sharing</li>
                      <li>• Export scheduling</li>
                      <li>• Data archiving</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'scheduling' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Scheduled Reporting
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Scheduling Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Automated report generation</li>
                      <li>• Custom delivery schedules</li>
                      <li>• Email notifications</li>
                      <li>• Report distribution lists</li>
                      <li>• Delivery confirmation</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Schedule Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Schedule creation and editing</li>
                      <li>• Recurring report schedules</li>
                      <li>• Schedule monitoring</li>
                      <li>• Schedule templates</li>
                      <li>• Schedule analytics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 dark:bg-teal-600 text-white py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6">
            Ready to Transform Your Data into Insights?
          </h2>
          <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-teal-100 dark:text-teal-100 max-w-3xl mx-auto">
            Start using these powerful reporting and analytics tools to make data-driven decisions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 dark:bg-white dark:text-teal-600 dark:hover:bg-gray-100 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-teal-600 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 