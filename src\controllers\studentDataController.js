const mongoose = require("mongoose");
const Student = require("../models/Student");
const School = require("../models/School");
const ClassModel = require("../models/Class");
const Grade = require("../models/Grade");
const Attendance = require("../models/Attendance");
const Discipline = require("../models/Discipline");
const Announcement = require("../models/Announcement");
const SchoolResource = require("../models/SchoolResources");
const Fee = require("../models/Fees");
const FeePayment = require("../models/FeePayment");
const { buildScheduleBuckets } = require("../utils/scheduleBuckets");

/**
 * GET /student-data/:studentId
 * Aggregates a student’s dashboard data by student_id or _id.
 */
async function getStudentData(req, res) {
  try {
    const { studentId } = req.params;

    // Build filter: match either student_id field or MongoDB _id
    const filter = { $or: [{ student_id: studentId }] };
    if (mongoose.isValidObjectId(studentId)) {
      filter.$or.push({ _id: studentId });
    }

    // Fetch student document and populate class references
    const student = await Student.findOne(filter)
      .populate("class_id", "name class_code")
      .populate("class_level", "name")
      .lean();

    if (!student) {
      return res.status(404).json({ message: "Student not found" });
    }

    if (!student.school_id) {
      return res
        .status(404)
        .json({ message: "Student has no associated school" });
    }
    const school = await School.findById(
      student.school_id,
      "name address"
    ).lean();
    if (!school) {
      return res.status(404).json({ message: "School not found" });
    }

    const classInfo =
      student.class_id && student.class_id._id
        ? await ClassModel.findById(student.class_id._id).lean()
        : null;

    // ===== Grades =====
    const grades = await Grade.find({ student_id: student._id })
      .populate("subject_id", "name coefficient") 
      .populate("exam_type", "type")
      .lean();

    // Map each grade to include top-level subjectName, subjectCode, and coefficient
    const gradesWithCoefficient = grades.map((grade) => ({
      ...grade,
      subjectName: grade.subject_id.name,
      subjectCode: grade.subject_id.subject_id || grade.subject_id._id,
      coefficient: grade.subject_id.coefficient,
    }));

    // ===== Attendance =====
    const attendance = await Attendance.find({ student_id: student._id })
      .populate("schedule_id", "name")
      .lean();

    // ===== Discipline =====
    const discipline = await Discipline.find({
      student_id: student._id,
    }).lean();

    // ===== Announcements =====
    const announcements = await Announcement.find({
      school_id: school._id,
      is_published: true,
    })
      .sort({ published_at: -1 })
      .populate("author_id", "first_name last_name")
      .lean();

    // ===== Resources =====
    const resources = await SchoolResource.find({
      school_id: school._id,
    }).lean();

    // ===== Schedule Buckets =====
    let schedule = { classes: [], exams: [], events: [] };
    if (student.class_id && student.class_id._id) {
      schedule = await buildScheduleBuckets(
        student.school_id,
        student.class_id._id
      );
    }

    // ===== Fees & Payments =====
    const feeDefinitions = await Fee.find({ school_id: school._id }).lean();
    const feePayments = await FeePayment.find({
      student_id: student._id,
    }).lean();

    return res.json({
      student,
      school,
      class: classInfo,
      grades: gradesWithCoefficient, 
      attendance,
      discipline,
      announcements,
      resources,
      schedule,
      feeDefinitions,
      feePayments,
    });
  } catch (err) {
    console.error("Error in getStudentData:", err);
    return res.status(500).json({ message: "Internal server error" });
  }
}

module.exports = { getStudentData };
