const FeatureRegistryService = require('../services/FeatureRegistryService');
const ResponseFormatter = require('../utils/responseFormatter');

/**
 * Controller for feature registry operations
 */
class FeatureRegistryController {
  
  /**
   * Get all features for a subscription level
   * GET /api/features/subscription/:level
   */
  static async getFeaturesBySubscriptionLevel(req, res) {
    try {
      const { level } = req.params;
      const { module, useCache = 'true' } = req.query;
      
      // Validate subscription level
      const validLevels = ['basic', 'standard', 'premium', 'enterprise'];
      if (!validLevels.includes(level)) {
        return ResponseFormatter.validationError(res, {
          field: 'level',
          message: 'Invalid subscription level',
          valid_values: validLevels
        });
      }
      
      let features;
      
      if (module) {
        features = await FeatureRegistryService.getFeaturesByModule(
          module, 
          level, 
          useCache === 'true'
        );
      } else {
        features = await FeatureRegistryService.getFeaturesBySubscriptionLevel(
          level, 
          useCache === 'true'
        );
      }
      
      return ResponseFormatter.success(res, features, `Features retrieved for ${level} subscription`, 200, {
        subscription_level: level,
        module: module || 'all',
        count: features.length
      });
      
    } catch (error) {
      console.error('Error getting features by subscription level:', error);
      return ResponseFormatter.error(res, 'Failed to retrieve features', 'FEATURE_RETRIEVAL_ERROR', 500);
    }
  }
  
  /**
   * Get features grouped by module
   * GET /api/features/grouped
   */
  static async getFeaturesByModuleGrouped(req, res) {
    try {
      const { level = 'basic', useCache = 'true' } = req.query;
      
      const groupedFeatures = await FeatureRegistryService.getFeaturesByModuleGrouped(
        level, 
        useCache === 'true'
      );
      
      return ResponseFormatter.success(res, groupedFeatures, 'Features grouped by module', 200, {
        subscription_level: level,
        modules: Object.keys(groupedFeatures),
        total_features: Object.values(groupedFeatures).reduce((sum, features) => sum + features.length, 0)
      });
      
    } catch (error) {
      console.error('Error getting grouped features:', error);
      return ResponseFormatter.error(res, 'Failed to retrieve grouped features', 'FEATURE_GROUPING_ERROR', 500);
    }
  }
  
  /**
   * Check feature access for a school
   * GET /api/features/check/:featureId/school/:schoolId
   */
  static async checkSchoolFeatureAccess(req, res) {
    try {
      const { featureId, schoolId } = req.params;
      const { useCache = 'true' } = req.query;
      
      const accessResult = await FeatureRegistryService.checkSchoolFeatureAccess(
        featureId, 
        schoolId, 
        useCache === 'true'
      );
      
      if (!accessResult.hasAccess) {
        return ResponseFormatter.subscriptionRequired(
          res,
          featureId,
          accessResult.required_level || 'standard',
          accessResult.subscription_level || 'basic'
        );
      }
      
      return ResponseFormatter.success(res, accessResult, 'Feature access granted', 200, {
        feature_id: featureId,
        school_id: schoolId
      });
      
    } catch (error) {
      console.error('Error checking feature access:', error);
      return ResponseFormatter.error(res, 'Failed to check feature access', 'FEATURE_ACCESS_CHECK_ERROR', 500);
    }
  }
  
  /**
   * Get feature details by ID
   * GET /api/features/:featureId
   */
  static async getFeatureById(req, res) {
    try {
      const { featureId } = req.params;
      const { useCache = 'true' } = req.query;
      
      const feature = await FeatureRegistryService.getFeatureById(
        featureId, 
        useCache === 'true'
      );
      
      if (!feature) {
        return ResponseFormatter.featureNotFound(res, featureId);
      }
      
      return ResponseFormatter.success(res, feature, 'Feature details retrieved', 200, {
        feature_id: featureId
      });
      
    } catch (error) {
      console.error('Error getting feature by ID:', error);
      return ResponseFormatter.error(res, 'Failed to retrieve feature details', 'FEATURE_DETAIL_ERROR', 500);
    }
  }
  
  /**
   * Get feature statistics
   * GET /api/features/statistics
   */
  static async getFeatureStatistics(req, res) {
    try {
      const { level } = req.query;
      
      const statistics = await FeatureRegistryService.getFeatureStatistics(level);
      
      return ResponseFormatter.success(res, statistics, 'Feature statistics retrieved', 200, {
        subscription_level: level || 'all'
      });
      
    } catch (error) {
      console.error('Error getting feature statistics:', error);
      return ResponseFormatter.error(res, 'Failed to retrieve feature statistics', 'FEATURE_STATS_ERROR', 500);
    }
  }
  
  /**
   * Create or update a feature (Super Admin only)
   * POST /api/features
   * PUT /api/features/:featureId
   */
  static async createOrUpdateFeature(req, res) {
    try {
      const { featureId } = req.params;
      const featureData = req.body;
      
      // Add user information
      featureData.last_modified_by = req.user.id;
      if (!featureId) {
        featureData.created_by = req.user.id;
      } else {
        featureData.feature_id = featureId;
      }
      
      // Validate required fields
      const requiredFields = ['feature_id', 'name', 'description', 'module', 'category', 'subscription_level'];
      const missingFields = requiredFields.filter(field => !featureData[field]);
      
      if (missingFields.length > 0) {
        return ResponseFormatter.validationError(res, {
          missing_fields: missingFields,
          message: 'Required fields are missing'
        });
      }
      
      const feature = await FeatureRegistryService.createOrUpdateFeature(featureData);
      
      const message = featureId ? 'Feature updated successfully' : 'Feature created successfully';
      const statusCode = featureId ? 200 : 201;
      
      return ResponseFormatter.success(res, feature, message, statusCode, {
        feature_id: feature.feature_id,
        operation: featureId ? 'update' : 'create'
      });
      
    } catch (error) {
      console.error('Error creating/updating feature:', error);
      return ResponseFormatter.error(res, 'Failed to save feature', 'FEATURE_SAVE_ERROR', 500);
    }
  }
  
  /**
   * Toggle feature status (Super Admin only)
   * PATCH /api/features/:featureId/toggle
   */
  static async toggleFeature(req, res) {
    try {
      const { featureId } = req.params;
      const { enabled } = req.body;
      
      if (typeof enabled !== 'boolean') {
        return ResponseFormatter.validationError(res, {
          field: 'enabled',
          message: 'Enabled field must be a boolean value'
        });
      }
      
      const feature = await FeatureRegistryService.toggleFeature(
        featureId, 
        enabled, 
        req.user.id
      );
      
      const message = `Feature ${enabled ? 'enabled' : 'disabled'} successfully`;
      
      return ResponseFormatter.success(res, feature, message, 200, {
        feature_id: featureId,
        status: feature.status
      });
      
    } catch (error) {
      if (error.message === 'Feature not found') {
        return ResponseFormatter.featureNotFound(res, req.params.featureId);
      }
      
      console.error('Error toggling feature:', error);
      return ResponseFormatter.error(res, 'Failed to toggle feature', 'FEATURE_TOGGLE_ERROR', 500);
    }
  }
  
  /**
   * Clear feature cache (Super Admin only)
   * DELETE /api/features/cache
   */
  static async clearCache(req, res) {
    try {
      const { featureId, module } = req.query;
      
      if (featureId) {
        FeatureRegistryService.clearFeatureCache(featureId);
      } else if (module) {
        FeatureRegistryService.clearModuleCache(module);
      } else {
        FeatureRegistryService.clearAllCache();
      }
      
      return ResponseFormatter.success(res, null, 'Cache cleared successfully', 200, {
        cleared: featureId ? 'feature' : module ? 'module' : 'all'
      });
      
    } catch (error) {
      console.error('Error clearing cache:', error);
      return ResponseFormatter.error(res, 'Failed to clear cache', 'CACHE_CLEAR_ERROR', 500);
    }
  }
  
  /**
   * Get cache statistics (Super Admin only)
   * GET /api/features/cache/stats
   */
  static async getCacheStats(req, res) {
    try {
      const stats = FeatureRegistryService.getCacheStats();
      
      return ResponseFormatter.success(res, stats, 'Cache statistics retrieved', 200);
      
    } catch (error) {
      console.error('Error getting cache stats:', error);
      return ResponseFormatter.error(res, 'Failed to retrieve cache statistics', 'CACHE_STATS_ERROR', 500);
    }
  }
}

module.exports = FeatureRegistryController;
