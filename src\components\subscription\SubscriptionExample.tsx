"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Upload, 
  CreditCard, 
  FileText, 
  BarChart3, 
  Settings,
  Crown,
  Zap
} from 'lucide-react';
import FeatureGate from './FeatureGate';
import FeatureButton from './FeatureButton';
import SubscriptionStatus from './SubscriptionStatus';
import { useTranslation } from '@/hooks/useTranslation';
import { useSubscription } from '@/hooks/useSubscription';

/**
 * Composant d'exemple montrant l'utilisation des fonctionnalités de subscription
 * Ce composant peut être utilisé comme référence pour intégrer les fonctionnalités
 * de contrôle d'accès basé sur l'abonnement dans d'autres parties de l'application
 */
const SubscriptionExample: React.FC = () => {
  const { t } = useTranslation();
  const { subscription, hasFeatureAccess } = useSubscription();
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);

  // Exemples de fonctions d'action
  const handleImportCSV = () => {
    console.log('Import CSV action triggered');
    // Logique d'import CSV ici
  };

  const handleGenerateIDCards = () => {
    console.log('Generate ID Cards action triggered');
    // Logique de génération de cartes d'identité ici
  };

  const handleAdvancedReporting = () => {
    console.log('Advanced Reporting action triggered');
    // Logique de rapports avancés ici
  };

  const handleBulkOperations = () => {
    console.log('Bulk Operations action triggered');
    // Logique d'opérations en lot ici
  };

  return (
    <div className="max-w-6xl mx-auto p-6 space-y-8">
      {/* Header avec statut d'abonnement */}
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100 mb-4">
          {t('subscription.dashboard.title', 'Gestion des Abonnements')}
        </h1>
        <p className="text-gray-600 dark:text-gray-400 mb-6">
          Exemple d'intégration des fonctionnalités de contrôle d'accès basé sur l'abonnement
        </p>
        
        {/* Statut d'abonnement compact */}
        <SubscriptionStatus compact={true} className="max-w-md mx-auto" />
      </div>

      {/* Section des fonctionnalités de base */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <Users className="w-6 h-6 mr-2 text-blue-500" />
          Fonctionnalités de Base (Tous les plans)
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Gestion des étudiants - Toujours disponible */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              {t('subscription.features.students_crud_operations', 'Gestion des étudiants')}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Ajouter, modifier et supprimer des étudiants
            </p>
            <button className="w-full px-4 py-2 bg-blue-500 text-white rounded-lg hover:bg-blue-600 transition-colors">
              Gérer les étudiants
            </button>
          </div>

          {/* Gestion des classes - Toujours disponible */}
          <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
              {t('subscription.features.classes_crud_operations', 'Gestion des classes')}
            </h3>
            <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
              Créer et organiser les classes
            </p>
            <button className="w-full px-4 py-2 bg-green-500 text-white rounded-lg hover:bg-green-600 transition-colors">
              Gérer les classes
            </button>
          </div>
        </div>
      </motion.section>

      {/* Section des fonctionnalités Standard */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <Zap className="w-6 h-6 mr-2 text-blue-500" />
          Fonctionnalités Standard
          {!hasFeatureAccess('students_bulk_operations') && (
            <span className="ml-2 px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded-full">
              Mise à niveau requise
            </span>
          )}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Import CSV - Fonctionnalité Standard */}
          <FeatureGate featureId="students_bulk_operations">
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {t('subscription.features.students_bulk_operations', 'Opérations en lot')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Importer des étudiants via CSV
              </p>
              <FeatureButton
                featureId="students_bulk_operations"
                onClick={handleImportCSV}
                className="w-full px-4 py-2 bg-purple-500 text-white rounded-lg hover:bg-purple-600 transition-colors flex items-center justify-center"
              >
                <Upload className="w-4 h-4 mr-2" />
                Importer CSV
              </FeatureButton>
            </div>
          </FeatureGate>

          {/* Analyses avancées - Fonctionnalité Standard */}
          <FeatureGate featureId="usage_analytics">
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {t('subscription.features.usage_analytics', 'Analyses d\'utilisation')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Rapports et statistiques avancés
              </p>
              <FeatureButton
                featureId="usage_analytics"
                onClick={handleAdvancedReporting}
                className="w-full px-4 py-2 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 transition-colors flex items-center justify-center"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Voir les analyses
              </FeatureButton>
            </div>
          </FeatureGate>
        </div>
      </motion.section>

      {/* Section des fonctionnalités Premium */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.2 }}
        className="bg-white dark:bg-gray-800 rounded-xl shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <Crown className="w-6 h-6 mr-2 text-yellow-500" />
          Fonctionnalités Premium
          {!hasFeatureAccess('students_id_card_generation') && (
            <span className="ml-2 px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded-full">
              Premium requis
            </span>
          )}
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Génération de cartes d'identité - Fonctionnalité Premium */}
          <FeatureGate featureId="students_id_card_generation">
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {t('subscription.features.students_id_card_generation', 'Cartes d\'identité')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Générer des cartes d'identité personnalisées
              </p>
              <FeatureButton
                featureId="students_id_card_generation"
                onClick={handleGenerateIDCards}
                disabled={selectedStudents.length === 0}
                className="w-full px-4 py-2 bg-yellow-500 text-white rounded-lg hover:bg-yellow-600 transition-colors flex items-center justify-center disabled:opacity-50"
              >
                <CreditCard className="w-4 h-4 mr-2" />
                Générer cartes ID
              </FeatureButton>
            </div>
          </FeatureGate>

          {/* Génération de bulletins - Fonctionnalité Premium */}
          <FeatureGate featureId="students_report_card_generation">
            <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
              <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                {t('subscription.features.students_report_card_generation', 'Bulletins scolaires')}
              </h3>
              <p className="text-sm text-gray-600 dark:text-gray-400 mb-3">
                Créer des bulletins personnalisés
              </p>
              <FeatureButton
                featureId="students_report_card_generation"
                onClick={() => console.log('Generate report cards')}
                className="w-full px-4 py-2 bg-orange-500 text-white rounded-lg hover:bg-orange-600 transition-colors flex items-center justify-center"
              >
                <FileText className="w-4 h-4 mr-2" />
                Générer bulletins
              </FeatureButton>
            </div>
          </FeatureGate>
        </div>
      </motion.section>

      {/* Statut d'abonnement détaillé */}
      <motion.section
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.3 }}
      >
        <SubscriptionStatus 
          showDetails={true}
          showUpgradeButton={true}
          className="bg-white dark:bg-gray-800"
        />
      </motion.section>

      {/* Section d'informations */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-blue-50 dark:bg-blue-900/20 rounded-xl p-6"
      >
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-3">
          Comment utiliser ces composants
        </h3>
        <ul className="space-y-2 text-sm text-blue-800 dark:text-blue-200">
          <li>• <strong>FeatureGate:</strong> Masque automatiquement le contenu si la fonctionnalité n'est pas accessible</li>
          <li>• <strong>FeatureButton:</strong> Bouton intelligent qui gère les états d'accès et les prompts de mise à niveau</li>
          <li>• <strong>SubscriptionStatus:</strong> Affiche le statut actuel de l'abonnement avec options de mise à niveau</li>
          <li>• <strong>useSubscription:</strong> Hook pour vérifier l'accès aux fonctionnalités et gérer les abonnements</li>
        </ul>
      </motion.div>
    </div>
  );
};

export default SubscriptionExample;
