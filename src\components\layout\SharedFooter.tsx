"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { 
  Mail, 
  ArrowRight,
  Heart,
  Shield,
  Zap,
  Users,
  BookOpen
} from 'lucide-react';
import Logo from '@/components/widgets/Logo';

export default function SharedFooter() {
  const router = useRouter();
  const [email, setEmail] = useState('');

  const handleEmailSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // Handle newsletter subscription
    console.log('Newsletter subscription:', email);
    setEmail('');
  };

  const features = [
    {
      icon: <Shield className="w-5 h-5" />,
      title: "Enterprise Security",
      description: "Bank-level encryption and compliance"
    },
    {
      icon: <Zap className="w-5 h-5" />,
      title: "Lightning Fast",
      description: "Optimized for performance"
    },
    {
      icon: <Users className="w-5 h-5" />,
      title: "Dedicated Support",
      description: "24/7 expert assistance"
    },
    {
      icon: <BookOpen className="w-5 h-5" />,
      title: "Comprehensive",
      description: "All-in-one school management"
    }
  ];

  return (
    <footer className="bg-gray-900 dark:bg-gray-900 text-white">
      {/* Main Footer Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12 lg:py-16">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
          {/* Brand Section */}
          <div>
            <Logo />
            <p className="mt-4 text-gray-400 dark:text-gray-400 text-sm leading-relaxed">
              Transforming education through innovative technology. Scholarify provides comprehensive 
              school management solutions that empower educators and administrators worldwide.
            </p>
            
            {/* Newsletter Subscription */}
            <div className="mt-6">
              <h4 className="text-sm font-semibold text-white mb-3">Stay Updated</h4>
              <form onSubmit={handleEmailSubmit} className="flex flex-col sm:flex-row gap-2">
                <input
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  placeholder="Enter your email"
                  className="flex-1 px-3 py-2 bg-gray-800 dark:bg-gray-800 border border-gray-700 dark:border-gray-700 rounded-lg sm:rounded-l-lg sm:rounded-r-none text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-teal-500 dark:focus:ring-teal-400"
                />
                <button
                  type="submit"
                  className="px-4 py-2 bg-teal-600 hover:bg-teal-700 dark:bg-teal-600 dark:hover:bg-teal-700 rounded-lg sm:rounded-l-none sm:rounded-r-lg transition-colors"
                >
                  <ArrowRight className="w-4 h-4" />
                </button>
              </form>
            </div>
          </div>

          {/* Features Grid */}
          <div className="grid grid-cols-2 gap-4 lg:gap-6">
            {features.map((feature, index) => (
              <div key={index} className="text-center">
                <div className="flex justify-center mb-2">
                  <div className="p-2 bg-teal-600/20 dark:bg-teal-600/20 rounded-lg">
                    <div className="text-teal-400 dark:text-teal-400">
                      {feature.icon}
                    </div>
                  </div>
                </div>
                <h5 className="text-sm font-semibold text-white mb-1">
                  {feature.title}
                </h5>
                <p className="text-xs text-gray-400 dark:text-gray-400">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="border-t border-gray-800 dark:border-gray-800">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 lg:py-6">
          <div className="flex flex-col sm:flex-row justify-between items-center space-y-2 sm:space-y-0">
            <div className="flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-6 text-sm text-gray-400 dark:text-gray-400">
              <span>© 2024 Scholarify. All rights reserved.</span>
              <span className="hidden sm:inline">•</span>
              <span>Made with <Heart className="w-4 h-4 inline text-red-500" /> for education</span>
            </div>
            
            <div className="flex flex-col sm:flex-row items-center space-y-1 sm:space-y-0 sm:space-x-6 text-sm text-gray-400 dark:text-gray-400">
              <div className="flex items-center space-x-2">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                <span>System Status: Operational</span>
              </div>
              <span className="hidden sm:inline">•</span>
              <span>v2.1.0</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
} 