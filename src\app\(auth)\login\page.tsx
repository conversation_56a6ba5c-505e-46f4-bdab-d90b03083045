"use client";
import { FormEvent, useEffect, useState } from "react";
import AppLogo from "@/components/AppLogo";
import Input from "@/components/input";
import FormHeading from "@/components/FormHeading";
import { Loader2, ArrowLeft, Home } from "lucide-react";
import LoginErrorCard, { useLoginError } from "@/components/auth/LoginErrorCard";
import { ErrorHandlingService } from "@/app/services/ErrorHandlingService";
import '@/styles/formStyle.css';
import useAuth from "@/app/hooks/useAuth";
import { redirect, useRouter } from "next/navigation";
import CircularLoader from "@/components/widgets/CircularLoader";
import { getCurrentUser } from "@/app/services/UserServices";

export default function Login() {
    const [isLoading, setIsLoading] = useState(false);
    const [isForgotPasswordLoading, setIsForgotPasswordLoading] = useState(false);
    const [isRedirecting, setIsRedirecting] = useState(false);
    const { login, isAuthenticated, loading, redirectAfterLogin, user } = useAuth();
    const router = useRouter();

    // State pour les champs de formulaire
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [rememberMe, setRememberMe] = useState(false);
    const [hasRedirected, setHasRedirected] = useState(false);

    // State pour la validation
    const [emailError, setEmailError] = useState("");
    const [passwordError, setPasswordError] = useState("");

    // Gestion des erreurs avec le nouveau système
    const { errorDetails, isVisible: isErrorVisible, showError, hideError, clearError } = useLoginError();

    // Fonctions de validation
    const validateEmail = (email: string): string => {
        if (!email) return "L'email est requis";
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) return "Format d'email invalide";
        return "";
    };

    const validatePassword = (password: string): string => {
        if (!password) return "Le mot de passe est requis";
        if (password.length < 6) return "Le mot de passe doit contenir au moins 6 caractères";
        return "";
    };

    const validateForm = (): boolean => {
        const emailErr = validateEmail(email);
        const passwordErr = validatePassword(password);

        setEmailError(emailErr);
        setPasswordError(passwordErr);

        return !emailErr && !passwordErr;
    };

    // Fonction pour obtenir la redirection par défaut selon le rôle
    const getRoleBasedRedirect = (userRole: string): string => {
        const roleRedirectMap: Record<string, string> = {
            super: '/super-admin/dashboard',
            admin: '/school-admin/dashboard',
            teacher: '/teacher-dashboard',
            parent: '/parent-dashboard',
            dean_of_studies: '/school-admin/dashboard',
            bursar: '/school-admin/dashboard',
            school_admin: '/school-admin/dashboard',
        };

        return roleRedirectMap[userRole] || '/';
    };

    const getDefaultRedirectByRole = async () => {
        const user = await getCurrentUser();
        if (!user) return '/login';

        console.log("User role for redirect:", user.role);
        return getRoleBasedRedirect(user.role);
    };

    useEffect(() => {
        // SEULEMENT rediriger si l'utilisateur est déjà authentifié ET qu'on n'a pas encore redirigé
        if (!loading && isAuthenticated && !hasRedirected) {
            const handleRedirect = async () => {
                try {
                    setHasRedirected(true); // Marquer comme redirigé pour éviter les doublons
                    setIsRedirecting(true); // Afficher l'état de redirection

                    // Use user from context instead of making another API call
                    const redirectTo = redirectAfterLogin || getRoleBasedRedirect(user?.role || '');
                    console.log("Auto-redirecting to:", redirectTo);
                    router.replace(redirectTo); // Use replace for faster redirect
                } catch (error) {
                    console.error("Error during auto-redirect:", error);
                    setIsRedirecting(false);
                    // Fallback en cas d'erreur
                    router.replace('/dashboard');
                }
            };

            handleRedirect();
        }
    }, [isAuthenticated, loading, redirectAfterLogin, router, hasRedirected, user]);

    if (loading || isRedirecting) {
        return (
            <div className="flex flex-col justify-center items-center h-screen w-full absolute top-0 left-0 z-50 bg-white dark:bg-gray-900">
                <CircularLoader size={40} color="teal-500" />
                <p className="mt-4 text-gray-600 dark:text-gray-300">
                    {loading ? "Chargement..." : "Redirection en cours..."}
                </p>
            </div>
        );
    }
    // Redirect logic is handled in useEffect above, no need for sync redirect here

    const handleSubmit = async (e: FormEvent) => {
        e.preventDefault();

        // Valider le formulaire avant de continuer
        if (!validateForm()) {
            return;
        }

        setIsLoading(true);

        // Réinitialiser les erreurs
        clearError();

        try {
            await login(email, password, rememberMe);

            // User is now logged in, redirect will be handled by useEffect
            // No need for manual redirect here since useEffect handles it
        } catch (error: any) {
            console.error("Login error:", error);

            // Utiliser le nouveau système de gestion d'erreurs
            if (error.errorDetails) {
                showError(error.errorDetails);
            } else {
                // Classifier l'erreur nous-mêmes
                const errorDetails = ErrorHandlingService.classifyLoginError(error);
                showError(errorDetails);
            }
        } finally {
            setIsLoading(false);
        }
    };

    // Fonction pour réessayer la connexion
    const handleRetry = () => {
        if (email && password) {
            handleSubmit({ preventDefault: () => {} } as FormEvent);
        }
    };

    // Fonction pour gérer le clic sur le lien "Forgot Password?"
    const handleForgotPasswordClick = (e: React.MouseEvent<HTMLAnchorElement>) => {
        e.preventDefault();
        setIsForgotPasswordLoading(true);

        // Rediriger vers la page de récupération de mot de passe après un court délai
        setTimeout(() => {
            router.push('/forgot-password');
        }, 800); // Délai de 800ms pour montrer l'animation
    };

    return <>
        <div className="flex flex-col lg:flex-row bg-white dark:bg-gray-900 dark:text-white min-h-screen">
            {/* Back to Home Button */}
            <button
                onClick={() => router.push('/')}
                className="fixed top-4 left-4 z-50 flex items-center gap-2 px-3 py-2 sm:px-4 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 border border-gray-200 dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
                <ArrowLeft className="w-4 h-4" />
                <span className="text-xs sm:text-sm font-medium hidden sm:inline">Back to Home</span>
                <span className="text-xs font-medium sm:hidden">Back</span>
            </button>

            {/* Section de l'image latérale - Responsive */}
            <div className="asideLogo w-full lg:w-[50%] h-48 sm:h-64 lg:h-screen py-2 px-2 lg:fixed lg:left-0 lg:top-0">
                <div className="asideImage w-full h-full">
                    <img
                        src="/assets/images/asideImage2.png"
                        className="h-full w-full rounded-[15px] sm:rounded-[20px] lg:rounded-[25px] object-cover"
                        alt="Aside Image"
                    />
                </div>
            </div>

            {/* Section du formulaire - Responsive and scrollable */}
            <div className="asideForm w-full lg:w-[50%] lg:ml-[50%] bg-white dark:bg-gray-900 flex flex-col justify-center items-center min-h-screen lg:overflow-y-auto px-4 sm:px-6 lg:px-4 custom-scrollbar">
                <div className="flex flex-col justify-center items-center w-full max-w-[90%] sm:max-w-[400px] lg:max-w-[500px] dark:text-white py-4 sm:py-6 lg:py-8">
                    <div className="mb-4 sm:mb-6">
                        <AppLogo logoSrc="/assets/logo.png" logoAlt="Logo" />
                    </div>
                    <div className="w-full mx-auto p-4 sm:p-6" >
                        <FormHeading
                            title="Nice to see you!"
                            subtitle="Sign in to access your Dashboard"
                            formIcon={
                                <svg width="50" height="50" viewBox="0 0 50 50" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <rect x="1.25" y="1.25" width="47.5" height="47.5" rx="13.75" stroke="#17B890" strokeWidth="2.5" />
                                    <path d="M27.167 17.832H30.5003C30.9424 17.832 31.3663 18.0076 31.6788 18.3202C31.9914 18.6327 32.167 19.0567 32.167 19.4987V31.1654C32.167 31.6074 31.9914 32.0313 31.6788 32.3439C31.3663 32.6564 30.9424 32.832 30.5003 32.832H27.167" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M23 29.4974L27.1667 25.3307L23 21.1641" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                    <path d="M27.167 25.332H17.167" stroke="#17B890" strokeWidth="2.5" strokeLinecap="round" strokeLinejoin="round" />
                                </svg>
                            }
                        />
                        {/* Error handling */}
                        <LoginErrorCard
                            errorDetails={errorDetails}
                            isVisible={isErrorVisible}
                            onClose={hideError}
                            onRetry={handleRetry}
                        />
                        <form onSubmit={handleSubmit} className="flex flex-col space-y-4 sm:space-y-5">
                            <div>
                                <Input
                                    label="Email"
                                    type="email"
                                    id="email"
                                    prefixIcon={
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M17.5 3.4375H2.5C2.25136 3.4375 2.0129 3.53627 1.83709 3.71209C1.66127 3.8879 1.5625 4.12636 1.5625 4.375V15C1.5625 15.4144 1.72712 15.8118 2.02015 16.1049C2.31317 16.3979 2.7106 16.5625 3.125 16.5625H16.875C17.2894 16.5625 17.6868 16.3979 17.9799 16.1049C18.2729 15.8118 18.4375 15.4144 18.4375 15V4.375C18.4375 4.12636 18.3387 3.8879 18.1629 3.71209C17.9871 3.53627 17.7486 3.4375 17.5 3.4375ZM15.0898 5.3125L10 9.97813L4.91016 5.3125H15.0898ZM3.4375 14.6875V6.50625L9.36641 11.9414C9.53932 12.1 9.7654 12.1879 10 12.1879C10.2346 12.1879 10.4607 12.1 10.6336 11.9414L16.5625 6.50625V14.6875H3.4375Z" fill="#575D5E" />
                                        </svg>
                                    }
                                    value={email}
                                    onChange={(e) => {
                                        setEmail(e.target.value);
                                        if (emailError) setEmailError("");
                                    }}
                                    className={`w-full pl-10 pr-3 py-2 sm:py-3 border rounded-lg focus:outline-none focus:ring-2 text-sm sm:text-base ${
                                        emailError ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'
                                    }`}
                                    required
                                />
                                {emailError && (
                                    <p className="text-red-500 text-sm mt-1">{emailError}</p>
                                )}
                            </div>

                            <div>
                                <Input
                                    label="Password"
                                    type="password"
                                    id="password"
                                    prefixIcon={
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M15.625 8.75H15V6.25C15 3.48858 12.7614 1.25 10 1.25C7.23858 1.25 5 3.48858 5 6.25V8.75H4.375C3.68464 8.75 3.125 9.30964 3.125 10V16.25C3.125 16.9404 3.68464 17.5 4.375 17.5H15.625C16.3154 17.5 16.875 16.9404 16.875 16.25V10C16.875 9.30964 16.3154 8.75 15.625 8.75ZM6.25 6.25C6.25 4.17893 7.92893 2.5 10 2.5C12.0711 2.5 13.75 4.17893 13.75 6.25V8.75H6.25V6.25ZM15.625 16.25H4.375V10H15.625V16.25ZM10 11.875C9.65482 11.875 9.375 12.1548 9.375 12.5V14.375C9.375 14.7202 9.65482 15 10 15C10.3452 15 10.625 14.7202 10.625 14.375V12.5C10.625 12.1548 10.3452 11.875 10 11.875Z" fill="#575D5E" />
                                        </svg>
                                    }
                                    value={password}
                                    onChange={(e) => {
                                        setPassword(e.target.value);
                                        if (passwordError) setPasswordError("");
                                    }}
                                    className={`w-full pl-10 pr-3 py-2 sm:py-3 border rounded-lg focus:outline-none focus:ring-2 text-sm sm:text-base ${
                                        passwordError ? 'border-red-500 focus:ring-red-500' : 'focus:ring-blue-500'
                                    }`}
                                    required
                                />
                                {passwordError && (
                                    <p className="text-red-500 text-sm mt-1">{passwordError}</p>
                                )}
                            </div>

                            <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center mb-4 sm:mb-6 space-y-2 sm:space-y-0">
                                <div className="flex flex-col">
                                    <label className="flex items-center">
                                        <input
                                            type="checkbox"
                                            checked={rememberMe}
                                            onChange={(e) => setRememberMe(e.target.checked)}
                                            className="mr-2 w-4 h-4"
                                        />
                                        <span className="text-xs sm:text-sm text-gray-800 dark:text-gray-200">Keep me signed in (30 days)</span>
                                    </label>
                                    <span className="text-xs text-gray-500 dark:text-gray-400 ml-6">
                                        Default session: 7 days
                                    </span>
                                </div>
                                <a
                                    href="/forgot-password"
                                    onClick={handleForgotPasswordClick}
                                    className="text-xs sm:text-sm text-blue-600 hover:underline flex items-center mt-2 sm:mt-0"
                                >
                                    {isForgotPasswordLoading ? (
                                        <>
                                            <Loader2 className="h-3 w-3 animate-spin mr-1" />
                                            Redirecting...
                                        </>
                                    ) : (
                                        "Forgot Password?"
                                    )}
                                </a>
                            </div>

                            <button
                                type="submit"
                                disabled={isLoading || isRedirecting}
                                className={`w-full flex justify-center gap-2 items-center py-2 sm:py-3 rounded-full transition duration-200 text-sm sm:text-base font-medium ${
                                    isLoading || isRedirecting
                                        ? 'bg-gray-400 cursor-not-allowed'
                                        : 'bg-[#17B890] hover:bg-[#17b890c4]'
                                } text-white`}
                            >
                                {(isLoading || isRedirecting) && <Loader2 className="h-4 w-4 animate-spin" />}
                                {isRedirecting ? "Redirection..." : isLoading ? "Connexion..." : "Sign In"}
                                {!isLoading && !isRedirecting && (
                                    <svg width="21" height="20" viewBox="0 0 21 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                        <path d="M4.66699 10H16.3337M16.3337 10L11.3337 5M16.3337 10L11.3337 15" stroke="white" strokeWidth="1.67" strokeLinecap="round" strokeLinejoin="round" />
                                    </svg>
                                )}
                            </button>
                        </form>
                    </div>
                    <div>
                        <ul className="flex justify-center items-center gap-5 py-4 text-gray-800 dark:text-gray-200 text-sm">
                            <li className="hover:text-[#17B890]"><a href="#">Licence</a></li>
                            <li className="hover:text-[#17B890]"><a href="#">Terms of Use</a></li>
                            <li className="hover:text-[#17B890]"><a href="#">Blog</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </>
}
