import Cookies from "js-cookie";
import { BASE_API_URL } from "./AuthContext";
import {
  StudentSchema,
  StudentCreateSchema,
  StudentUpdateSchema,
} from "../models/StudentModel";
import { FeePaymentSchema } from "../models/FeePayment";
import { UserSchema } from "../models/UserModel";
import ApiInterceptorService, { SubscriptionRequiredError, FeatureAccessDeniedError } from "./ApiInterceptorService";

/**
 * Helper pour gérer les erreurs de subscription de manière cohérente
 */
const handleSubscriptionError = (error: any, defaultMessage: string): never => {
  console.error('Service error:', error);

  if (error instanceof SubscriptionRequiredError) {
    throw new Error(`Mise à niveau requise: ${error.subscriptionError.subscription_required}`);
  }

  if (error instanceof FeatureAccessDeniedError) {
    throw new Error(`Accès refusé: Cette fonctionnalité nécessite un abonnement ${error.subscriptionError.subscription_required}`);
  }

  throw new Error(defaultMessage);
};

// const BASE_API_URL = "http://localhost:3001/api";
interface RegisterStudentResponse {
  ok: boolean;
  message: string;
  student: StudentSchema;
  feePayment?: FeePaymentSchema;
  parent?: UserSchema;
}
export function getTokenFromCookie(name: string) {
  return Cookies.get(name);
}

export async function registerStudent(studentFormData: Record<string, any>): Promise<RegisterStudentResponse> {
  try {
    return await ApiInterceptorService.post('/student/register-students', studentFormData);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to register student with parent and payment");
  }
}

export async function getStudents(): Promise<StudentSchema[]> {
  try {
    return await ApiInterceptorService.get('/student/get-students');
  } catch (error) {
    return handleSubscriptionError(error, "Failed to fetch students");
  }
}

export async function getStudentById(studentId: string): Promise<StudentSchema> {
  try {
    return await ApiInterceptorService.get(`/student/get-student/${studentId}`);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to fetch student");
  }
}

export async function createStudent(studentData: StudentCreateSchema): Promise<StudentSchema> {
  try {
    return await ApiInterceptorService.post('/student/create-student', studentData);
  } catch (error) {
    return handleSubscriptionError(error, "Failed to create student");
  }
}

export async function updateStudent(id: string, studentData: StudentUpdateSchema): Promise<StudentSchema> {
  const token = getTokenFromCookie("idToken");

  // Debug logging for unassignment operations
  if (studentData.class_id === null) {
    console.log(`🚀 Sending unassignment request for student ${id}:`, studentData);
    console.log(`📤 Request payload:`, JSON.stringify(studentData, null, 2));
  }

  const response = await fetch(`${BASE_API_URL}/student/update-student/${id}`, {
    method: "PUT",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
    body: JSON.stringify(studentData),
  });

  if (!response.ok) {
    let errorMessage = "Failed to update student";
    try {
      const errorBody = await response.json();
      errorMessage = errorBody?.message || errorMessage;
    } catch (parseError) {
      console.warn("Could not parse error response:", parseError);
    }
    console.error("Error updating student:", errorMessage);
    throw new Error(errorMessage);
  }

  const data = await response.json();

  // Debug logging for unassignment operations
  if (studentData.class_id === null) {
    console.log(`📥 Backend response for student ${id}:`, data);
    console.log(`🔍 Returned class_id:`, data.class_id, `(type: ${typeof data.class_id})`);
  }

  return data as StudentSchema;
}

export async function deleteStudent(id: string): Promise<{ message: string }> {
  const token = getTokenFromCookie("idToken");

  const response = await fetch(`${BASE_API_URL}/student/delete-student/${id}`, {
    method: "DELETE",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    let errorMessage = "Failed to delete student";
    try {
      const errorBody = await response.json();
      errorMessage = errorBody?.message || errorMessage;
    } catch (parseError) {
      console.warn("Could not parse error response:", parseError);
    }
    console.error("Error deleting student:", errorMessage);
    throw new Error(errorMessage);
  }

  const result = await response.json();
  return result;
}

export async function getStudentsByClassAndSchool(
  classId: string,
  schoolId: string
): Promise<StudentSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(
      `${BASE_API_URL}/student/class/${classId}/school/${schoolId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      console.error("Error fetching students by class and school:", response.statusText);
      throw new Error("Failed to fetch filtered students");
    }

    const data = await response.json();
    return data as StudentSchema[];
  } catch (error) {
    console.error("Fetch error (filtered students):", error);
    throw new Error("Failed to fetch filtered students");
  }
}

// New function to get students by class history and academic year
export async function getStudentsByClassHistoryAndAcademicYear(
  classId: string,
  schoolId: string,
  academicYear?: string
): Promise<StudentSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    // If no academic year is provided, fall back to current class assignment
    if (!academicYear) {
      return getStudentsByClassAndSchool(classId, schoolId);
    }

    // Get all students from the school
    const allStudents = await getStudentsBySchool(schoolId);

    // Filter students who were in this class during the specified academic year
    const studentsInClassForYear = allStudents.filter(student => {
      // Check if student has class history
      if (!student.class_history || !Array.isArray(student.class_history)) {
        // If no class history, check current assignment
        return student.class_id === classId;
      }

      // Check class history for the specific academic year and class
      return student.class_history.some(historyEntry =>
        historyEntry.class_id === classId &&
        historyEntry.academic_year === academicYear
      );
    });

    console.log(`🔍 Found ${studentsInClassForYear.length} students who were in class ${classId} during ${academicYear}`);

    return studentsInClassForYear;
  } catch (error) {
    console.error("Error fetching students by class history and academic year:", error);
    throw new Error("Failed to fetch students by class history and academic year");
  }
}

export async function getStudentsBySchool(schoolId: string, p0?: { page: number; limit: number; classLevelId: string; statusFilter: string; }): Promise<StudentSchema[]> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(
      `${BASE_API_URL}/student/get-students-by-school?schoolId=${schoolId}`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      console.error("Error fetching students by school:", response.statusText);
      throw new Error("Failed to fetch students by school");
    }

    const data = await response.json();
    return data as StudentSchema[];
  } catch (error) {
    console.error("Fetch error (students by school):", error);
    throw new Error("Failed to fetch students by school");
  }
}

export async function searchStudents(
  schoolId: string,
  options: { student_id?: string; name?: string }
): Promise<StudentSchema[]> {
  const token = getTokenFromCookie("idToken");

  if (!schoolId) {
    throw new Error("schoolId is required to search students.");
  }

  const params = new URLSearchParams({ school_id: schoolId });

  if (options.student_id?.trim()) {
    params.append("student_id", options.student_id.trim());
  }

  if (options.name?.trim()) {
    params.append("name", options.name.trim());
  }

  const url = `${BASE_API_URL}/student/search-students?${params.toString()}`;

  try {
    const response = await fetch(url, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Search error response:", response.status, errorText);
      throw new Error(`Failed to search students: ${response.statusText}`);
    }

    const data = await response.json();
    return data as StudentSchema[];
  } catch (error) {
    console.error("Search error:", error);
    throw new Error("An error occurred while searching for students.");
  }
}

export async function importStudentsFromCSV(
  schoolId: string,
  file: File
): Promise<{
  message: string;
  total: number;
  successful: number;
  errors: Array<{ row: number; error: string }>;
}> {

  try {
    return await ApiInterceptorService.uploadFile(
      `/student/import-csv-students/${schoolId}`,
      file,
      'file',
      undefined,
      true // Afficher le prompt de mise à niveau si nécessaire
    );
  } catch (error) {
    console.error('Error importing students from CSV:', error);

    // Gestion spécifique des erreurs d'abonnement
    if (error instanceof SubscriptionRequiredError) {
      throw new Error(`Mise à niveau requise: ${error.subscriptionError.subscription_required}`);
    }

    if (error instanceof FeatureAccessDeniedError) {
      throw new Error(`Accès refusé: Cette fonctionnalité nécessite un abonnement ${error.subscriptionError.subscription_required}`);
    }

    throw new Error("An error occurred while importing students from CSV.");
  }
}
export async function uploadStudentAvatar(studentId: string, file: File): Promise<{ message: string; avatarUrl?: string }> {
  const token = getTokenFromCookie("idToken");

  const formData = new FormData();
  formData.append("avatar", file); // field name must match `upload.single('avatar')`

  try {
    const response = await fetch(`${BASE_API_URL}/student/upload-student-photo/${studentId}/avatar`, {
      method: "POST",
      headers: {
        Authorization: `Bearer ${token}`,
        // ❗ Do NOT manually set Content-Type when using FormData
      },
      body: formData,
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("Avatar upload failed:", response.status, errorText);
      throw new Error("Failed to upload avatar");
    }

    const result = await response.json();
    return result; // expected: { message: string, avatarUrl?: string }
  } catch (error) {
    console.error("Upload avatar error:", error);
    throw new Error("An error occurred while uploading the avatar.");
  }
}

export async function deleteMultipleStudents(studentIds: string[]): Promise<{ message: string }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/student/delete-students`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids: studentIds }),
    });

    if (!response.ok) {
      let errorMessage = "Failed to delete multiple students";
      try {
        const errorBody = await response.json();
        errorMessage = errorBody?.message || errorMessage;
      } catch (parseError) {
        console.warn("Could not parse error response:", parseError);
      }
      console.error("Error deleting multiple students:", errorMessage);
      throw new Error(errorMessage);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error in deleteMultipleStudents:", error);
    throw new Error("An error occurred while deleting multiple students.");
  }
}

export async function deleteAllStudents(): Promise<{ message: string; deletedCount: number }> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/student/delete-all-students`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      let errorMessage = "Failed to delete all students";
      try {
        const errorBody = await response.json();
        errorMessage = errorBody?.message || errorMessage;
      } catch (parseError) {
        console.warn("Could not parse error response:", parseError);
      }
      throw new Error(errorMessage);
    }

    const result = await response.json();
    return result;
  } catch (error) {
    console.error("Error in deleteAllStudents:", error);
    throw new Error("An error occurred while deleting all students.");
  }
}


// Response interface for total students
export interface TotalStudentsResponse {
  totalStudents: number;
}

// Response interface for students count with change
export interface StudentsCountChangeResponse {
  countThisMonth: number;
  percentageChange: number; // e.g. 12.5 means +12.5%, -8 means -8%
}

// Fetch total number of students
export async function getTotalStudents(): Promise<TotalStudentsResponse> {
  const token = Cookies.get("idToken");

  const response = await fetch(`${BASE_API_URL}/student/total-students`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching total students:", response.statusText);
    throw new Error("Failed to fetch total students");
  }

    const data: TotalStudentsResponse = await response.json();
    return data;
}

// Fetch number of students created this month and % change vs previous month
export async function getStudentsCountChange(): Promise<StudentsCountChangeResponse> {
  const token = Cookies.get("idToken");

  const response = await fetch(`${BASE_API_URL}/student/students-count-change`, {
    method: "GET",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${token}`,
    },
  });

  if (!response.ok) {
    console.error("Error fetching students count change:", response.statusText);
    throw new Error("Failed to fetch students count change");
  }

  const data = await response.json();
  return data as StudentsCountChangeResponse;
}

export async function verifyStudentId(id: string): Promise<StudentSchema> {
  const token = getTokenFromCookie("idToken");

  try {
    const response = await fetch(`${BASE_API_URL}/student/verify-id/${id}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error verifying student ID:", response.statusText);
      throw new Error("Failed to verify student ID");
    }

    const data = await response.json();
    return data as StudentSchema;
  } catch (error) {
    console.error("Error verifying student ID:", error);
    throw new Error("Failed to verify student ID");
  }
}

// Interface pour les statistiques des étudiants enregistrés
export interface RegisteredStudentsStatsSchema {
  school_id: string;
  academic_year: string;
  registered_students: number;
  total_students: number;
  registration_rate: number;
  total_revenue: number;
  average_payment: number;
  message: string;
}

// Récupérer les statistiques des étudiants enregistrés par école
export async function getRegisteredStudentsStats(schoolId: string, academicYear: string): Promise<RegisteredStudentsStatsSchema> {
  const token = getTokenFromCookie("idToken");
  console.log("academicYear:", academicYear);
  try {
    const response = await fetch(`${BASE_API_URL}/student/registered-stats/${schoolId}?academic_year=${academicYear}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      console.error("Error fetching registered students stats:", response.statusText);
      throw new Error("Failed to fetch registered students stats");
    }

    const data = await response.json();
    return data as RegisteredStudentsStatsSchema;
  } catch (error) {
    console.error("Error fetching registered students stats:", error);
    throw error;
  }
}