'use client';

import React, { useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { 
  Users, 
  UserPlus, 
  Download, 
  Upload,
  BarChart3,
  Crown,
  AlertTriangle
} from 'lucide-react';
import { useSubscription, useFeatureAccess, useFeatureLimit } from '@/context/SubscriptionContext';
import { FeatureGate } from '@/components/subscription/FeatureGate';
import { FeatureButton } from '@/components/subscription/FeatureButton';
import { UsageLimit } from '@/components/school-admin/SubscriptionAwareWrapper';
import StudentComponent from '@/components/Dashboard/ReusableComponents/StudentComponent';

interface SubscriptionAwareStudentPageProps {
  user: any;
  schoolId?: string;
}

export default function SubscriptionAwareStudentPage({ 
  user, 
  schoolId 
}: SubscriptionAwareStudentPageProps) {
  const { subscription, setSchoolId } = useSubscription();
  const { hasAccess: hasAdvancedReports } = useFeatureAccess('advanced_reports');
  const { hasAccess: hasBulkOperations } = useFeatureAccess('bulk_operations');
  const studentLimit = useFeatureLimit('students');

  // Définir le schoolId si fourni
  useEffect(() => {
    if (schoolId) {
      setSchoolId(schoolId);
    }
  }, [schoolId, setSchoolId]);

  return (
    <div className="space-y-6">
      {/* Banner d'information sur les limites */}
      {studentLimit.isNearLimit && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-3">
                <AlertTriangle className="w-5 h-5 text-yellow-600" />
                <div>
                  <h4 className="font-semibold text-yellow-800">
                    Limite d'étudiants bientôt atteinte
                  </h4>
                  <p className="text-sm text-yellow-700">
                    Vous avez utilisé {studentLimit.usage} sur {studentLimit.limit} étudiants autorisés.
                  </p>
                </div>
              </div>
              <FeatureButton
                featureId="student_limit_increase"
                className="bg-yellow-600 hover:bg-yellow-700 text-white"
              >
                <Crown className="w-4 h-4 mr-2" />
                Augmenter la limite
              </FeatureButton>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Actions rapides avec contrôle d'accès */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {/* Action de base - toujours disponible */}
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <UserPlus className="w-8 h-8 mx-auto mb-3 text-blue-500" />
            <h3 className="font-semibold mb-2">Ajouter un étudiant</h3>
            <p className="text-sm text-gray-600 mb-4">Inscrire un nouvel étudiant</p>
            <Button className="w-full">
              Ajouter
            </Button>
          </CardContent>
        </Card>

        {/* Rapports avancés - Premium */}
        <FeatureGate
          featureId="advanced_reports"
          fallback={
            <Card className="relative border-dashed border-gray-300">
              <div className="absolute inset-0 bg-gray-50 bg-opacity-80 flex items-center justify-center z-10">
                <div className="text-center p-4">
                  <Crown className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                  <p className="text-xs text-gray-600 mb-2">Fonctionnalité Premium</p>
                  <FeatureButton
                    featureId="advanced_reports"
                    size="sm"
                  >
                    Débloquer
                  </FeatureButton>
                </div>
              </div>
              <CardContent className="p-6 text-center opacity-50">
                <BarChart3 className="w-8 h-8 mx-auto mb-3 text-purple-500" />
                <h3 className="font-semibold mb-2">Rapports avancés</h3>
                <p className="text-sm text-gray-600 mb-4">Analyses détaillées des étudiants</p>
                <Button variant="outline" className="w-full" disabled>
                  Générer rapport
                </Button>
              </CardContent>
            </Card>
          }
        >
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <BarChart3 className="w-8 h-8 mx-auto mb-3 text-purple-500" />
              <h3 className="font-semibold mb-2">Rapports avancés</h3>
              <p className="text-sm text-gray-600 mb-4">Analyses détaillées des étudiants</p>
              <Button variant="outline" className="w-full">
                Générer rapport
              </Button>
            </CardContent>
          </Card>
        </FeatureGate>

        {/* Opérations en lot - Enterprise */}
        <FeatureGate
          featureId="bulk_operations"
          fallback={
            <Card className="relative border-dashed border-gray-300">
              <div className="absolute inset-0 bg-gray-50 bg-opacity-80 flex items-center justify-center z-10">
                <div className="text-center p-4">
                  <Crown className="w-6 h-6 mx-auto mb-2 text-gray-400" />
                  <p className="text-xs text-gray-600 mb-2">Fonctionnalité Enterprise</p>
                  <FeatureButton
                    featureId="bulk_operations"
                    size="sm"
                  >
                    Débloquer
                  </FeatureButton>
                </div>
              </div>
              <CardContent className="p-6 text-center opacity-50">
                <Upload className="w-8 h-8 mx-auto mb-3 text-green-500" />
                <h3 className="font-semibold mb-2">Import/Export</h3>
                <p className="text-sm text-gray-600 mb-4">Opérations en lot</p>
                <div className="space-y-2">
                  <Button variant="outline" className="w-full" disabled>
                    <Upload className="w-4 h-4 mr-2" />
                    Importer
                  </Button>
                  <Button variant="outline" className="w-full" disabled>
                    <Download className="w-4 h-4 mr-2" />
                    Exporter
                  </Button>
                </div>
              </CardContent>
            </Card>
          }
        >
          <Card className="cursor-pointer hover:shadow-md transition-shadow">
            <CardContent className="p-6 text-center">
              <Upload className="w-8 h-8 mx-auto mb-3 text-green-500" />
              <h3 className="font-semibold mb-2">Import/Export</h3>
              <p className="text-sm text-gray-600 mb-4">Opérations en lot</p>
              <div className="space-y-2">
                <Button variant="outline" className="w-full">
                  <Upload className="w-4 h-4 mr-2" />
                  Importer
                </Button>
                <Button variant="outline" className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Exporter
                </Button>
              </div>
            </CardContent>
          </Card>
        </FeatureGate>
      </div>

      {/* Limite d'utilisation des étudiants */}
      {subscription && (
        <UsageLimit
          current={studentLimit.usage}
          limit={studentLimit.limit}
          label="Étudiants inscrits"
          featureId="student_limit"
          className="mb-6"
        />
      )}

      {/* Composant principal des étudiants */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Gestion des étudiants
            </div>
            <div className="flex space-x-2">
              {hasAdvancedReports && (
                <Button variant="outline" size="sm">
                  <BarChart3 className="w-4 h-4 mr-2" />
                  Rapports
                </Button>
              )}
              {hasBulkOperations && (
                <Button variant="outline" size="sm">
                  <Upload className="w-4 h-4 mr-2" />
                  Import
                </Button>
              )}
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <StudentComponent user={user} />
        </CardContent>
      </Card>

      {/* Informations sur le plan actuel */}
      {subscription && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-semibold text-blue-800">
                  Plan actuel : {subscription.plan.toUpperCase()}
                </h4>
                <p className="text-sm text-blue-700">
                  {subscription.features.length} fonctionnalités actives
                </p>
              </div>
              <FeatureButton
                featureId="plan_upgrade"
                className="bg-blue-600 hover:bg-blue-700"
              >
                <Crown className="w-4 h-4 mr-2" />
                Mettre à niveau
              </FeatureButton>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
