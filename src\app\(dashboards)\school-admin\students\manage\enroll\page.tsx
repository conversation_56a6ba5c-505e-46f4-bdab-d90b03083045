"use client";
import SuperLayout from '@/components/Dashboard/Layouts/SuperLayout'
import { GraduationCap, Presentation, Trash2, UserPlus, X } from 'lucide-react' // Added Trash2, UserPlus, X for icons
import React, { useEffect, useState, useMemo } from 'react'
import { useSearchParams } from "next/navigation";
import { SchoolSchema } from '@/app/models/SchoolModel';
import { ClassLevelSchema } from '@/app/models/ClassLevel';
import { StudentSchema } from '@/app/models/StudentModel';
import { ClassSchema } from '@/app/models/ClassModel';
import { getSchoolBy_id } from '@/app/services/SchoolServices';
import { getClassLevelsBySchoolId } from '@/app/services/ClassLevels';
import { getStudentsBySchool, updateStudent } from '@/app/services/StudentServices';
import { getClassesBySchool } from '@/app/services/ClassServices';
import CircularLoader from '@/components/widgets/CircularLoader';
import Head from 'next/head';
import SchoolLayout from '@/components/Dashboard/Layouts/SchoolLayout';
import UnassignConfirmModal from './component/UnassignConfirmModal'; // Ensure this import path is correct
import { useAcademicYearContext } from '@/context/AcademicYearContext';
import { ClassHistoryEntry } from '@/app/models/StudentModel';
import EnhancedPagination from '@/components/ui/EnhancedPagination';

// For modal animation (if you have framer-motion installed)
import { motion, AnimatePresence } from 'framer-motion';

// Define default items per page for client-side pagination
const DEFAULT_ITEMS_PER_PAGE = 10;
const ITEMS_PER_PAGE_OPTIONS = [5, 10, 20, 50, 100];
const BASE_URL = "/school-admin";

// --- Inline AssignStudentModal Component ---
// This is a simplified modal for assigning a student to a class.
interface AssignStudentModalProps {
    isOpen: boolean;
    onClose: () => void;
    onConfirm: (classId: string) => Promise<void>;
    student: StudentSchema | null;
    availableClasses: ClassSchema[];
    selectedClassId: string;
    onClassSelect: (classId: string) => void;
    loading: boolean;
}

const AssignStudentModal: React.FC<AssignStudentModalProps> = ({
    isOpen,
    onClose,
    onConfirm,
    student,
    availableClasses,
    selectedClassId,
    onClassSelect,
    loading
}) => {
    if (!isOpen || !student) return null;

    // Filter classes relevant to the student's current class level
    const relevantClasses = useMemo(() => {
        return availableClasses.filter(cls => cls.class_level === student.class_level);
    }, [availableClasses, student.class_level]);


    return (
        <AnimatePresence>
            {isOpen && (
                <motion.div
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4 font-inter"
                >
                    <motion.div
                        initial={{ opacity: 0, scale: 0.95 }}
                        animate={{ opacity: 1, scale: 1 }}
                        exit={{ opacity: 0, scale: 0.95 }}
                        className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-md text-gray-900 dark:text-gray-100"
                    >
                        {/* Header */}
                        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
                            <div className="flex items-center space-x-3">
                                <div className="w-10 h-10 bg-indigo-600 rounded-lg flex items-center justify-center">
                                    <GraduationCap className="h-5 w-5 text-white" />
                                </div>
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100">
                                        Assign Class to {student.name}
                                    </h3>
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                        Select a class for this student.
                                    </p>
                                </div>
                            </div>
                            <button
                                onClick={onClose}
                                className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors"
                                disabled={loading}
                            >
                                <X className="h-5 w-5" />
                            </button>
                        </div>

                        {/* Content */}
                        <div className="p-6">
                            <div className="mb-4">
                                <p className="text-gray-700 dark:text-gray-300 mb-2">
                                    Student ID: <span className="font-semibold">{student.student_id}</span>
                                </p>
                                <p className="text-gray-700 dark:text-gray-300 mb-4">
                                    Class Level: <span className="font-semibold">
                                        {/* Find the class level name from availableClassLevels */}
                                        {student.class_level ? (
                                            (availableClasses.find(c => c.class_level === student.class_level)?.name?.split(' ')[0] || 'N/A')
                                        ) : 'N/A'}
                                    </span>
                                </p>

                                <label htmlFor="assignClassSelect" className="block text-gray-700 dark:text-gray-300 font-medium mb-2">
                                    Select Class:
                                </label>
                                <div className="relative">
                                    <select
                                        id="assignClassSelect"
                                        className="block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-base pr-8 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                        value={selectedClassId}
                                        onChange={(e) => onClassSelect(e.target.value)}
                                        disabled={loading}
                                    >
                                        {relevantClasses.length === 0 ? (
                                            <option value="">No Classes Available for this level</option>
                                        ) : (
                                            <>
                                                <option value="">Select a Class</option>
                                                {relevantClasses.map((cls) => (
                                                    <option key={cls._id} value={cls._id}>
                                                        {cls.name} (Code: {cls.class_code})
                                                    </option>
                                                ))}
                                            </>
                                        )}
                                    </select>
                                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20">
                                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            {/* Actions */}
                            <div className="flex justify-end space-x-3">
                                <button
                                    type="button"
                                    onClick={onClose}
                                    className="px-4 py-2 text-gray-700 hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-100 transition-colors rounded-md"
                                    disabled={loading}
                                >
                                    Cancel
                                </button>
                                <button
                                    onClick={() => onConfirm(selectedClassId)}
                                    className="flex items-center space-x-2 px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
                                    disabled={loading || !selectedClassId}
                                >
                                    {loading && <CircularLoader size={16} color="white" />}
                                    {!loading && <UserPlus className="h-4 w-4" />}
                                    <span>{loading ? "Assigning..." : "Assign"}</span>
                                </button>
                            </div>
                        </div>
                    </motion.div>
                </motion.div>
            )}
        </AnimatePresence>
    );
};
// --- END Inline AssignStudentModal Component ---


function EnrollmentPage() {
    const searchParams = useSearchParams();
    const schoolId = searchParams.get("schoolId");
    const { currentAcademicYear } = useAcademicYearContext();

    const [school, setSchool] = useState<SchoolSchema | null>(null);
    const [classLevels, setClassLevels] = useState<ClassLevelSchema[]>([]);

    // Raw data from backend (all students)
    const [allStudentsRaw, setAllStudentsRaw] = useState<StudentSchema[]>([]);

    // States for client-side filtering and display for UNASSIGNED students
    const [unassignedSearchQuery, setUnassignedSearchQuery] = useState<string>(''); // New state for unassigned student search
    const [selectedClassLevelFilter, setSelectedClassLevelFilter] = useState<string>(''); // Renamed for clarity for unassigned students
    const [pendingStudents, setPendingStudents] = useState<StudentSchema[]>([]); // This will be the paginated, filtered list

    // States for client-side filtering and display for ENROLLED students
    const [enrolledSearchQuery, setEnrolledSearchQuery] = useState<string>(''); // Renamed for clarity for enrolled students
    const [enrolledFilterByClass, setEnrolledFilterByClass] = useState<string>(''); // Renamed for clarity for enrolled students
    const [enrolledStudents, setEnrolledStudents] = useState<StudentSchema[]>([]); // This will be the paginated, filtered list

    const [availableClasses, setAvailableClasses] = useState<ClassSchema[]>([]);
    const [selectedStudents, setSelectedStudents] = useState<string[]>([]); // For batch assignment (pending/unassigned students)

    // New state for batch unassign (enrolled students)
    const [selectedEnrolledStudents, setSelectedEnrolledStudents] = useState<string[]>([]);

    const [batchAssignClass, setBatchAssignClass] = useState<string>('');

    const [loading, setLoading] = useState(true); // Main loading indicator for initial fetches
    const [batchLoading, setBatchLoading] = useState(false); // For batch assignment/unassignment operations
    const [individualLoading, setIndividualLoading] = useState(false); // For individual assignment/unassignment operations
    const [error, setError] = useState<string | null>(null);

    const [showAssignModal, setShowAssignModal] = useState(false); // Controls visibility of individual assignment modal
    const [currentStudentForAssignment, setCurrentStudentForAssignment] = useState<StudentSchema | null>(null);
    const [selectedClassForIndividual, setSelectedClassForIndividual] = useState<string>('');

    // States for Unassign Confirm Modal
    const [showUnassignModal, setShowUnassignModal] = useState(false);
    const [studentForUnassign, setStudentForUnassign] = useState<StudentSchema | null>(null); // For single unassign
    const [studentsToUnassignIds, setStudentsToUnassignIds] = useState<string[]>([]); // For batch unassign
    const [unassignModalType, setUnassignModalType] = useState<"single" | "multiple">("single");


    // Pagination states for pending students
    const [currentPagePending, setCurrentPagePending] = useState(1);
    const [totalPendingCount, setTotalPendingCount] = useState(0);
    const [itemsPerPagePending, setItemsPerPagePending] = useState(DEFAULT_ITEMS_PER_PAGE);

    // Pagination states for enrolled students
    const [currentPageEnrolled, setCurrentPageEnrolled] = useState(1);
    const [totalEnrolledCount, setTotalEnrolledCount] = useState(0);
    const [itemsPerPageEnrolled, setItemsPerPageEnrolled] = useState(DEFAULT_ITEMS_PER_PAGE);


    // --- Class History Helper Functions ---
    // Note: Enrollment history is handled automatically by the backend when class_id and academic_year are provided
    // We only need the removal function for unenrollment since backend doesn't handle that automatically
    const removeFromClassHistory = (existingHistory: ClassHistoryEntry[] = [], classId: string, academicYear?: string): ClassHistoryEntry[] => {
        return existingHistory.filter(entry => {
            if (academicYear) {
                // Remove specific entry for this class and academic year
                return !(entry.class_id === classId && entry.academic_year === academicYear);
            } else {
                // Remove all entries for this class
                return entry.class_id !== classId;
            }
        });
    };

    // --- Data Fetching Functions ---
    const fetchSchoolData = async () => {
        try {
            const schoolData = await getSchoolBy_id(schoolId as string);
            if (schoolData) {
                setSchool(schoolData);
            } else {
                setError("School not found");
            }
        } catch (err) {
            console.error("Error fetching school data:", err);
            setError("Failed to fetch school data");
        }
    };

    const fetchClassLevels = async () => {
        try {
            const levels = await getClassLevelsBySchoolId(schoolId as string);
            setClassLevels(levels);
            // Do NOT set default selectedClassLevelFilter here. Keep it empty for "All" option.
            // The initial empty string for selectedClassLevelFilter will correctly show "All".
        } catch (err) {
            console.error("Error fetching class levels:", err);
            setError("Failed to fetch class levels");
        }
    };

    // Unified function to fetch ALL student data from the backend
    const fetchAllStudentsData = async () => {
        setError(null); // Clear previous errors
        try {
            // Fetch all students with a large limit for client-side filtering and pagination
            const students = await getStudentsBySchool(schoolId as string, {
                page: 1,
                limit: 10000, // Large limit to get all students for client-side filtering
                classLevelId: "", // Not filtering by class level at the API level for this
                statusFilter: "" // Not filtering by status at the API level for this
            });
            setAllStudentsRaw(students);
        } catch (err) {
            console.error("Error fetching all students:", err);
            setError("Failed to fetch all students data");
            setAllStudentsRaw([]);
        }
    };

    const fetchAvailableClasses = async () => {
        try {
            const allClasses = await getClassesBySchool(schoolId as string);
            setAvailableClasses(allClasses);
            if (allClasses.length > 0) {
                // Ensure batchAssignClass is a valid class for the currently selected level
                // This assumes all selected students for batch assign are of the same class level (controlled by filter)
                const relevantClasses = allClasses.filter(cls => selectedClassLevelFilter ? cls.class_level === selectedClassLevelFilter : true); // If no level is selected, all classes are relevant
                if (relevantClasses.length > 0 && !relevantClasses.some(cls => cls._id === batchAssignClass)) {
                    setBatchAssignClass(relevantClasses[0]._id);
                } else if (relevantClasses.length === 0) {
                    setBatchAssignClass('');
                }
            } else {
                setBatchAssignClass('');
            }
        } catch (err) {
            console.error("Error fetching classes:", err);
            setError("Failed to fetch classes");
        }
    };

    // Initial data fetching on component mount or schoolId change
    useEffect(() => {
        if (schoolId) {
            const fetchDataOnInit = async () => {
                setLoading(true);
                await Promise.all([
                    fetchSchoolData(),
                    fetchClassLevels(),
                    fetchAllStudentsData(), // Fetch all students once
                    fetchAvailableClasses(),
                ]);
                setLoading(false);
            };
            fetchDataOnInit();
        }
    }, [schoolId]);

    // --- Client-side Filtering and Pagination ---
    useEffect(() => {
        // Only proceed if raw data is available or if initial loading is complete and no raw data was found
        if (!allStudentsRaw.length && !loading) {
             setPendingStudents([]);
             setEnrolledStudents([]);
             setTotalPendingCount(0);
             setTotalEnrolledCount(0);
             return;
        }

        // Filter for Students Not Assigned to a Class (Status "not enrolled" or "pending" and no class_id)
        let filteredPending = allStudentsRaw.filter((student) => {
            const matchesClassLevel = selectedClassLevelFilter
                ? student.class_level === selectedClassLevelFilter
                : true; // If no class level selected (empty string), consider all
            const isNotAssigned =
                (student.status === "not enrolled") && // Include "not enrolled" and "pending"
                student.registered === true && // Must be registered with the school
                matchesClassLevel &&
                !student.class_id; // Crucially, they must NOT have a class_id assigned

            const matchesSearch = unassignedSearchQuery
                ? student.name.toLowerCase().includes(unassignedSearchQuery.toLowerCase()) ||
                  student.student_id.toLowerCase().includes(unassignedSearchQuery.toLowerCase())
                : true;

            return isNotAssigned && matchesSearch;
        });

        setTotalPendingCount(filteredPending.length);
        const startIndexPending = (currentPagePending - 1) * itemsPerPagePending;
        const endIndexPending = startIndexPending + itemsPerPagePending;
        setPendingStudents(filteredPending.slice(startIndexPending, endIndexPending));
        setSelectedStudents([]); // Clear batch assignment selections when pending students data refreshes

        // Filter for Enrolled Students
        let filteredEnrolled = allStudentsRaw.filter((student) => {
            const isEnrolled = student.status === "enrolled" && student.registered === true && student.class_id;
            if (!isEnrolled) return false;

            const matchesSearch = enrolledSearchQuery
                ? student.name.toLowerCase().includes(enrolledSearchQuery.toLowerCase()) ||
                  student.student_id.toLowerCase().includes(enrolledSearchQuery.toLowerCase())
                : true;

            const matchesClassFilter = enrolledFilterByClass
                ? student.class_id === enrolledFilterByClass
                : true;

            return matchesSearch && matchesClassFilter;
        });

        setTotalEnrolledCount(filteredEnrolled.length);
        const startIndexEnrolled = (currentPageEnrolled - 1) * itemsPerPageEnrolled;
        const endIndexEnrolled = startIndexEnrolled + itemsPerPageEnrolled;
        setEnrolledStudents(filteredEnrolled.slice(startIndexEnrolled, endIndexEnrolled));
        setSelectedEnrolledStudents([]); // Clear batch unassign selections when enrolled students data refreshes

    }, [allStudentsRaw, selectedClassLevelFilter, unassignedSearchQuery, enrolledSearchQuery, enrolledFilterByClass, currentPagePending, currentPageEnrolled, itemsPerPagePending, itemsPerPageEnrolled, loading]);


    // Effect to reset pending page when class level filter or search changes
    useEffect(() => {
        setCurrentPagePending(1);
    }, [selectedClassLevelFilter, unassignedSearchQuery]);

    // Effect to reset enrolled page when search/filter changes
    useEffect(() => {
        setCurrentPageEnrolled(1);
    }, [enrolledSearchQuery, enrolledFilterByClass]);

    // Effect to reset page when items per page changes
    useEffect(() => {
        setCurrentPagePending(1);
    }, [itemsPerPagePending]);

    useEffect(() => {
        setCurrentPageEnrolled(1);
    }, [itemsPerPageEnrolled]);


    // --- Selection Handlers ---
    const handleStudentSelect = (studentId: string) => { // For batch assignment (pending/unassigned students)
        setSelectedStudents(prev =>
            prev.includes(studentId)
                ? prev.filter(id => id !== studentId)
                : [...prev, studentId]
        );
    };

    const handleSelectAllStudents = (e: React.ChangeEvent<HTMLInputElement>) => { // For batch assignment (pending/unassigned students)
        if (e.target.checked) {
            setSelectedStudents(pendingStudents.map(student => student._id));
        } else {
            setSelectedStudents([]);
        }
    };

    const handleEnrolledStudentSelect = (studentId: string) => { // For batch unassignment (enrolled students)
        setSelectedEnrolledStudents(prev =>
            prev.includes(studentId)
                ? prev.filter(id => id !== studentId)
                : [...prev, studentId]
        );
    };

    const handleSelectAllEnrolledStudents = (e: React.ChangeEvent<HTMLInputElement>) => { // For batch unassignment (enrolled students)
        if (e.target.checked) {
            setSelectedEnrolledStudents(enrolledStudents.map(student => student._id));
        } else {
            setSelectedEnrolledStudents([]);
        }
    };


    // --- Assignment Functions ---
    const openAssignModal = (student: StudentSchema) => {
        setCurrentStudentForAssignment(student);
        setShowAssignModal(true);
        // Filter available classes to only those matching the student's class_level for individual assignment
        const classesForStudentLevel = availableClasses.filter(cls => cls.class_level === student.class_level);
        if (classesForStudentLevel.length > 0) {
            setSelectedClassForIndividual(student.class_id || classesForStudentLevel[0]._id);
        } else {
            setSelectedClassForIndividual('');
        }
    };

    const handleBatchAssign = async () => {
        if (selectedStudents.length === 0 || !batchAssignClass) {
            setError('Please select students and a class for batch assignment.');
            return;
        }
        if (!schoolId) {
            setError('School context missing. Cannot perform assignment. Please ensure school ID is in URL.');
            return;
        }

        setBatchLoading(true);
        setError(null);
        try {
            // Get the selected class details for class history
            const selectedClass = availableClasses.find(cls => cls._id === batchAssignClass);

            const updates = selectedStudents.map(studentId => {
                return updateStudent(studentId, {
                    class_id: batchAssignClass,
                    class_level: selectedClass?.class_level,
                    academic_year: currentAcademicYear || new Date().getFullYear() + "/" + (new Date().getFullYear() + 1),
                    status: "enrolled",
                    _id: studentId, // Ensure _id is passed for update
                    registered: true,
                    enrollement_date: new Date() // Set enrollment date on enrollment
                    // Note: class_history is handled automatically by the backend when class_id and academic_year are provided
                });
            });
            await Promise.all(updates);
            await fetchAllStudentsData(); // Re-fetch all raw data to reflect changes
            setSelectedStudents([]);
            setBatchAssignClass('');
            setError("Batch assignment successful!"); // Using setError for notifications
        } catch (err) {
            console.error("Error during batch assignment:", err);
            setError("Failed to batch assign students. Please try again.");
        } finally {
            setBatchLoading(false);
        }
    }

    const handleIndividualAssign = async (classId: string) => {
        if (!currentStudentForAssignment || !classId) {
            setError('Please select a class for the student.');
            return;
        }

        setIndividualLoading(true);
        setError(null);
        try {
            // Get the selected class details for class history
            const selectedClass = availableClasses.find(cls => cls._id === classId);

            await updateStudent(currentStudentForAssignment._id, {
                class_id: classId,
                class_level: selectedClass?.class_level,
                academic_year: currentAcademicYear,
                status: "enrolled",
                _id: currentStudentForAssignment._id, // Ensure _id is passed for update
                registered: true,
                enrollement_date: new Date() // Set enrollment date on enrollment
                // Note: class_history is handled automatically by the backend when class_id and academic_year are provided
            });

            setShowAssignModal(false); // Close the individual assign modal
            setCurrentStudentForAssignment(null);
            setSelectedClassForIndividual('');
            await fetchAllStudentsData(); // Re-fetch all raw data to reflect changes
            setError("Student assigned successfully!"); // Using setError for notifications
        } catch (err) {
            console.error("Error during individual assignment:", err);
            setError("Failed to assign student. Please try again.");
        } finally {
            setIndividualLoading(false);
        }
    };

    // --- Unassign Functions ---
    // Function to open the Unassign Confirmation Modal for a single student
    const openIndividualUnassignModal = (student: StudentSchema) => {
        setStudentForUnassign(student);
        setStudentsToUnassignIds([]); // Ensure batch unassign list is empty
        setUnassignModalType("single");
        setShowUnassignModal(true);
    };

    // Function to open the Unassign Confirmation Modal for multiple students
    const openBatchUnassignModal = () => {
        if (selectedEnrolledStudents.length === 0) {
            setError("Please select students to unassign.");
            return;
        }
        setStudentsToUnassignIds(selectedEnrolledStudents);
        setStudentForUnassign(null); // Ensure single unassign student is null
        setUnassignModalType("multiple");
        setShowUnassignModal(true);
    };

    // Unified function to handle the actual unassign logic (called by the modal's onConfirm)
    const confirmUnassignAction = async () => {
        setError(null);
        // Set loading states for UI feedback during the unassign operation
        setIndividualLoading(true); // Used for single unassign
        setBatchLoading(true); // Used for batch unassign

        let studentIdsToProcess: string[] = [];
        let messageContext = "";

        if (unassignModalType === "single" && studentForUnassign) {
            studentIdsToProcess = [studentForUnassign._id];
            messageContext = `class from ${studentForUnassign.name}`;
        } else if (unassignModalType === "multiple" && studentsToUnassignIds.length > 0) {
            studentIdsToProcess = studentsToUnassignIds;
            messageContext = `${studentsToUnassignIds.length} selected students`;
        } else {
            setError("No students selected for unassignment.");
            setShowUnassignModal(false);
            setIndividualLoading(false);
            setBatchLoading(false);
            return;
        }

        try {
            const updates = studentIdsToProcess.map(id => {
                // Find the student to get their current class history and class_id
                const student = allStudentsRaw.find(s => s._id === id);
                const currentHistory = student?.class_history || [];
                const currentClassId = student?.class_id;

                // Remove class history entry for current academic year and class
                const updatedHistory = currentClassId
                    ? removeFromClassHistory(currentHistory, currentClassId, currentAcademicYear)
                    : currentHistory;

                // Try different approaches for unsetting class_id
                const updatePayload = {
                    // Option 1: Explicit null (MongoDB stores null value)
                    class_id: null,

                    // Option 2: Try undefined (might get filtered out)
                    // class_id: undefined,

                    // Option 3: Try empty string (if backend expects string)
                    // class_id: "",

                    // Option 4: Use MongoDB $unset syntax (requires backend support)
                    // $unset: { class_id: "" },
                    // $set: {
                    //     status: "not enrolled",
                    //     registered: true,
                    //     enrollement_date: null,
                    //     class_history: updatedHistory,
                    // }

                    status: "not enrolled" as const,
                    _id: id,
                    registered: true,
                    enrollement_date: null,
                    class_history: updatedHistory,
                };

                // Debug logging to see what's being sent
                console.log(`🔍 Unassigning student ${id}:`, updatePayload);
                console.log(`📝 class_id value:`, updatePayload.class_id, `(type: ${typeof updatePayload.class_id})`);
                console.log(`📋 Full payload:`, JSON.stringify(updatePayload, null, 2));

                return updateStudent(id, updatePayload);
            });
            await Promise.all(updates);
            await fetchAllStudentsData(); // Re-fetch all raw data to reflect changes in UI
            setError(`Successfully unassigned ${messageContext}.`); // Success message
            setShowUnassignModal(false); // Close the modal after successful operation
            setSelectedEnrolledStudents([]); // Clear selections after batch unassign
        } catch (err) {
            console.error("Error during unassignment:", err);
            setError(`Failed to unassign ${messageContext}. Please try again.`);
        } finally {
            setIndividualLoading(false);
            setBatchLoading(false);
        }
    };

    // Calculate total pages for pagination controls
    const totalPagesPending = Math.ceil(totalPendingCount / itemsPerPagePending);
    const totalPagesEnrolled = Math.ceil(totalEnrolledCount / itemsPerPageEnrolled);

    const navigation = {
        icon: Presentation,
        baseHref: `${BASE_URL}/students`,
        title: "Students"
    };

    return (
            <SchoolLayout navigation={navigation} onLogout={() => { /* Handle logout */ }}>
                <div className="min-h-screen bg-background text-foreground font-inter p-4 sm:p-6 lg:p-8">
                    <Head>
                        <title>Assign Students to Classes - {school?.name ?? ''}</title>
                    </Head>

                    <div className="max-w-7xl mx-auto bg-background text-foreground shadow-xl rounded-xl p-6 sm:p-8">
                        <h1 className="text-3xl sm:text-4xl font-bold text-foreground mb-4 border-b pb-4">
                            Student Enrollment Management
                        </h1>
                        <p className="text-lg text-gray-600 mb-6">
                            Currently managing assignments for: <span className="font-semibold text-teal-600 dark:text-teal-400">{school?.name}</span>
                        </p>

                        {/* Error/Success Notification */}
                        {error && <div className={`px-4 py-3 rounded-md text-center text-sm font-semibold mb-4 ${error.includes('successful') ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'}`}>
                            {error}
                        </div>}

                        {/* Students Not Assigned to a Class Table Section */}
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-8 relative">
                            <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                Students Not Assigned to a Class ({totalPendingCount})
                            </h2>

                            {/* Search and Filter for UNASSIGNED Students */}
                            <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                                {/* Search by Name/ID for Unassigned Students */}
                                <div className="flex-grow w-full sm:w-auto">
                                    <label htmlFor="unassignedStudentSearch" className="sr-only">Search Unassigned Student</label>
                                    <input
                                        type="text"
                                        id="unassignedStudentSearch"
                                        placeholder="Search unassigned student by name or ID..."
                                        value={unassignedSearchQuery}
                                        onChange={(e) => setUnassignedSearchQuery(e.target.value)}
                                        className="block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                        disabled={loading}
                                    />
                                </div>

                                {/* Filter by Class Level for Unassigned Students */}
                                <div className="relative w-full sm:w-64">
                                    <label htmlFor="unassignedClassLevelFilter" className="sr-only">Filter by Class Level</label>
                                    <select
                                        id="unassignedClassLevelFilter"
                                        className="block w-full py-2 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-base pr-8"
                                        value={selectedClassLevelFilter}
                                        onChange={(e) => setSelectedClassLevelFilter(e.target.value)}
                                        disabled={loading}
                                    >
                                        <option value="">All Class Levels</option> {/* "All" option */}
                                        {classLevels.length === 0 && !loading ? (
                                            <option value="" disabled>No Class Levels Available</option>
                                        ) : (
                                            <>
                                                {classLevels.map((level) => (
                                                    <option key={level._id} value={level._id}>
                                                        {level.name}
                                                    </option>
                                                ))}
                                            </>
                                        )}
                                    </select>
                                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.000/svg" viewBox="0 0 20 20">
                                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            {loading && (
                                <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900 bg-opacity-80 z-10 rounded-lg">
                                    <CircularLoader size={40} />
                                </div>
                            )}

                            {pendingStudents.length === 0 && !loading && (
                                <p className="text-center text-gray-600 dark:text-gray-400 py-8">
                                    {selectedClassLevelFilter || unassignedSearchQuery ? 'No students found matching your filters.' : 'No students currently unassigned to a class.'}
                                </p>
                            )}

                            {pendingStudents.length > 0 && (
                                <>
                                    {/* Summary Info */}
                                    <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-blue-800 dark:text-blue-200">
                                                Showing {((currentPagePending - 1) * itemsPerPagePending) + 1}-{Math.min(currentPagePending * itemsPerPagePending, totalPendingCount)} of {totalPendingCount} unassigned students
                                            </span>
                                            <span className="text-blue-600 dark:text-blue-400 font-medium">
                                                Page {currentPagePending} of {totalPagesPending}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                        <thead className="bg-gray-50 dark:bg-gray-700 ">
                                            <tr>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tl-lg">
                                                    <input
                                                        type="checkbox"
                                                        className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                                                        onChange={handleSelectAllStudents}
                                                        checked={selectedStudents.length === pendingStudents.length && pendingStudents.length > 0}
                                                        disabled={loading}
                                                    />
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Student Name
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Student ID
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Class Level
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Status
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Registered Date
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tr-lg">
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                            {pendingStudents.map((student) => (
                                                <tr key={student._id} className="dark:hover:bg-gray-800 hover:bg-gray-50">
                                                    <td className="px-4 py-3 whitespace-nowrap">
                                                        <input
                                                            type="checkbox"
                                                            className="form-checkbox h-4 w-4 text-indigo-600 transition duration-150 ease-in-out rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                                                            checked={selectedStudents.includes(student._id)}
                                                            onChange={() => handleStudentSelect(student._id)}
                                                            disabled={loading}
                                                        />
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{student.name}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{student.student_id}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        {classLevels.find((level) => level._id === student.class_level)?.name || 'N/A'}
                                                    </td>
                                                     <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                                                            student.status === 'not enrolled' ? 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200' :
                                                            'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300' // Default or unknown status
                                                        }`}>
                                                            {student.status ? (student.status.charAt(0).toUpperCase() + student.status.slice(1)) : 'N/A'}
                                                        </span>
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        {typeof student.registration_date === 'string' && student.registration_date
                                                            ? new Date(student.registration_date).toLocaleDateString()
                                                            : 'N/A'}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() => openAssignModal(student)}
                                                            className="inline-flex items-center px-3 py-1 bg-indigo-600 text-white rounded-md hover:bg-indigo-700 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                                            disabled={loading || availableClasses.filter(cls => cls.class_level === student.class_level).length === 0}
                                                        >
                                                            Assign Class
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    </div>

                                    {/* Enhanced Pagination Controls for Students Not Assigned to a Class */}
                                    <EnhancedPagination
                                        currentPage={currentPagePending}
                                        totalPages={totalPagesPending}
                                        totalItems={totalPendingCount}
                                        itemsPerPage={itemsPerPagePending}
                                        itemsPerPageOptions={ITEMS_PER_PAGE_OPTIONS}
                                        onPageChange={setCurrentPagePending}
                                        onItemsPerPageChange={setItemsPerPagePending}
                                        loading={loading}
                                        className="mt-4"
                                    />
                                </>
                            )}
                        </div>

                        {/* Batch Assignment Control Section */}
                        {selectedStudents.length > 0 && (
                            <div className="bg-blue-50 dark:bg-blue-900/20 backdrop-blur-lg p-6 rounded-xl shadow-xl border border-blue-200 dark:border-blue-700 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-6 mb-8">
                                <div className="flex-grow w-full md:w-auto">
                                    <label htmlFor="batchClassSelect" className="block text-blue-800 dark:text-blue-200 font-bold mb-2">
                                        Assign {selectedStudents.length} Selected Students to:
                                    </label>
                                    <div className="relative">
                                        <select
                                            id="batchClassSelect"
                                            className="block w-full text-gray-900 py-2 px-3 border border-blue-300 bg-white/80 dark:bg-blue-100/80 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-base pr-8 backdrop-blur-md"
                                            value={batchAssignClass}
                                            onChange={(e) => setBatchAssignClass(e.target.value)}
                                            disabled={availableClasses.filter(cls => selectedClassLevelFilter ? cls.class_level === selectedClassLevelFilter : true).length === 0 || loading || batchLoading}
                                        >
                                            {availableClasses.filter(cls => selectedClassLevelFilter ? cls.class_level === selectedClassLevelFilter : true).length === 0 ? (
                                                <option value="">No Classes Available for this level</option>
                                            ) : (
                                                <>
                                                    <option value="">Select a Class</option>
                                                    {availableClasses
                                                        .filter(cls => selectedClassLevelFilter ? cls.class_level === selectedClassLevelFilter : true) // Filter by current class level, or all if none selected
                                                        .map((cls) => (
                                                        <option key={cls._id} value={cls._id}>
                                                            {cls.name} (Code: {cls.class_code})
                                                        </option>
                                                    ))}
                                                </>
                                            )}
                                        </select>
                                    </div>
                                </div>
                                <button
                                    onClick={handleBatchAssign}
                                    disabled={selectedStudents.length === 0 || !batchAssignClass || loading || batchLoading}
                                    className={`px-6 py-3 rounded-lg text-white font-semibold transition duration-200 ease-in-out shadow-md flex items-center justify-center
                                        ${selectedStudents.length === 0 || !batchAssignClass || loading || batchLoading
                                            ? 'bg-blue-300 cursor-not-allowed'
                                            : 'bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 active:bg-blue-800'
                                        }`}
                                >
                                    {batchLoading ? <CircularLoader size={20} color="white" /> : null}
                                    {batchLoading ? 'Assigning...' : 'Apply Batch Assignment'}
                                </button>
                            </div>
                        )}

                        <hr className="my-8 border-gray-200 dark:border-gray-700" />

                        {/* Enrolled Students Section */}
                        <div className="bg-white dark:bg-gray-800 p-4 rounded-lg shadow-md mb-8 relative">
                            <h2 className="text-2xl font-semibold text-gray-900 dark:text-gray-100 mb-4">
                                Enrolled Students ({totalEnrolledCount})
                            </h2>

                            {/* Search and Filter for Enrolled Students */}
                            <div className="mb-6 flex flex-col sm:flex-row items-start sm:items-center space-y-2 sm:space-y-0 sm:space-x-4">
                                {/* Search by Name */}
                                <div className="flex-grow w-full sm:w-auto">
                                    <label htmlFor="enrolledStudentSearch" className="sr-only">Search Enrolled Student</label>
                                    <input
                                        type="text"
                                        id="enrolledStudentSearch"
                                        placeholder="Search enrolled student by name or ID..."
                                        value={enrolledSearchQuery}
                                        onChange={(e) => setEnrolledSearchQuery(e.target.value)}
                                        className="block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-base bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                        disabled={loading}
                                    />
                                </div>

                                {/* Filter by Class */}
                                <div className="relative w-full sm:w-64">
                                    <label htmlFor="enrolledFilterByClass" className="sr-only">Filter by Class</label>
                                    <select
                                        id="enrolledFilterByClass"
                                        className="block w-full py-2 px-3 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-indigo-500 focus:border-indigo-500 sm:text-base pr-8 bg-white dark:bg-gray-700 text-gray-900 dark:text-gray-100"
                                        value={enrolledFilterByClass}
                                        onChange={(e) => setEnrolledFilterByClass(e.target.value)}
                                        disabled={loading}
                                    >
                                        <option value="">Filter by Class (All)</option>
                                        {availableClasses.length === 0 && !loading ? (
                                            <option value="" disabled>No Classes Available</option>
                                        ) : (
                                            <>
                                                {availableClasses.map((cls) => (
                                                    <option key={cls._id} value={cls._id}>
                                                        {cls.name} (Code: {cls.class_code})
                                                    </option>
                                                ))}
                                            </>
                                        )}
                                    </select>
                                    <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700 dark:text-gray-300">
                                        <svg className="fill-current h-4 w-4" xmlns="http://www.w3.000/svg" viewBox="0 0 20 20">
                                            <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" />
                                        </svg>
                                    </div>
                                </div>
                            </div>

                            {loading && (
                                <div className="absolute inset-0 flex items-center justify-center bg-gray-50 dark:bg-gray-900 bg-opacity-80 z-10 rounded-lg">
                                    <CircularLoader size={40} />
                                </div>
                            )}

                            {enrolledStudents.length === 0 && !loading && (
                                <p className="text-center text-gray-600 dark:text-gray-400 py-8">
                                    No students found enrolled matching the current filters.
                                </p>
                            )}

                            {enrolledStudents.length > 0 && (
                                <>
                                    {/* Summary Info */}
                                    <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                                        <div className="flex items-center justify-between text-sm">
                                            <span className="text-green-800 dark:text-green-200">
                                                Showing {((currentPageEnrolled - 1) * itemsPerPageEnrolled) + 1}-{Math.min(currentPageEnrolled * itemsPerPageEnrolled, totalEnrolledCount)} of {totalEnrolledCount} enrolled students
                                            </span>
                                            <span className="text-green-600 dark:text-green-400 font-medium">
                                                Page {currentPageEnrolled} of {totalPagesEnrolled}
                                            </span>
                                        </div>
                                    </div>

                                    <div className="overflow-x-auto rounded-lg border border-gray-200 dark:border-gray-700">
                                    <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                        <thead className="bg-gray-50 dark:bg-gray-700">
                                            <tr>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tl-lg">
                                                    <input
                                                        type="checkbox"
                                                        className="form-checkbox h-4 w-4 text-red-600 transition duration-150 ease-in-out rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                                                        onChange={handleSelectAllEnrolledStudents}
                                                        checked={selectedEnrolledStudents.length === enrolledStudents.length && enrolledStudents.length > 0}
                                                        disabled={loading || individualLoading || batchLoading}
                                                    />
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Student Name
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Student ID
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Class
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Class Level
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider">
                                                    Enrollment Date
                                                </th>
                                                <th scope="col" className="px-4 py-3 text-right text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider rounded-tr-lg">
                                                    Actions
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-700">
                                            {enrolledStudents.map((student) => (
                                                <tr key={student._id} className="dark:hover:bg-gray-800 hover:bg-gray-50">
                                                    <td className="px-4 py-3 whitespace-nowrap">
                                                        <input
                                                            type="checkbox"
                                                            className="form-checkbox h-4 w-4 text-red-600 transition duration-150 ease-in-out rounded border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800"
                                                            checked={selectedEnrolledStudents.includes(student._id)}
                                                            onChange={() => handleEnrolledStudentSelect(student._id)}
                                                            disabled={loading || individualLoading || batchLoading}
                                                        />
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-gray-100">{student.name}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">{student.student_id}</td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        {availableClasses.find((cls) => cls._id === student.class_id)?.name || 'N/A'}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        {classLevels.find((level) => level._id === student.class_level)?.name || 'N/A'}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                        {typeof student.enrollement_date === 'string' && student.enrollement_date
                                                            ? new Date(student.enrollement_date).toLocaleDateString()
                                                            : 'N/A'}
                                                    </td>
                                                    <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                                        <button
                                                            onClick={() => openIndividualUnassignModal(student)}
                                                            className="inline-flex items-center px-3 py-1 bg-red-600 text-white rounded-md hover:bg-red-700 transition duration-150 ease-in-out focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 shadow-sm disabled:opacity-50 disabled:cursor-not-allowed"
                                                            disabled={loading || individualLoading || batchLoading}
                                                        >
                                                            Unassign
                                                        </button>
                                                    </td>
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                    </div>

                                    {/* Enhanced Pagination Controls for Enrolled Students */}
                                    <EnhancedPagination
                                        currentPage={currentPageEnrolled}
                                        totalPages={totalPagesEnrolled}
                                        totalItems={totalEnrolledCount}
                                        itemsPerPage={itemsPerPageEnrolled}
                                        itemsPerPageOptions={ITEMS_PER_PAGE_OPTIONS}
                                        onPageChange={setCurrentPageEnrolled}
                                        onItemsPerPageChange={setItemsPerPageEnrolled}
                                        loading={loading}
                                        className="mt-4"
                                    />
                                </>
                            )}
                        </div>

                        {/* Batch Unassign Control Section for Enrolled Students */}
                        {selectedEnrolledStudents.length > 0 && (
                            <div className="bg-red-50 dark:bg-red-900/20 backdrop-blur-lg p-6 rounded-xl shadow-xl border border-red-200 dark:border-red-700 flex flex-col md:flex-row items-center justify-between space-y-4 md:space-y-0 md:space-x-6">
                                <div className="flex-grow w-full md:w-auto">
                                    <p className="block text-red-800 dark:text-red-200 font-bold mb-2">
                                        Unassign {selectedEnrolledStudents.length} Selected Enrolled Students?
                                    </p>
                                    <p className="text-sm text-red-700 dark:text-red-300">
                                        This will remove their class assignment and set their status to "not enrolled".
                                    </p>
                                </div>
                                <button
                                    onClick={openBatchUnassignModal}
                                    disabled={selectedEnrolledStudents.length === 0 || loading || individualLoading || batchLoading}
                                    className={`px-6 py-3 rounded-lg text-white font-semibold transition duration-200 ease-in-out shadow-md flex items-center justify-center
                                        ${selectedEnrolledStudents.length === 0 || loading || individualLoading || batchLoading
                                            ? 'bg-red-300 cursor-not-allowed'
                                            : 'bg-red-600 hover:bg-red-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500 active:bg-red-800'
                                        }`}
                                >
                                    <Trash2 className="w-5 h-5 mr-2" />
                                    {(loading || individualLoading || batchLoading) ? <CircularLoader size={20} color="white" /> : 'Confirm Batch Unassign'}
                                </button>
                            </div>
                        )}

                    </div>

                    {/* Render the Unassign Confirmation Modal */}
                    <UnassignConfirmModal
                        isOpen={showUnassignModal}
                        onClose={() => setShowUnassignModal(false)}
                        onConfirm={confirmUnassignAction}
                        title={unassignModalType === "single" ? "Confirm Unassign Class" : "Confirm Batch Unassign"}
                        message={unassignModalType === "single"
                            ? `Are you sure you want to unassign the class from "${studentForUnassign?.name}" (ID: ${studentForUnassign?.student_id})? This will make them pending for assignment again.`
                            : `Are you sure you want to unassign classes from ${studentsToUnassignIds.length} selected students? This will make them pending for assignment again.`
                        }
                        itemName={unassignModalType === "single" ? `${studentForUnassign?.name} (ID: ${studentForUnassign?.student_id})` : undefined}
                        itemCount={unassignModalType === "multiple" ? studentsToUnassignIds.length : undefined}
                        type={unassignModalType}
                        loading={individualLoading || batchLoading} // Pass combined loading state
                    />

                    {/* Render the Assign Student Modal */}
                    <AssignStudentModal
                        isOpen={showAssignModal}
                        onClose={() => setShowAssignModal(false)}
                        onConfirm={handleIndividualAssign}
                        student={currentStudentForAssignment}
                        availableClasses={availableClasses}
                        selectedClassId={selectedClassForIndividual}
                        onClassSelect={setSelectedClassForIndividual}
                        loading={individualLoading}
                    />
                </div>
            </SchoolLayout>
    );
}

export default EnrollmentPage;