"use client";

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { Menu, X } from 'lucide-react';
import Logo from '@/components/widgets/Logo';
import ThemeToggle from '@/components/ThemeToggle';

interface SharedNavigationProps {
  showLoginButton?: boolean;
  showBackButton?: boolean;
  backButtonText?: string;
  onBackClick?: () => void;
}

export default function SharedNavigation({
  showLoginButton = true,
  showBackButton = false,
  backButtonText = "Back to Home",
  onBackClick
}: SharedNavigationProps) {
  const router = useRouter();
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleLogoClick = () => {
    router.push('/');
  };

  const handleNavigation = async (path: string) => {
    await router.push(path);
  };

  const handleBackClick = () => {
    if (onBackClick) {
      onBackClick();
    } else {
      router.push('/');
    }
  };

  const navigationItems = [
    { label: "About", path: "/#about" },
    { label: "Features", path: "/#features" },
    { label: "Pricing", path: "/pricing" },
    { label: "Team", path: "/#team" },
    { label: "Docs", path: "/docs" },
    { label: "Contact", path: "/contact" },
    { label: "Careers", path: "/careers" },
  ];

  if (!mounted) {
    return null;
  }

  return (
    <nav className="fixed top-0 left-0 right-0 z-40 bg-white/95 dark:bg-gray-900/95 backdrop-blur-md border-b border-gray-200 dark:border-gray-700 transition-all duration-300 shadow-sm dark:shadow-2xl">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Back Button */}
          <div className="flex items-center space-x-4">
            <button
              onClick={handleLogoClick}
              className="transition-transform duration-200 hover:scale-105 focus:outline-none focus:ring-2 focus:ring-teal-500 focus:ring-offset-2 rounded-lg"
              aria-label="Go to homepage"
            >
              <Logo />
            </button>
            
            {showBackButton && (
              <button
                onClick={handleBackClick}
                className="text-gray-600 dark:text-gray-300 hover:text-teal-600 dark:hover:text-teal-300 transition-colors font-medium text-sm flex items-center space-x-1"
              >
                <span>←</span>
                <span>{backButtonText}</span>
              </button>
            )}
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-6">
            {navigationItems.map((item) => (
              <button
                key={item.label}
                onClick={() => handleNavigation(item.path)}
                className="text-gray-800 dark:text-gray-100 hover:text-teal-600 dark:hover:text-teal-300 transition-colors font-medium text-sm"
              >
                {item.label}
              </button>
            ))}
          </div>

          <div className="flex items-center space-x-4">
            {/* Theme Toggle */}
            <ThemeToggle />

            {/* Login Button */}
            {showLoginButton && (
              <button
                onClick={() => handleNavigation("/login")}
                className="bg-teal-600 hover:bg-teal-700 dark:text-white text-white px-6 py-2 rounded-lg font-medium transition-all duration-200 shadow-sm hover:shadow-md transform hover:-translate-y-0.5"
              >
                Login
              </button>
            )}

            {/* Mobile menu button */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="md:hidden text-gray-800 dark:text-gray-100 p-2 rounded-lg hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors"
              aria-label="Toggle mobile menu"
            >
              {mobileMenuOpen ? (
                <X className="w-6 h-6" />
              ) : (
                <Menu className="w-6 h-6" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {mobileMenuOpen && (
          <div className="md:hidden bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-700">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigationItems.map((item) => (
                <button
                  key={item.label}
                  onClick={() => {
                    handleNavigation(item.path);
                    setMobileMenuOpen(false);
                  }}
                  className="block px-3 py-2 text-gray-800 dark:text-gray-100 hover:text-teal-600 dark:hover:text-teal-300 hover:bg-gray-50 dark:hover:bg-gray-800 transition-colors w-full text-left font-medium rounded-lg"
                >
                  {item.label}
                </button>
              ))}
              
              {showLoginButton && (
                <div className="pt-2 border-t border-gray-200 dark:border-gray-700 mt-2">
                  <button
                    onClick={() => {
                      handleNavigation("/login");
                      setMobileMenuOpen(false);
                    }}
                    className="w-full bg-teal-600 hover:bg-teal-700 text-white px-4 py-2 rounded-lg font-medium transition-colors"
                  >
                    Login
                  </button>
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </nav>
  );
} 