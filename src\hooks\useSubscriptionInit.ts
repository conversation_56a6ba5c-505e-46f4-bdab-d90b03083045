'use client';

import { useEffect } from 'react';
import { useSubscription } from '@/context/SubscriptionContext';
import useAuth from '@/app/hooks/useAuth';

/**
 * Hook pour initialiser automatiquement le subscription context
 * avec les données d'authentification
 */
export function useSubscriptionInit() {
  const { setSchoolId, schoolId } = useSubscription();
  const { user } = useAuth();

  useEffect(() => {
    // Initialiser le schoolId depuis les données d'authentification
    if (user?.schoolId && user.schoolId !== schoolId) {
      setSchoolId(user.schoolId);
    }
  }, [user?.schoolId, schoolId, setSchoolId]);

  return {
    isInitialized: !!schoolId,
    schoolId,
    user
  };
}

/**
 * Hook pour vérifier si l'utilisateur a accès à une fonctionnalité
 * avec initialisation automatique
 */
export function useFeatureAccessWithInit(featureId: string) {
  const { isInitialized } = useSubscriptionInit();
  const { useFeatureAccess } = require('@/context/SubscriptionContext');
  
  const access = useFeatureAccess(featureId);
  
  return {
    ...access,
    isInitialized
  };
}

export default useSubscriptionInit;
