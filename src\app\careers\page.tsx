"use client";

import { useState } from "react";
import { ArrowRight, MapPin, Clock, DollarSign, Users, Zap, Heart, Shield, BookOpen, GraduationCap, MessageCircle, Clock as ClockIcon } from "lucide-react";
import SharedNavigation from "@/components/layout/SharedNavigation";
import SharedFooter from "@/components/layout/SharedFooter";
import { useRouter } from "next/navigation";

export default function CareersPage() {
    const router = useRouter();
    const [activeTab, setActiveTab] = useState("openings");

    const jobOpenings = [
        {
            title: "Senior Full-Stack Developer",
            department: "Engineering",
            location: "Remote",
            type: "Full-time",
            salary: "$80,000 - $120,000",
            description: "Join our engineering team to build scalable educational technology solutions that impact thousands of students worldwide.",
            requirements: [
                "5+ years of experience with React/Next.js",
                "Strong backend development skills (Node.js, Python)",
                "Experience with cloud platforms (AWS, Azure)",
                "Knowledge of educational technology domain",
                "Excellent problem-solving and communication skills"
            ],
            benefits: [
                "Competitive salary and equity",
                "Flexible remote work environment",
                "Health, dental, and vision insurance",
                "Professional development budget",
                "Unlimited PTO"
            ]
        },
        {
            title: "Product Manager",
            department: "Product",
            location: "Remote",
            type: "Full-time",
            salary: "$90,000 - $130,000",
            description: "Lead product strategy and development for our school management platform, working closely with educators and administrators.",
            requirements: [
                "3+ years of product management experience",
                "Experience in EdTech or SaaS products",
                "Strong analytical and user research skills",
                "Excellent stakeholder management",
                "Agile/Scrum methodology experience"
            ],
            benefits: [
                "Competitive salary and equity",
                "Flexible remote work environment",
                "Health, dental, and vision insurance",
                "Professional development budget",
                "Unlimited PTO"
            ]
        },
        {
            title: "UX/UI Designer",
            department: "Design",
            location: "Remote",
            type: "Full-time",
            salary: "$70,000 - $100,000",
            description: "Create intuitive and accessible user experiences for educators, students, and administrators using our platform.",
            requirements: [
                "3+ years of UX/UI design experience",
                "Portfolio demonstrating educational or SaaS products",
                "Proficiency in Figma and design systems",
                "Understanding of accessibility standards",
                "Experience with user research and testing"
            ],
            benefits: [
                "Competitive salary and equity",
                "Flexible remote work environment",
                "Health, dental, and vision insurance",
                "Professional development budget",
                "Unlimited PTO"
            ]
        }
    ];

    const companyValues = [
        {
            icon: <Heart className="w-8 h-8" />,
            title: "Passion for Education",
            description: "We believe in the transformative power of education and are committed to making learning accessible to all."
        },
        {
            icon: <Zap className="w-8 h-8" />,
            title: "Innovation",
            description: "We constantly push boundaries to create cutting-edge solutions that address real educational challenges."
        },
        {
            icon: <Users className="w-8 h-8" />,
            title: "Collaboration",
            description: "We foster a culture of teamwork, open communication, and mutual respect across all departments."
        },
        {
            icon: <Shield className="w-8 h-8" />,
            title: "Integrity",
            description: "We maintain the highest standards of ethical behavior and data security in everything we do."
        }
    ];

    const benefits = [
        {
            icon: <BookOpen className="w-6 h-6" />,
            title: "Learning & Growth",
            description: "Continuous learning opportunities, conferences, and skill development programs."
        },
        {
            icon: <GraduationCap className="w-6 h-6" />,
            title: "Education Impact",
            description: "Work on products that directly impact students and educators worldwide."
        },
        {
            icon: <MessageCircle className="w-6 h-6" />,
            title: "Flexible Work",
            description: "Remote-first culture with flexible hours and work-life balance."
        },
        {
            icon: <ClockIcon className="w-6 h-6" />,
            title: "Unlimited PTO",
            description: "Take time off when you need it to recharge and maintain work-life balance."
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <SharedNavigation showBackButton={true} />

            {/* Hero Section */}
            <section className="py-20 bg-gradient-to-r from-teal-600 to-teal-700 dark:from-teal-800 dark:to-teal-900">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
                        Join Our Mission
                    </h1>
                    <p className="text-xl md:text-2xl text-teal-100 mb-8 max-w-3xl mx-auto">
                        Help us transform education technology and make a difference in the lives of students, teachers, and administrators worldwide.
                    </p>
                    <button
                        onClick={() => setActiveTab("openings")}
                        className="bg-white text-teal-700 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors"
                    >
                        View Open Positions
                    </button>
                </div>
            </section>

            {/* Navigation Tabs */}
            <section className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex space-x-8">
                        {[
                            { id: "openings", label: "Open Positions" },
                            { id: "values", label: "Our Values" },
                            { id: "benefits", label: "Benefits" }
                        ].map((tab) => (
                            <button
                                key={tab.id}
                                onClick={() => setActiveTab(tab.id)}
                                className={`py-4 px-1 border-b-2 font-medium text-sm transition-colors ${
                                    activeTab === tab.id
                                        ? "border-teal-500 text-teal-600 dark:text-teal-400"
                                        : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                                }`}
                            >
                                {tab.label}
                            </button>
                        ))}
                    </div>
                </div>
            </section>

            {/* Content Sections */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {activeTab === "openings" && (
                    <div className="space-y-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Open Positions
                            </h2>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                Join our team and help shape the future of education technology.
                            </p>
                        </div>

                        <div className="grid gap-8">
                            {jobOpenings.map((job, index) => (
                                <div
                                    key={index}
                                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8"
                                >
                                    <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                                        <div>
                                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
                                                {job.title}
                                            </h3>
                                            <p className="text-teal-600 dark:text-teal-400 font-medium">
                                                {job.department}
                                            </p>
                                        </div>
                                        <div className="flex flex-wrap gap-4 mt-4 md:mt-0">
                                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                                                <MapPin className="w-4 h-4 mr-2" />
                                                {job.location}
                                            </div>
                                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                                                <Clock className="w-4 h-4 mr-2" />
                                                {job.type}
                                            </div>
                                            <div className="flex items-center text-gray-600 dark:text-gray-300">
                                                <DollarSign className="w-4 h-4 mr-2" />
                                                {job.salary}
                                            </div>
                                        </div>
                                    </div>

                                    <p className="text-gray-700 dark:text-gray-300 mb-6">
                                        {job.description}
                                    </p>

                                    <div className="grid md:grid-cols-2 gap-8">
                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                                                Requirements
                                            </h4>
                                            <ul className="space-y-2">
                                                {job.requirements.map((req, reqIndex) => (
                                                    <li key={reqIndex} className="flex items-start">
                                                        <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                                        <span className="text-gray-700 dark:text-gray-300">{req}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>

                                        <div>
                                            <h4 className="font-semibold text-gray-900 dark:text-white mb-3">
                                                Benefits
                                            </h4>
                                            <ul className="space-y-2">
                                                {job.benefits.map((benefit, benefitIndex) => (
                                                    <li key={benefitIndex} className="flex items-start">
                                                        <div className="w-2 h-2 bg-teal-500 rounded-full mt-2 mr-3 flex-shrink-0"></div>
                                                        <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
                                                    </li>
                                                ))}
                                            </ul>
                                        </div>
                                    </div>

                                    <div className="mt-8">
                                        <button className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center">
                                            Apply Now
                                            <ArrowRight className="w-4 h-4 ml-2" />
                                        </button>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {activeTab === "values" && (
                    <div className="space-y-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Our Values
                            </h2>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                The principles that guide everything we do.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 gap-8">
                            {companyValues.map((value, index) => (
                                <div
                                    key={index}
                                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8 text-center"
                                >
                                    <div className="text-teal-600 dark:text-teal-400 mb-4 flex justify-center">
                                        {value.icon}
                                    </div>
                                    <h3 className="text-xl font-semibold text-gray-900 dark:text-white mb-3">
                                        {value.title}
                                    </h3>
                                    <p className="text-gray-700 dark:text-gray-300">
                                        {value.description}
                                    </p>
                                </div>
                            ))}
                        </div>
                    </div>
                )}

                {activeTab === "benefits" && (
                    <div className="space-y-8">
                        <div className="text-center mb-12">
                            <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                                Why Work With Us
                            </h2>
                            <p className="text-lg text-gray-600 dark:text-gray-300">
                                We invest in our team's growth and well-being.
                            </p>
                        </div>

                        <div className="grid md:grid-cols-2 gap-8">
                            {benefits.map((benefit, index) => (
                                <div
                                    key={index}
                                    className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
                                >
                                    <div className="flex items-start">
                                        <div className="text-teal-600 dark:text-teal-400 mr-4">
                                            {benefit.icon}
                                        </div>
                                        <div>
                                            <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                                {benefit.title}
                                            </h3>
                                            <p className="text-gray-700 dark:text-gray-300">
                                                {benefit.description}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    </div>
                )}
            </div>

            {/* CTA Section */}
            <section className="bg-gray-100 dark:bg-gray-800 py-16">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Ready to Join Our Team?
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                        Don't see a position that fits? We're always looking for talented individuals to join our mission.
                    </p>
                    <button className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-lg font-semibold transition-colors">
                        Send Us Your Resume
                    </button>
                </div>
            </section>

            {/* Footer */}
            <SharedFooter />
        </div>
    );
} 