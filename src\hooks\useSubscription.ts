import { useState, useEffect, useCallback, useContext } from 'react';
import { getTokenFromCookie } from '@/app/services/UserServices';
import FeatureRegistryServices, { FeatureAccess, FeatureInfo } from '@/app/services/FeatureRegistryServices';

// Les interfaces sont maintenant importées du service

interface SubscriptionInfo {
  plan_type: 'basic' | 'standard' | 'premium' | 'enterprise';
  status: 'active' | 'inactive' | 'suspended';
  features: string[];
  credits_balance: number;
  expires_at?: string;
}

interface UseSubscriptionReturn {
  // Subscription info
  subscription: SubscriptionInfo | null;
  loading: boolean;
  error: string | null;
  
  // Feature access methods
  checkFeatureAccess: (featureId: string) => Promise<FeatureAccess>;
  hasFeatureAccess: (featureId: string) => boolean;
  getRequiredLevel: (featureId: string) => string | null;
  
  // Bulk feature checks
  checkMultipleFeatures: (featureIds: string[]) => Promise<Record<string, FeatureAccess>>;
  
  // Subscription management
  refreshSubscription: () => Promise<void>;
  upgradeSubscription: (targetPlan: string) => void;
  
  // Feature discovery
  getAvailableFeatures: (module?: string) => Promise<any[]>;
  getFeaturesByModule: () => Promise<Record<string, any[]>>;
  
  // Cache management
  clearCache: () => void;
}

/**
 * Hook for managing subscription and feature access
 */
export function useSubscription(): UseSubscriptionReturn {
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [featureCache, setFeatureCache] = useState<Record<string, FeatureAccess>>({});

  // Get school ID from user context or localStorage
  const getSchoolId = useCallback(() => {
    try {
      const user = JSON.parse(localStorage.getItem('user') || '{}');
      return user.school_ids?.[0] || localStorage.getItem('school_id');
    } catch {
      return localStorage.getItem('school_id');
    }
  }, []);

  // Fetch subscription information
  const fetchSubscription = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const schoolId = getSchoolId();
      if (!schoolId) {
        throw new Error('School ID not found');
      }

      const token = getTokenFromCookie("idToken");
      const response = await fetch(`/api/school-subscription/get-subscription/${schoolId}`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error('Failed to fetch subscription');
      }

      const data = await response.json();
      
      if (data.success && data.data) {
        setSubscription({
          plan_type: data.data.plan_type,
          status: data.data.status,
          features: data.data.features || [],
          credits_balance: data.data.credits_balance || 0,
          expires_at: data.data.expires_at
        });
      }
    } catch (err) {
      console.error('Error fetching subscription:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch subscription');
    } finally {
      setLoading(false);
    }
  }, [getSchoolId]);

  // Check access to a specific feature
  const checkFeatureAccess = useCallback(async (featureId: string): Promise<FeatureAccess> => {
    // Check cache first
    if (featureCache[featureId]) {
      return featureCache[featureId];
    }

    try {
      const schoolId = getSchoolId();
      if (!schoolId) {
        return { hasAccess: false, reason: 'no_school_id' };
      }

      const result = await FeatureRegistryServices.checkFeatureAccess(featureId, schoolId);

      // Cache the result
      setFeatureCache(prev => ({ ...prev, [featureId]: result }));

      return result;
    } catch (err) {
      console.error('Error checking feature access:', err);
      return { hasAccess: false, reason: 'error' };
    }
  }, [getSchoolId, featureCache]);

  // Synchronous feature access check (uses cache)
  const hasFeatureAccess = useCallback((featureId: string): boolean => {
    const cached = featureCache[featureId];
    return cached ? cached.hasAccess : false;
  }, [featureCache]);

  // Get required subscription level for a feature
  const getRequiredLevel = useCallback((featureId: string): string | null => {
    const cached = featureCache[featureId];
    return cached ? cached.required_level || null : null;
  }, [featureCache]);

  // Check multiple features at once
  const checkMultipleFeatures = useCallback(async (featureIds: string[]): Promise<Record<string, FeatureAccess>> => {
    const results: Record<string, FeatureAccess> = {};

    // Check which features are not in cache
    const uncachedFeatures = featureIds.filter(id => !featureCache[id]);

    // Return cached results for features we already have
    featureIds.forEach(id => {
      if (featureCache[id]) {
        results[id] = featureCache[id];
      }
    });

    // Fetch uncached features using the service
    if (uncachedFeatures.length > 0) {
      const schoolId = getSchoolId();
      if (schoolId) {
        const serviceResults = await FeatureRegistryServices.checkMultipleFeatures(uncachedFeatures, schoolId);

        // Update cache and results
        Object.entries(serviceResults).forEach(([featureId, access]) => {
          results[featureId] = access;
          setFeatureCache(prev => ({ ...prev, [featureId]: access }));
        });
      }
    }

    return results;
  }, [featureCache, getSchoolId]);

  // Refresh subscription data
  const refreshSubscription = useCallback(async () => {
    await fetchSubscription();
    // Clear feature cache when subscription changes
    setFeatureCache({});
  }, [fetchSubscription]);

  // Navigate to upgrade page
  const upgradeSubscription = useCallback((targetPlan: string) => {
    const upgradeUrl = `/subscription/upgrade?plan=${targetPlan}`;
    window.location.href = upgradeUrl;
  }, []);

  // Get available features for current subscription
  const getAvailableFeatures = useCallback(async (module?: string): Promise<FeatureInfo[]> => {
    try {
      const level = subscription?.plan_type || 'basic';
      return await FeatureRegistryServices.getFeaturesBySubscriptionLevel(level, module);
    } catch (err) {
      console.error('Error fetching available features:', err);
      return [];
    }
  }, [subscription]);

  // Get features grouped by module
  const getFeaturesByModule = useCallback(async (): Promise<Record<string, FeatureInfo[]>> => {
    try {
      const level = subscription?.plan_type || 'basic';
      return await FeatureRegistryServices.getFeaturesByModuleGrouped(level);
    } catch (err) {
      console.error('Error fetching features by module:', err);
      return {};
    }
  }, [subscription]);

  // Clear feature cache
  const clearCache = useCallback(() => {
    setFeatureCache({});
  }, []);

  // Initialize subscription data
  useEffect(() => {
    fetchSubscription();
  }, [fetchSubscription]);

  return {
    subscription,
    loading,
    error,
    checkFeatureAccess,
    hasFeatureAccess,
    getRequiredLevel,
    checkMultipleFeatures,
    refreshSubscription,
    upgradeSubscription,
    getAvailableFeatures,
    getFeaturesByModule,
    clearCache
  };
}

// Utility hook for simple feature checks
export function useFeatureAccess(featureId: string) {
  const { checkFeatureAccess, hasFeatureAccess, getRequiredLevel } = useSubscription();
  const [access, setAccess] = useState<FeatureAccess | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAccess = async () => {
      setLoading(true);
      const result = await checkFeatureAccess(featureId);
      setAccess(result);
      setLoading(false);
    };

    checkAccess();
  }, [featureId, checkFeatureAccess]);

  return {
    hasAccess: access?.hasAccess || false,
    loading,
    requiredLevel: access?.required_level,
    currentLevel: access?.current_level,
    feature: access?.feature,
    reason: access?.reason
  };
}
