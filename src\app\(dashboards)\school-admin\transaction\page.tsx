"use client";
import React, { useEffect, useState, Suspense } from "react";
import {
  <PERSON><PERSON><PERSON> as Recharts<PERSON>ie<PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON> as RechartsBar<PERSON>hart,
  <PERSON>,
  XAxis,
  <PERSON>A<PERSON>s,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
} from "recharts";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import useAuth from "@/app/hooks/useAuth";
import CircularLoader from "@/components/widgets/CircularLoader";
// Import Lucide React icons
import { CreditCard, DollarSign, TrendingUp, Clock, Bell, Search, Filter, CalendarDays, Users, GraduationCap, ListChecks, ChevronDown, ChevronUp, ChevronLeft, ChevronRight, <PERSON><PERSON>hart as PieChartIcon, <PERSON><PERSON>hart as BarChartIcon, Printer, Download, FileText, FileSpreadsheet } from "lucide-react";
import { getFeePaymentsBySchoolId, markFeePaymentAsPaid, getSelectedFeesBySchoolId } from "@/app/services/FeePaymentServices"; // Import markFeePaymentAsPaid
import { FeePaymentSchema } from "@/app/models/FeePayment";

// Placeholder for ReceiptPDF and pdf function
// IMPORTANT: You need to ensure these imports and the ReceiptPDF component exist in your project.
// For example, if ReceiptPDF is in components/Receipt/ReceiptPDF.tsx
// and @react-pdf/renderer is installed.
import { pdf } from '@react-pdf/renderer'; // Assuming @react-pdf/renderer is installed
import ReceiptPDF from "@/components/utils/RecieptPDF";
import TransactionReportPDF from "@/components/utils/TransactionReportPDF";
import { FeeSchema } from "@/app/models/FeesModel";
import { getSettings } from "@/app/services/Settings";
import { useNotifications } from "@/hooks/useNotifications";

// Constants
const SCHOLARIFY_FEE_ID = "507f1f77bcf86cd799439011";

interface MonthlyCollection {
  month: string;
  amount: number;
}

interface UpcomingInstallment {
  student: string;
  dueDate: string;
  amount: number;
}

interface Notification {
  message: string;
}

// Define the Installment interface to explicitly include _id
interface Installment {
  amount: number;
  dueDate: string;
  paid: boolean;
  _id?: string; // Make _id optional since it might not exist for newly created installments
  transactionRef?: string;
  paidAt?: string;
}

// Interfaces for populated data from backend
interface StudentPopulated {
  _id: string;
  first_name: string;
  last_name: string;
  student_id: string;
  class_level?: string; // Can be string if not populated further, or ClassLevelPopulated
  class_id?: string; // If class_level is not populated
}

interface ClassLevelPopulated {
  _id: string;
  name: string;
}

interface SchoolPopulated {
  _id: string;
  name: string;
  logoUrl?: string; // Assuming your school model has a logoUrl
  email?: string;
  address?: string;
  website?: string;
  phone_number?: string;
}

interface FeePopulated {
  _id: string;
  fee_type: string;
  amount: number;
}

interface SchoolResourcePopulated {
  _id: string;
  name: string;
  price: number;
}

interface PaymentRecord {
  _id: string;
  student: string; // Transformed student name
  studentId: string; // Transformed student ID
  classLevel: string; // Transformed class level name
  academicYear: string;
  status: "pending" | "partially_paid" | "paid" | "cancelled";
  paymentMode: "full" | "installment";
  totalAmount: number;
  receipt: string; // receipt_number from backend
  installments: Installment[]; // Use the new Installment interface
  createdAt: string; // Add createdAt for monthly collections logic
  // Add original populated fields for receipt generation
  student_id: StudentPopulated | null;
  school_id: SchoolPopulated | null;
  class_level: ClassLevelPopulated | null;
  selectedFees: FeePopulated[];
  selectedResources: SchoolResourcePopulated[];
  scholarshipPercentage?: number;
}

// Interface for data passed to ReceiptPDF
interface ReceiptItem {
  description: string;
  amount: number;
}

interface ReceiptData {
  student: {
    student_id: string;
    first_name: string;
    last_name: string;
    class_level: string; // or class_id
  };
  school: {
    name: string;
    logoUrl?: string;
    email?: string;
    address?: string;
    website?: string;
    phone_number?: string;
  };
  paymentItems: ReceiptItem[];
  receiptId: string;
  date: string;
  taxRate?: number;
  applyScholarship?: boolean;
  scholarshipPercentage?: number;
  installments?: number; // Total number of installments
  installmentDates?: string[]; // Formatted due dates
  paidInstallmentNumber?: number; // Count of paid installments
  amountPaid?: number; // Amount of *this* specific payment/installment (adjusted for cumulative)
  totalPaidSoFar?: number; // Total amount paid for the whole fee
  remainingBalance?: number; // Remaining balance for the whole fee
  isInstallment?: boolean; // Added for the stamp logic
}


const FeeDashboard = () => {
  const { user } = useAuth();
  const { showSuccess, showError } = useNotifications();
  const schoolId = user?.school_ids?.[0];
  // State for summary card amounts
  const [totalCollectedAmount, setTotalCollectedAmount] = useState(0);
  const [totalOutstandingAmount, setTotalOutstandingAmount] = useState(0);
  const [totalPartiallyPaidCategoryAmount, setTotalPartiallyPaidCategoryAmount] = useState(0);

  const [monthlyCollections, setMonthlyCollections] = useState<MonthlyCollection[]>([]);
  const [upcomingInstallments, setUpcomingInstallments] = useState<UpcomingInstallment[]>([]);
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [allPayments, setAllPayments] = useState<PaymentRecord[]>([]); // Store all fetched payments
  const [academicYears, setAcademicYears] = useState<string[]>([]);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set()); // State to manage expanded rows
  const [filters, setFilters] = useState({
    search: "",
    classLevel: "",
    academicYear: "",
    status: "",
  });

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // State for receipt data
  const [receiptData, setReceiptData] = useState<ReceiptData | null>(null);

  // Export loading states
  const [isExportingPDF, setIsExportingPDF] = useState(false);
  const [isExportingCSV, setIsExportingCSV] = useState(false);

  // Declare currency variable
  const currency = "XAF";

  useEffect(() => {
    fetchFeePayments();
  }, []); // Fetch data once on component mount

  const fetchFeePayments = async () => {
    if (!schoolId) return;
    try {
      // Get populated data for display
      const data = await getFeePaymentsBySchoolId(schoolId);
      // Get unpopulated selectedFees to check for Scholarify fee
      const unpopulatedData = await getSelectedFeesBySchoolId(schoolId);
      const settings = await getSettings();
      const SCHOLARIFY_FEE: FeeSchema = {
        _id: SCHOLARIFY_FEE_ID,
        fee_type: "Scholarify Fee",
        amount: settings?.credit?.resell_price_per_credit || 3000,
        school_id: schoolId,
      };

      // Create a lookup map for unpopulated selectedFees by payment ID
      const unpopulatedFeesMap = new Map();
      unpopulatedData.forEach(payment => {
        unpopulatedFeesMap.set(payment._id, payment.selectedFees || []);
      });

      const transformed = data.map((p: FeePaymentSchema) => {
        // Ensure populated fields are treated as objects, not strings
        const studentObj = typeof p.student_id !== "string" ? p.student_id : null;
        const classLevelObj = typeof p.class_level !== "string" ? p.class_level : null;
        const schoolObj = typeof p.school_id !== "string" ? p.school_id : null;

        const studentName = studentObj
          ? `${studentObj.first_name || "Unknown"} ${studentObj.last_name || "Student"}`
          : "Unknown Student";

        const studentIdValue = studentObj?.student_id || (typeof p.student_id === "string" ? p.student_id : "N/A");

        const classLevelName = classLevelObj?.name || (typeof p.class_level === "string" ? p.class_level : "-");

        // Check if Scholarify fee is among the selected fees using unpopulated data
        const unpopulatedSelectedFees = unpopulatedFeesMap.get(p._id) || [];
        const scholarifyFeeSelected = unpopulatedSelectedFees.some((feeId: any) =>
          feeId.toString() === SCHOLARIFY_FEE_ID
        );





        // Process selected fees - filter out strings to get populated objects
        const processedSelectedFees = (p.selectedFees || []).filter((item): item is FeePopulated => typeof item !== 'string');

        // If Scholarify fee is selected but not in the populated fees, add it
        if (scholarifyFeeSelected && !processedSelectedFees.some(fee => fee._id === SCHOLARIFY_FEE_ID)) {
          processedSelectedFees.push({
            _id: SCHOLARIFY_FEE_ID,
            fee_type: SCHOLARIFY_FEE.fee_type,
            amount: SCHOLARIFY_FEE.amount
          });


        }

        return {
          _id: p._id,
          student: studentName,
          studentId: studentIdValue,
          classLevel: classLevelName,
          academicYear: p.academic_year,
          status: p.status,
          paymentMode: p.paymentMode,
          totalAmount: p.totalAmount,
          receipt: p.receipt_number,
          installments: p.installments || [], // Now p.installments should match the Installment interface
          createdAt: p.createdAt || new Date().toISOString(),
          // Pass original populated objects for receipt generation
          student_id: studentObj,
          school_id: schoolObj,
          class_level: classLevelObj,
          selectedFees: processedSelectedFees,
          selectedResources: (p.selectedResources || []).filter((item): item is SchoolResourcePopulated => typeof item !== 'string'),
          scholarshipPercentage: p.scholarshipPercentage,
        } as PaymentRecord; // Cast to PaymentRecord to ensure type compatibility
      });

      setAllPayments(transformed); // Store all payments
      getAcademicYears(transformed); // Extract academic years from all payments



      // Now calculate dashboard data from transformed payments
      calculateDashboardData(transformed, filters.academicYear);

    } catch (err) {
      console.error("Failed to load fee payments", err);
    }
  };

  const calculateDashboardData = (paymentsData: PaymentRecord[], currentAcademicYearFilter: string) => {
    // Filter payments based on current academic year filter for dashboard stats
    const currentYearPayments = paymentsData.filter(p =>
      !currentAcademicYearFilter || p.academicYear === currentAcademicYearFilter
    );

    // Calculate amounts for summary cards and pie chart
    let sumCollected = 0;
    let sumOutstanding = 0;
    let sumTotalForPartiallyPaidStatus = 0; // Sum of totalAmount for records where status is 'partially_paid'

    currentYearPayments.forEach(p => {
      if (p.status === 'paid') {
        sumCollected += p.totalAmount;
      } else if (p.status === 'pending') {
        sumOutstanding += p.totalAmount;
      } else if (p.status === 'partially_paid') {
        sumTotalForPartiallyPaidStatus += p.totalAmount; // This is the total fee for this category

        if (p.paymentMode === 'installment' && p.installments) {
          const paidInstallments = p.installments.filter(inst => inst.paid);
          const unpaidInstallments = p.installments.filter(inst => !inst.paid);

          sumCollected += paidInstallments.reduce((sum, inst) => sum + inst.amount, 0);
          sumOutstanding += unpaidInstallments.reduce((sum, inst) => sum + inst.amount, 0);
        }
      }
    });

    setTotalCollectedAmount(sumCollected);
    setTotalOutstandingAmount(sumOutstanding);
    setTotalPartiallyPaidCategoryAmount(sumTotalForPartiallyPaidStatus);

    // Calculate monthly collections
    const monthlyData: { [key: string]: number } = {};
    currentYearPayments.forEach(p => {
      if (p.status === 'paid' || p.status === 'partially_paid') {
        if (p.paymentMode === 'full' && p.status === 'paid') {
          const month = new Date(p.createdAt).toLocaleString('en-US', { month: 'short' });
          monthlyData[month] = (monthlyData[month] || 0) + p.totalAmount;
        } else if (p.paymentMode === 'installment' && p.installments) {
          p.installments.forEach(inst => {
            if (inst.paid && inst.paidAt) {
              const month = new Date(inst.paidAt).toLocaleString('en-US', { month: 'short' });
              monthlyData[month] = (monthlyData[month] || 0) + inst.amount;
            }
          });
        }
      }
    });

    const monthOrder = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
    const sortedMonthlyCollections = monthOrder
      .map(month => ({ month, amount: monthlyData[month] || 0 }))
      .filter(item => item.amount > 0); // Only show months with collections
    setMonthlyCollections(sortedMonthlyCollections);

    // Calculate upcoming installments and notifications
    const now = new Date();
    const upcoming: UpcomingInstallment[] = [];
    const newNotifications: Notification[] = [];

    currentYearPayments.forEach(p => {
      if (p.paymentMode === 'installment' && p.installments) {
        p.installments.forEach(inst => {
          if (!inst.paid) {
            const dueDate = new Date(inst.dueDate);
            upcoming.push({
              student: p.student,
              dueDate: dueDate.toLocaleDateString(),
              amount: inst.amount,
            });

            const diffTime = dueDate.getTime() - now.getTime();
            const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

            if (diffDays >= 0 && diffDays <= 7) {
              newNotifications.push({ message: `Reminder: ${p.student}'s installment of ${currency}${inst.amount.toFixed(2)} due in ${diffDays} day(s).` });
            } else if (diffDays < 0) {
              newNotifications.push({ message: `${p.student}'s installment of ${currency}${inst.amount.toFixed(2)} is overdue by ${Math.abs(diffDays)} day(s).` });
            }
          }
        });
      }
    });
    setUpcomingInstallments(upcoming.sort((a, b) => new Date(a.dueDate).getTime() - new Date(b.dueDate).getTime()));
    setNotifications(newNotifications);
  };

  useEffect(() => {
    // Recalculate dashboard data whenever filters change, especially academicYear
    calculateDashboardData(allPayments, filters.academicYear);
  }, [filters.academicYear, allPayments]); // Depend on allPayments to ensure it's loaded

  const getAcademicYears = (data: PaymentRecord[]) => {
    const years = [...new Set(data.map((item) => item.academicYear))].sort((a, b) => {
      // Sort academic years like "2023/2024" correctly
      const yearA = parseInt(a.split('/')[0]);
      const yearB = parseInt(b.split('/')[0]);
      return yearA - yearB;
    });
    setAcademicYears(years);
    if (!filters.academicYear && years.length > 0) {
      // Set the default academic year to the latest one if not already set
      setFilters((prevFilters) => ({ ...prevFilters, academicYear: years[years.length - 1] }));
    }
  };

  // Filter payments for the table based on all filter criteria
  const filteredPayments = allPayments.filter((p) => {
    return (
      (!filters.search ||
        p.student.toLowerCase().includes(filters.search.toLowerCase()) ||
        p.studentId.includes(filters.search)) &&
      (!filters.classLevel || p.classLevel === filters.classLevel) &&
      (!filters.academicYear || p.academicYear === filters.academicYear) &&
      (!filters.status || p.status === filters.status)
    );
  });

  // Pagination logic
  const totalItems = filteredPayments.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedPayments = filteredPayments.slice(startIndex, endIndex);

  // Reset to first page when filters change
  React.useEffect(() => {
    setCurrentPage(1);
  }, [filters.search, filters.classLevel, filters.academicYear, filters.status]);

  // Data for the Pie Chart, now using calculated totals from filtered payments
  const pieChartData = [
    { name: "Collected", value: totalCollectedAmount, color: "#4CAF50" }, // Green
    { name: "Outstanding", value: totalOutstandingAmount, color: "#F44336" }, // Red
  ];


  // Function to toggle expanded row
  const toggleRow = (id: string) => {
    setExpandedRows((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(id)) {
        newSet.delete(id);
      } else {
        newSet.add(id);
      }
      return newSet;
    });
  };

  // Function to mark payment/installment as paid
  const handleMarkPaymentAsPaid = async (paymentId: string, installmentId?: string) => {
    try {
      await markFeePaymentAsPaid(paymentId, installmentId);
      // Re-fetch all payments to update the dashboard with the latest data
      await fetchFeePayments();
      showSuccess("payment.marked_paid", {});
    } catch (error) {
      console.error("Failed to mark payment as paid:", error);
      showError("payment.mark_paid_failed", {});
    }
  };

  // Function to prepare data and generate PDF
  const handleViewReceipt = async (payment: PaymentRecord) => {
    if (!payment.student_id || !payment.school_id || !payment.class_level) {
      alert("Student, School, or Class data missing for receipt generation. Please ensure data is fully populated.");
      console.error("Missing populated data for receipt:", payment);
      return;
    }

    const paymentItems: ReceiptItem[] = [];

    // Add selected fees to payment items
    payment.selectedFees?.forEach(fee => {
      paymentItems.push({ description: fee.fee_type, amount: fee.amount });
    });



    // Add selected resources to payment items
    payment.selectedResources?.forEach(resource => {
      paymentItems.push({ description: resource.name, amount: resource.price });
    });





    const totalPaidSoFar = payment.installments.filter(inst => inst.paid).reduce((sum, inst) => sum + inst.amount, 0);
    const remainingBalance = payment.totalAmount - totalPaidSoFar;

    const receiptDataForPdf: ReceiptData = {
      student: {
        student_id: payment.student_id!.student_id,
        first_name: payment.student_id!.first_name,
        last_name: payment.student_id!.last_name,
        class_level: payment.class_level!.name, // Assuming class_level is populated with a 'name'
      },
      school: {
        name: payment.school_id!.name,
        logoUrl: payment.school_id!.logoUrl,
        email: payment.school_id!.email,
        address: payment.school_id!.address,
        website: payment.school_id!.website,
        phone_number: payment.school_id!.phone_number,
      },
      paymentItems: paymentItems,
      receiptId: payment.receipt,
      date: payment.createdAt,
      taxRate: 0, // Example tax rate, adjust as needed or fetch from config
      applyScholarship: (payment.scholarshipPercentage || 0) > 0,
      scholarshipPercentage: payment.scholarshipPercentage,
      isInstallment: payment.paymentMode === 'installment',
      installments: payment.paymentMode === 'installment' ? payment.installments.length : undefined,
      installmentDates: payment.paymentMode === 'installment' ? payment.installments.map(inst => new Date(inst.dueDate).toLocaleDateString()) : undefined,
      paidInstallmentNumber: payment.paymentMode === 'installment' ? payment.installments.filter(inst => inst.paid).length : undefined,
      amountPaid: payment.paymentMode === 'installment' ? totalPaidSoFar : payment.totalAmount, // Adjusted for cumulative paid for installments
      totalPaidSoFar: totalPaidSoFar,
      remainingBalance: remainingBalance,
    };

    setReceiptData(receiptDataForPdf); // Set state for potential modal/display if needed

    try {
      const receiptDoc = <ReceiptPDF
        {...receiptDataForPdf}
        school={{
          ...receiptDataForPdf.school,
          email: receiptDataForPdf.school.email || '',
          phone_number: receiptDataForPdf.school.phone_number || ''
        }}
      />;
      const blob = await pdf(receiptDoc).toBlob();
      const url = URL.createObjectURL(blob);
      const newTab = window.open(url, "_blank"); // Open URL directly in new tab
      if (!newTab) {
        alert("Popup blocked! Please allow popups to view the receipt.");
      }
    } catch (error) {
      console.error("Error generating or opening PDF:", error);
      alert("Failed to generate receipt. Please try again.");
    }
  };

  // Export functions
  const handleExportPDF = async () => {
    setIsExportingPDF(true);
    try {
      // Get current filtered data for export
      const dataToExport = filteredPayments;

      if (dataToExport.length === 0) {
        showError("export.no_data", { type: "PDF" });
        return;
      }

      // Get school information from the first payment record
      const schoolInfo = dataToExport[0]?.school_id || {
        name: "School Name",
        logoUrl: "",
        email: "",
        address: "",
        website: "",
        phone_number: ""
      };

      // Prepare data for PDF generation
      const reportData = {
        school: {
          name: schoolInfo.name || "School Name",
          logoUrl: schoolInfo.logoUrl,
          email: schoolInfo.email || "",
          address: schoolInfo.address || "",
          website: schoolInfo.website || "",
          phone_number: schoolInfo.phone_number || ""
        },
        reportTitle: `Transaction Report - ${filters.academicYear || "All Academic Years"}`,
        generatedDate: new Date().toLocaleDateString(),
        generatedTime: new Date().toLocaleTimeString(),
        filters: {
          academicYear: filters.academicYear || "All Years",
          status: filters.status || "All Statuses",
          search: filters.search || "None"
        },
        summary: {
          totalCollected: totalCollectedAmount,
          totalOutstanding: totalOutstandingAmount,
          totalPartiallyPaid: totalPartiallyPaidCategoryAmount,
          totalRecords: dataToExport.length,
          currency: currency
        },
        transactions: dataToExport.map(payment => {
          const paidAmount = payment.installments
            ? payment.installments.filter(inst => inst.paid).reduce((sum, inst) => sum + inst.amount, 0)
            : (payment.status === 'paid' ? payment.totalAmount : 0);

          const outstandingAmount = payment.totalAmount - paidAmount;

          return {
            student: payment.student,
            studentId: payment.studentId,
            classLevel: payment.classLevel,
            academicYear: payment.academicYear,
            paymentMode: payment.paymentMode,
            status: payment.status.replace('_', ' '),
            totalAmount: payment.totalAmount,
            paidAmount: paidAmount,
            outstandingAmount: outstandingAmount,
            receipt: payment.receipt,
            createdDate: new Date(payment.createdAt).toLocaleDateString(),
            installments: payment.installments || []
          };
        })
      };

      // Generate PDF using the dedicated TransactionReportPDF component
      const reportPDF = (
        <TransactionReportPDF
          school={reportData.school}
          reportTitle={reportData.reportTitle}
          generatedDate={reportData.generatedDate}
          generatedTime={reportData.generatedTime}
          filters={reportData.filters}
          summary={reportData.summary}
          transactions={reportData.transactions}
        />
      );

      // Generate PDF blob
      const blob = await pdf(reportPDF).toBlob();

      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transaction-report-${filters.academicYear || 'all-years'}-${new Date().toISOString().split('T')[0]}.pdf`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      showSuccess("export.success", {
        count: dataToExport.length.toString(),
        format: "PDF"
      });
    } catch (error) {
      console.error("Error exporting PDF:", error);
      showError("export.failed", { format: "PDF" });
    } finally {
      setIsExportingPDF(false);
    }
  };

  const handleExportCSV = async () => {
    setIsExportingCSV(true);
    try {
      // Get current filtered data for export
      const dataToExport = filteredPayments;

      if (dataToExport.length === 0) {
        showError("export.no_data", { type: "CSV" });
        return;
      }

      // Define comprehensive CSV headers
      const headers = [
        "Student Name",
        "Student ID",
        "Class Level",
        "Academic Year",
        "Payment Mode",
        "Status",
        "Total Amount (" + currency + ")",
        "Paid Amount (" + currency + ")",
        "Outstanding Amount (" + currency + ")",
        "Receipt Number",
        "Created Date",
        "Number of Installments",
        "Paid Installments",
        "Scholarship Percentage",
        "Selected Fees",
        "Selected Resources"
      ];

      // Convert data to CSV rows with enhanced details
      const rows = dataToExport.map(payment => {
        const paidAmount = payment.installments
          ? payment.installments.filter(inst => inst.paid).reduce((sum, inst) => sum + inst.amount, 0)
          : (payment.status === 'paid' ? payment.totalAmount : 0);

        const outstandingAmount = payment.totalAmount - paidAmount;
        const totalInstallments = payment.installments ? payment.installments.length : 0;
        const paidInstallments = payment.installments ? payment.installments.filter(inst => inst.paid).length : 0;

        // Get fee and resource names
        const feeNames = payment.selectedFees?.map(fee => fee.fee_type).join('; ') || 'N/A';
        const resourceNames = payment.selectedResources?.map(resource => resource.name).join('; ') || 'N/A';

        return [
          payment.student,
          payment.studentId,
          payment.classLevel,
          payment.academicYear,
          payment.paymentMode,
          payment.status.replace('_', ' '),
          payment.totalAmount.toFixed(2),
          paidAmount.toFixed(2),
          outstandingAmount.toFixed(2),
          payment.receipt,
          new Date(payment.createdAt).toLocaleDateString(),
          totalInstallments,
          paidInstallments,
          payment.scholarshipPercentage ? payment.scholarshipPercentage + '%' : '0%',
          feeNames,
          resourceNames
        ];
      });

      // Add summary row at the top
      const summaryRows = [
        ["TRANSACTION REPORT SUMMARY"],
        ["Generated on:", new Date().toLocaleDateString() + " " + new Date().toLocaleTimeString()],
        ["Academic Year Filter:", filters.academicYear || "All Years"],
        ["Status Filter:", filters.status || "All Statuses"],
        ["Search Filter:", filters.search || "None"],
        ["Total Records:", dataToExport.length.toString()],
        ["Total Collected:", currency + " " + totalCollectedAmount.toFixed(2)],
        ["Total Outstanding:", currency + " " + totalOutstandingAmount.toFixed(2)],
        ["Total Partially Paid Category:", currency + " " + totalPartiallyPaidCategoryAmount.toFixed(2)],
        [""], // Empty row for separation
        headers // Column headers
      ];

      // Create CSV content with summary and data
      const csvContent = [
        ...summaryRows.map(row => row.map(cell => `"${cell}"`).join(",")),
        ...rows.map(row => row.map(cell => `"${cell}"`).join(","))
      ].join("\n");

      // Create and download the CSV file
      const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `transaction-report-${filters.academicYear || 'all-years'}-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      showSuccess("export.success", {
        count: dataToExport.length.toString(),
        format: "CSV"
      });
    } catch (error) {
      console.error("Error exporting CSV:", error);
      showError("export.failed", { format: "CSV" });
    } finally {
      setIsExportingCSV(false);
    }
  };


  return (
    <div className="min-h-screen p-6 sm:p-8 font-inter">
      {/* Header with Title, Academic Year Dropdown, and Export Buttons */}
      <div className="flex flex-col lg:flex-row justify-between items-start lg:items-center mb-8 gap-4">
        <h1 className="text-3xl font-extrabold text-foreground flex items-center gap-3">
          <DollarSign className="text-indigo-600" size={32} />
          Fee Payment Dashboard
        </h1>

        <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
          {/* Academic Year Filter */}
          <div className="bg-widget flex items-center gap-2 bg-white p-2 rounded-lg shadow-sm border border-gray-200">
            <CalendarDays className="text-gray-500" size={20} />
            <select
              className="bg-widget text-foreground font-medium cursor-pointer border-none"
              value={filters.academicYear}
              onChange={(e) => setFilters({ ...filters, academicYear: e.target.value })}
            >
              <option value="">All Academic Years</option>
              {academicYears.map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>

          {/* Export Buttons */}
          <div className="flex items-center gap-2">
            <div className="relative group">
              <button
                onClick={handleExportPDF}
                disabled={isExportingPDF || filteredPayments.length === 0}
                className="flex items-center gap-2 px-4 py-2 bg-red-500 hover:bg-red-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg shadow-sm transition-all duration-200 text-sm font-medium"
                title="Export detailed transaction report as PDF"
              >
                {isExportingPDF ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <FileText size={16} />
                    Export PDF
                  </>
                )}
              </button>
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                Professional PDF report with summary & transaction details
              </div>
            </div>

            <div className="relative group">
              <button
                onClick={handleExportCSV}
                disabled={isExportingCSV || filteredPayments.length === 0}
                className="flex items-center gap-2 px-4 py-2 bg-green-500 hover:bg-green-600 disabled:bg-gray-400 disabled:cursor-not-allowed text-white rounded-lg shadow-sm transition-all duration-200 text-sm font-medium"
                title="Export transaction data as spreadsheet"
              >
                {isExportingCSV ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                    Exporting...
                  </>
                ) : (
                  <>
                    <FileSpreadsheet size={16} />
                    Export CSV
                  </>
                )}
              </button>
              {/* Tooltip */}
              <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-800 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                Spreadsheet format for data analysis
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md flex items-center justify-between transition-all duration-300 hover:shadow-lg">
          <div>
            <p className="text-sm font-medium text-gray-500">Total Collected</p>
            <h3 className="text-2xl font-bold text-foreground mt-1">{currency} {totalCollectedAmount.toFixed(2)}</h3>
          </div>
          <div className="p-3 bg-green-100 rounded-full">
            <ListChecks className="text-green-600" size={24} />
          </div>
        </div>

        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md flex items-center justify-between transition-all duration-300 hover:shadow-lg">
          <div>
            <p className="text-sm font-medium text-gray-500">Total Outstanding</p>
            <h3 className="text-2xl font-bold text-foreground mt-1">{currency} {totalOutstandingAmount.toFixed(2)}</h3>
          </div>
          <div className="p-3 bg-red-100 rounded-full">
            <Clock className="text-red-600" size={24} />
          </div>
        </div>

        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md flex items-center justify-between transition-all duration-300 hover:shadow-lg">
          <div>
            <p className="text-sm font-medium text-gray-500">Total Partially Paid (Category)</p>
            <h3 className="text-2xl font-bold text-foreground mt-1">{currency} {totalPartiallyPaidCategoryAmount.toFixed(2)}</h3>
          </div>
          <div className="p-3 bg-amber-100 rounded-full">
            <DollarSign className="text-amber-600" size={24} />
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-8 text-sm">
        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300 hover:shadow-lg">
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
            <PieChartIcon className="text-blue-600" size={20} />
            Collected vs Outstanding
          </h2>
          <ResponsiveContainer width="100%" height={280}>
            <RechartsPieChart>
              <Pie
                data={pieChartData} // Use pieChartData derived from filtered payments
                dataKey="value"
                nameKey="name"
                cx="50%"
                cy="50%"
                outerRadius={100}
                fill="#8884d8"
                label={({ name, percent }) => `${name} (${(percent * 100).toFixed(0)}%)`}
              >
                {pieChartData.map((entry, index) => (
                  <Cell key={`cell-${index}`} fill={entry.color} />
                ))}
              </Pie>
              <Tooltip formatter={(value) => `${currency} ${typeof value === 'number' ? value.toFixed(2) : value}`} />
              <Legend />
            </RechartsPieChart>
          </ResponsiveContainer>
        </div>

        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300 hover:shadow-lg">
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
            <BarChartIcon className="text-purple-600" size={20} />
            Monthly Collections
          </h2>
          <ResponsiveContainer width="100%" height={280}>
            <RechartsBarChart data={monthlyCollections} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
              <XAxis dataKey="month" axisLine={false} tickLine={false} />
              <YAxis axisLine={false} tickLine={false} tickFormatter={(value) => `${currency} ${value / 1000}k`} />
              <Tooltip formatter={(value) => `${currency} ${typeof value === 'number' ? value.toFixed(2) : value}`} />
              <Legend />
              <Bar dataKey="amount" fill="#6366F1" radius={[4, 4, 0, 0]} />
            </RechartsBarChart>
          </ResponsiveContainer>
        </div>
      </div>

      {/* Upcoming Installments & Notifications */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300 hover:shadow-lg">
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
            <Clock className="text-orange-500" size={20} />
            Upcoming Installments
          </h2>
          <ul className="space-y-3 max-h-48 overflow-y-auto pr-2 custom-scrollbar">
            {upcomingInstallments.length > 0 ? (
              upcomingInstallments.map((item, i) => (
                <li key={i} className="flex items-center justify-between text-base text-gray-700 dark:text-white bg-gray-50 dark:bg-gray-900 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                  <span className="font-medium">{item.student}</span>
                  <span className="text-sm text-gray-600">Due {item.dueDate} - <span className="font-semibold text-teal">{currency} {item.amount.toFixed(2)}</span></span>
                </li>
              ))
            ) : (
              <p className="text-gray-500 text-sm">No upcoming installments.</p>
            )}
          </ul>
        </div>

        <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300 hover:shadow-lg">
          <h2 className="text-xl font-semibold text-foreground mb-4 flex items-center gap-2">
            <Bell className="text-teal" size={20} />
            Notifications
          </h2>
          <ul className="space-y-3 max-h-48 overflow-y-auto pr-2 custom-scrollbar">
            {notifications.length > 0 ? (
              notifications.map((n, i) => (
                <li key={i} className="flex items-start gap-3 text-base text-gray-700 dark:text-white bg-gray-50 dark:bg-gray-900 p-3 rounded-lg border border-gray-100 dark:border-gray-700">
                  <Bell className="text-teal-500 flex-shrink-0 mt-0.5" size={18} />
                  <span>{n.message}</span>
                </li>
              ))
            ) : (
              <p className="text-gray-500 text-sm">No new notifications.</p>
            )}
          </ul>
        </div>
      </div>

      {/* Export Summary */}
      {filteredPayments.length > 0 && (
        <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-xl p-4 mb-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-800 rounded-lg">
                <Download className="text-blue-600 dark:text-blue-400" size={20} />
              </div>
              <div>
                <h3 className="font-semibold text-blue-900 dark:text-blue-100">Export Ready</h3>
                <p className="text-sm text-blue-700 dark:text-blue-300">
                  {filteredPayments.length} transaction record{filteredPayments.length !== 1 ? 's' : ''} available for export
                  {filters.academicYear && ` (${filters.academicYear})`}
                  {filters.status && ` • Status: ${filters.status.replace('_', ' ')}`}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-blue-700 dark:text-blue-300">
                <div>Collected: <span className="font-semibold">{currency} {totalCollectedAmount.toFixed(2)}</span></div>
                <div>Outstanding: <span className="font-semibold">{currency} {totalOutstandingAmount.toFixed(2)}</span></div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Payment Records Table */}
      <div className="bg-widget dark:bg-gray-800 rounded-xl p-6 shadow-md transition-all duration-300 hover:shadow-lg">
        <h2 className="text-xl font-semibold text-foreground mb-6 flex items-center gap-2">
          <Users className="text-green-600" size={20} />
          Payment Records
        </h2>

        {/* Filter Inputs */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mb-6 ">
          <div className="relative ">
            <Search className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
            <input
              className="bg-background pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500 transition-all duration-200"
              placeholder="Search student name or ID"
              value={filters.search}
              onChange={(e) => setFilters({ ...filters, search: e.target.value })}
            />
          </div>
          {/* <div className="relative bg-background">
            <GraduationCap className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
            <input
              className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg w-full focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500 transition-all duration-200"
              placeholder="Filter by Class Level"
              value={filters.classLevel}
              onChange={(e) => setFilters({ ...filters, classLevel: e.target.value })}
            />
          </div> */}
          <div className="relative">
            <Filter className="absolute left-3 top-1/2 -translate-y-1/2 text-gray-400" size={18} />
            <select
              className="pl-10 bg-background pr-4 py-2 border border-gray-300 rounded-lg w-full appearance-none focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500 transition-all duration-200 cursor-pointer"
              value={filters.status}
              onChange={(e) => setFilters({ ...filters, status: e.target.value })}
            >
              <option value="">All Statuses</option>
              <option value="paid">Paid</option>
              <option value="partially_paid">Partially Paid</option>
              <option value="pending">Pending</option>
              <option value="cancelled">Cancelled</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
              <svg className="fill-current h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z" /></svg>
            </div>
          </div>
        </div>

        {/* Payment Table */}
        <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50 dark:bg-gray-700 text-foreground">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Student</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">ID</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Class</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Year</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Mode</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Status</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Amount</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Receipt</th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium uppercase tracking-wider">Actions</th>
              </tr>
            </thead>
            <tbody className="bg-background divide-y divide-gray-200">{paginatedPayments.length > 0 ? (
                paginatedPayments.map((p) => (
                  <React.Fragment key={p._id}><tr
                      className="hover:bg-gray-50 dark:hover:bg-black cursor-pointer"
                      onClick={() => p.paymentMode === 'installment' && toggleRow(p._id)} // Only clickable if installment
                    >
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">{p.student}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{p.studentId}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{p.classLevel}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{p.academicYear}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{p.paymentMode}</td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${p.status === 'paid' ? 'bg-green-100 text-green-800' :
                          p.status === 'partially_paid' ? 'bg-yellow-100 text-yellow-800' :
                            p.status === 'pending' ? 'bg-red-100 text-red-800' :
                              'bg-gray-100 text-gray-800'
                          }`}>
                          {p.status.replace('_', ' ')}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{currency}{p.totalAmount.toFixed(2)}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{p.receipt}</td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium flex items-center justify-end gap-2">
                        {p.paymentMode === 'installment' && (
                          <button onClick={(e) => { e.stopPropagation(); toggleRow(p._id); }} className="text-indigo-600 hover:text-indigo-900">
                            {expandedRows.has(p._id) ? <ChevronUp size={18} /> : <ChevronDown size={18} />}
                          </button>
                        )}
                        <button
                          onClick={(e) => { e.stopPropagation(); handleViewReceipt(p); }}
                          className="px-3 py-1 bg-blue-500 text-white rounded-md text-xs hover:bg-blue-600 transition-colors duration-200 flex items-center gap-1"
                        >
                          <Printer size={14} /> View Receipt
                        </button>
                      </td>
                    </tr>
                    {expandedRows.has(p._id) && p.paymentMode === 'installment' && p.installments && p.installments.length > 0 && (
                      <tr>
                        <td colSpan={10} className="p-0"> {/* Span across all columns, including the new Actions */}
                          <div className="bg-gray-50 dark:bg-gray-800 p-4 border-t border-gray-200">
                            <h4 className="text-md font-semibold text-foreground mb-3">Installment Details:</h4>
                            <div className="overflow-x-auto">
                              <table className="min-w-full divide-y divide-gray-200 border border-gray-200 rounded-lg">
                                <thead className="bg-gray-100">
                                  <tr>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Installment</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Amount Owing</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Amount Paid</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Due Date</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Payment Date</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Status</th>
                                    <th scope="col" className="px-4 py-2 text-left text-xs font-medium text-gray-600 uppercase tracking-wider">Actions</th> {/* Actions for installments */}
                                  </tr>
                                </thead>
                                <tbody className="bg-white dark:bg-black divide-y divide-gray-100">{p.installments.map((inst, idx) => (
                                    <tr key={inst._id || idx} className="hover:bg-gray-50 dark:hover:bg-gray-700">
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 dark:text-white">Installment {idx + 1}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 dark:text-white">{currency}{inst.amount.toFixed(2)}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 dark:text-white">{currency}{(inst.paid ? inst.amount : 0).toFixed(2)}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 dark:text-white">{new Date(inst.dueDate).toLocaleDateString()}</td>
                                      <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-800 dark:text-white">{inst.paidAt ? new Date(inst.paidAt).toLocaleDateString() : 'N/A'}</td>
                                      <td className="px-4 py-3 whitespace-nowrap">
                                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${inst.paid ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                                          }`}>
                                          {inst.paid ? 'Paid' : 'Pending'}
                                        </span>
                                      </td>
                                      <td className="px-4 py-3 whitespace-nowrap text-right text-sm font-medium">
                                        {!inst.paid && inst._id && (
                                          <button
                                            onClick={(e) => { e.stopPropagation(); handleMarkPaymentAsPaid(p._id, inst._id!); }}
                                            className="px-3 py-1 bg-teal text-white rounded-md text-xs hover:bg-teal-600 transition-colors duration-200"
                                          >
                                            Mark Paid
                                          </button>
                                        )}
                                      </td>
                                    </tr>
                                  ))}</tbody>
                              </table>
                            </div>
                          </div>
                        </td>
                      </tr>
                    )}</React.Fragment>
                ))
              ) : (<tr>
                  <td colSpan={10} className="px-6 py-4 text-center text-sm text-gray-500">
                    {allPayments.length === 0
                      ? "No payment records found."
                      : "No payment records match the selected filters. Try adjusting your search criteria."
                    }
                  </td>
                </tr>
              )}</tbody>
          </table>
        </div>

        {/* Pagination Controls */}
        {filteredPayments.length > 0 && (
          <div className="flex flex-col sm:flex-row justify-between items-center mt-6 gap-4">
            {/* Items per page selector */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-gray-700 dark:text-white">Show</span>
              <select
                value={itemsPerPage}
                onChange={(e) => {
                  setItemsPerPage(Number(e.target.value));
                  setCurrentPage(1); // Reset to first page when changing items per page
                }}
                className="border bg-widget border-gray-300 rounded-md px-3 py-1 text-sm focus:ring-2 focus:ring-indigo-200 focus:border-indigo-500"
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
              </select>
              <span className="text-sm text-gray-700 dark:text-white">entries</span>
            </div>

            {/* Pagination info and controls */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-700 dark:text-white">
                Showing {startIndex + 1} to {Math.min(endIndex, totalItems)} of {totalItems} entries
              </span>

              <div className="flex items-center gap-1">
                <button
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className="p-2 rounded-md border border-gray-300 text-gray-500 hover:text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronLeft size={16} />
                </button>
                <span className="text-sm font-medium text-gray-700 dark:text-white">
                  Page {currentPage} of {totalPages}
                </span>
                <button
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className="p-2 rounded-md border border-gray-300 text-gray-500 hover:text-gray-700 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  <ChevronRight size={16} />
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default function Page() {
  const BASE_URL = "/school-admin";
  const navigation = {
    icon: CreditCard,
    baseHref: `${BASE_URL}/transaction`,
    title: "Transaction"
  };
  const { logout } = useAuth();
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center h-screen absolute top-0 left-0 z-50 bg-gray-50 bg-opacity-75">
          <CircularLoader size={32} color="teal" />
        </div>
      }
    >
      <SchoolLayout navigation={navigation} showGoPro={true} onLogout={() => logout()}>
        <FeeDashboard />
      </SchoolLayout>
    </Suspense>
  );
}