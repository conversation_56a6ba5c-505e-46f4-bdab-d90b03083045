import type { Metada<PERSON> } from "next";
import "./globals.css";
import { Inter } from "next/font/google";
import ThemeInitializer from "@/utils/ThemeInitializer";
import { AuthProvider } from "./services/AuthContext";
import MaintenanceModeProvider from "@/components/MaintenanceModeProvider";
import { AcademicYearProvider } from "@/context/AcademicYearContext";
import { ThemeProvider } from "@/contexts/ThemeContext";
import { CreditProvider } from "@/context/CreditContext";
// import ThemeScript from "./theme-script";

// Check environment variables on load
if (typeof window !== "undefined") {
    console.log("🔧 Environment Check:", {
        NEXT_PUBLIC_BASE_API_URL: process.env.NEXT_PUBLIC_BASE_API_URL,
        fallback: "https://backend.scholarifyltd.com/api",
    });
}

export const metadata: Metadata = {
    title: "Scholarify Admin",
    description: "Admin dashboard for Scholarify",
};
const inter = Inter({
    subsets: ["latin"],
    weight: ["400", "700"],
});

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en" suppressHydrationWarning>
            <head>
                {/* <ThemeScript /> */}
            </head>
            <body
                className={`${inter.className} transition-colors duration-300 bg-white dark:bg-gray-900`}
                suppressHydrationWarning={true}
            >
                <ThemeInitializer />
                <ThemeProvider>
                    <AuthProvider>
                        <CreditProvider>
                            <AcademicYearProvider>
                                <MaintenanceModeProvider>
                                    {children}
                                </MaintenanceModeProvider>
                            </AcademicYearProvider>
                        </CreditProvider>
                    </AuthProvider>
                </ThemeProvider>
            </body>
        </html>
    );
}
