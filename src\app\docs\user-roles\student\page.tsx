"use client";

import { useState } from 'react';
import { <PERSON>raduationCap, BookOpen, FileText, Clock, MessageSquare, ArrowRight, CheckCircle, AlertCircle, Eye, Users } from 'lucide-react';
import Link from 'next/link';

const capabilities = [
  {
    category: 'Academic Records',
    icon: FileText,
    description: 'Access your academic records and performance data',
    features: [
      'View your grades and academic performance',
      'Access attendance records and history',
      'Review assignment submissions and scores',
      'Download report cards and transcripts',
      'Track academic progress over time'
    ]
  },
  {
    category: 'Class Information',
    icon: BookOpen,
    description: 'Access information about your classes and schedule',
    features: [
      'View your class schedule and timetables',
      'Access class materials and resources',
      'See assignment due dates and requirements',
      'View class announcements and updates',
      'Access subject-specific information'
    ]
  },
  {
    category: 'Communication',
    icon: MessageSquare,
    description: 'Communicate with teachers and access school information',
    features: [
      'Send messages to teachers about assignments',
      'Receive announcements from teachers',
      'Access school-wide notifications',
      'View important school updates',
      'Communicate about academic concerns'
    ]
  },
  {
    category: 'Resource Access',
    icon: Eye,
    description: 'Access educational resources and materials',
    features: [
      'Download study materials and resources',
      'Access library resources and references',
      'View educational videos and content',
      'Access online learning tools',
      'Download assignment templates'
    ]
  },
  {
    category: 'Schedule Management',
    icon: Clock,
    description: 'Manage your academic schedule and deadlines',
    features: [
      'View class schedules and timetables',
      'Track assignment due dates',
      'Monitor exam schedules',
      'View school calendar and events',
      'Manage academic deadlines'
    ]
  },
  {
    category: 'Profile Management',
    icon: Users,
    description: 'Manage your student account and information',
    features: [
      'Update personal information',
      'Change password and security settings',
      'Manage notification preferences',
      'View account activity and history',
      'Update contact information'
    ]
  }
];

const permissions = [
  {
    module: 'Academic Records',
    permissions: [
      'view_own_grades',
      'view_own_attendance',
      'view_own_assignments',
      'download_own_reports',
      'view_own_transcripts'
    ]
  },
  {
    module: 'Classes',
    permissions: [
      'view_own_classes',
      'access_class_materials',
      'view_class_schedules',
      'view_class_announcements',
      'submit_assignments'
    ]
  },
  {
    module: 'Communication',
    permissions: [
      'send_messages_teachers',
      'receive_announcements',
      'view_notifications',
      'respond_messages',
      'schedule_meetings'
    ]
  },
  {
    module: 'Resources',
    permissions: [
      'access_educational_resources',
      'download_materials',
      'view_library_resources',
      'access_online_tools',
      'view_study_materials'
    ]
  },
  {
    module: 'Schedule',
    permissions: [
      'view_own_schedule',
      'view_exam_dates',
      'view_school_calendar',
      'track_deadlines',
      'view_events'
    ]
  },
  {
    module: 'Profile',
    permissions: [
      'update_own_profile',
      'change_password',
      'manage_preferences',
      'view_activity',
      'update_contact_info'
    ]
  }
];

const quickActions = [
  {
    title: 'View Grades',
    description: 'Check your academic performance and grades',
    href: '/student-dashboard/grades',
    icon: FileText
  },
  {
    title: 'My Classes',
    description: 'Access class information and materials',
    href: '/student-dashboard/classes',
    icon: BookOpen
  },
  {
    title: 'Assignments',
    description: 'View and submit assignments',
    href: '/student-dashboard/assignments',
    icon: Clock
  },
  {
    title: 'Communicate',
    description: 'Send messages to teachers',
    href: '/student-dashboard/communication',
    icon: MessageSquare
  }
];

export default function StudentGuide() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <GraduationCap className="h-8 w-8 text-orange-600 mr-3" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Student Guide</h1>
              <p className="text-xl text-gray-600">Complete guide for accessing your academic records and school resources</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Role Overview */}
        <div className="bg-gradient-to-r from-orange-50 to-amber-50 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-orange-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Role Overview</h2>
              <p className="text-gray-700 mb-4">
                Students have access to their academic records, class information, assignments, and educational 
                resources. This role focuses on self-service access to academic information and communication 
                with teachers for educational support.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Access Level</div>
                  <div className="text-orange-600 font-medium">Self-Service Access</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Data Access</div>
                  <div className="text-orange-600 font-medium">Own Records Only</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Communication</div>
                  <div className="text-orange-600 font-medium">With Teachers</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'overview', name: 'Overview', icon: BookOpen },
              { id: 'capabilities', name: 'Capabilities', icon: GraduationCap },
              { id: 'permissions', name: 'Permissions', icon: Eye },
              { id: 'quick-actions', name: 'Quick Actions', icon: ArrowRight }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-orange-500 text-orange-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200">
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Student Overview</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Responsibilities</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Monitor your academic progress and grades</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Access class materials and assignments</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Communicate with teachers about academic needs</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Stay informed about school events and announcements</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Manage your academic schedule and deadlines</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Areas</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Personal student dashboard</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Your academic records and grades</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Class information and materials</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Assignment submission and tracking</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Communication tools with teachers</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'capabilities' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Student Capabilities</h2>
              
              <div className="space-y-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon;
                  
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start mb-4">
                        <div className="p-2 bg-orange-50 rounded-lg mr-4">
                          <Icon className="h-6 w-6 text-orange-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{capability.category}</h3>
                          <p className="text-gray-600">{capability.description}</p>
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {capability.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Permissions</h2>
              
              <div className="space-y-6">
                {permissions.map((module, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{module.module}</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {module.permissions.map((permission, permIndex) => (
                        <div key={permIndex} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 font-mono bg-gray-100 px-2 py-1 rounded">
                            {permission}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'quick-actions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  
                  return (
                    <Link
                      key={index}
                      href={action.href}
                      className="group border border-gray-200 rounded-lg p-6 hover:border-orange-300 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          <div className="p-2 bg-orange-50 rounded-lg mr-4">
                            <Icon className="h-6 w-6 text-orange-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 group-hover:text-orange-600 transition-colors">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-orange-600 transition-colors" />
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Best Practices */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Practices</h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Check your grades and assignments regularly</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Communicate with teachers when you need help</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Keep track of assignment due dates and deadlines</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Stay informed about school events and announcements</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Documentation */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Documentation</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <Link href="/docs/user-roles/teacher" className="text-blue-600 hover:text-blue-800 text-sm">
              → Teacher Guide
            </Link>
            <Link href="/docs/user-roles/parent" className="text-blue-600 hover:text-blue-800 text-sm">
              → Parent Guide
            </Link>
            <Link href="/docs/features/academic-management" className="text-blue-600 hover:text-blue-800 text-sm">
              → Academic Management
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 