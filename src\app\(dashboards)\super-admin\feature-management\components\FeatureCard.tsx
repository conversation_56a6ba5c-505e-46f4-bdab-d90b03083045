"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  Shield, 
  Zap, 
  Crown, 
  Star, 
  Eye, 
  Edit, 
  Trash2, 
  CheckCircle, 
  XCircle,
  Code,
  Users,
  Settings
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { FeatureInfo } from '@/app/services/FeatureRegistryServices';

interface FeatureCardProps {
  feature: FeatureInfo;
  isSelected: boolean;
  onSelect: (selected: boolean) => void;
  onEdit: () => void;
  onDelete: () => void;
  viewMode: 'grid' | 'list';
}

/**
 * Composant pour afficher une carte de fonctionnalité
 */
const FeatureCard: React.FC<FeatureCardProps> = ({
  feature,
  isSelected,
  onSelect,
  onEdit,
  onDelete,
  viewMode
}) => {
  const { t } = useTranslation();

  const getSubscriptionIcon = (level: string) => {
    switch (level) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-5 h-5 text-blue-500" />;
      default:
        return <Star className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSubscriptionColor = (level: string) => {
    switch (level) {
      case 'premium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'enterprise':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'standard':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'students':
        return <Users className="w-4 h-4" />;
      case 'staff':
        return <Shield className="w-4 h-4" />;
      case 'classes':
        return <Settings className="w-4 h-4" />;
      default:
        return <Code className="w-4 h-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'active' 
      ? 'text-green-600 dark:text-green-400' 
      : 'text-red-600 dark:text-red-400';
  };

  if (viewMode === 'list') {
    return (
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 transition-all duration-200 ${
          isSelected 
            ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
            : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
        }`}
      >
        <div className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4 flex-1">
              {/* Checkbox */}
              <input
                type="checkbox"
                checked={isSelected}
                onChange={(e) => onSelect(e.target.checked)}
                className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
              />

              {/* Icône du module */}
              <div className="flex items-center space-x-2">
                {getModuleIcon(feature.module)}
                <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
                  {feature.module}
                </span>
              </div>

              {/* Nom et description */}
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 dark:text-gray-100">
                  {feature.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {feature.description}
                </p>
              </div>

              {/* ID de la fonctionnalité */}
              <div className="text-xs font-mono text-gray-400 dark:text-gray-500">
                {feature.feature_id}
              </div>

              {/* Niveau d'abonnement */}
              <div className="flex items-center space-x-2">
                {getSubscriptionIcon(feature.subscription_level)}
                <span className={`px-2 py-1 text-xs rounded-full ${getSubscriptionColor(feature.subscription_level)}`}>
                  {t(`subscription.plans.${feature.subscription_level}`, feature.subscription_level)}
                </span>
              </div>

              {/* Statut */}
              <div className="flex items-center space-x-1">
                {feature.status === 'active' ? (
                  <CheckCircle className={`w-4 h-4 ${getStatusColor(feature.status)}`} />
                ) : (
                  <XCircle className={`w-4 h-4 ${getStatusColor(feature.status)}`} />
                )}
                <span className={`text-sm ${getStatusColor(feature.status)}`}>
                  {feature.status === 'active' ? 'Actif' : 'Inactif'}
                </span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex items-center space-x-2">
              <button
                onClick={onEdit}
                className="p-2 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                title="Modifier"
              >
                <Edit className="w-4 h-4" />
              </button>
              <button
                onClick={onDelete}
                className="p-2 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
                title="Supprimer"
              >
                <Trash2 className="w-4 h-4" />
              </button>
            </div>
          </div>
        </div>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={`bg-white dark:bg-gray-800 rounded-lg shadow-sm border-2 transition-all duration-200 ${
        isSelected 
          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20' 
          : 'border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600'
      }`}
    >
      {/* Header */}
      <div className="p-4 border-b border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between mb-2">
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={isSelected}
              onChange={(e) => onSelect(e.target.checked)}
              className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
            />
            {getModuleIcon(feature.module)}
            <span className="text-sm text-gray-500 dark:text-gray-400 capitalize">
              {feature.module}
            </span>
          </div>
          
          <div className="flex items-center space-x-1">
            {feature.status === 'active' ? (
              <CheckCircle className={`w-4 h-4 ${getStatusColor(feature.status)}`} />
            ) : (
              <XCircle className={`w-4 h-4 ${getStatusColor(feature.status)}`} />
            )}
          </div>
        </div>

        <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
          {feature.name}
        </h3>
        
        <p className="text-sm text-gray-500 dark:text-gray-400 line-clamp-2">
          {feature.description}
        </p>
      </div>

      {/* Body */}
      <div className="p-4">
        <div className="space-y-3">
          {/* ID de la fonctionnalité */}
          <div>
            <span className="text-xs text-gray-400 dark:text-gray-500">ID:</span>
            <div className="text-xs font-mono text-gray-600 dark:text-gray-300 bg-gray-100 dark:bg-gray-700 px-2 py-1 rounded mt-1">
              {feature.feature_id}
            </div>
          </div>

          {/* Niveau d'abonnement */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400 dark:text-gray-500">Niveau:</span>
            <div className="flex items-center space-x-1">
              {getSubscriptionIcon(feature.subscription_level)}
              <span className={`px-2 py-1 text-xs rounded-full ${getSubscriptionColor(feature.subscription_level)}`}>
                {t(`subscription.plans.${feature.subscription_level}`, feature.subscription_level)}
              </span>
            </div>
          </div>

          {/* Catégorie */}
          <div className="flex items-center justify-between">
            <span className="text-xs text-gray-400 dark:text-gray-500">Catégorie:</span>
            <span className="text-xs text-gray-600 dark:text-gray-300 capitalize">
              {feature.category}
            </span>
          </div>

          {/* Endpoints API */}
          {feature.api_endpoints && feature.api_endpoints.length > 0 && (
            <div>
              <span className="text-xs text-gray-400 dark:text-gray-500">Endpoints:</span>
              <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                {feature.api_endpoints.length} endpoint(s)
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer */}
      <div className="px-4 py-3 bg-gray-50 dark:bg-gray-700/50 rounded-b-lg border-t border-gray-200 dark:border-gray-700">
        <div className="flex items-center justify-between">
          <span className={`text-xs ${getStatusColor(feature.status)}`}>
            {feature.status === 'active' ? 'Actif' : 'Inactif'}
          </span>
          
          <div className="flex items-center space-x-2">
            <button
              onClick={onEdit}
              className="p-1 text-gray-400 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
              title="Modifier"
            >
              <Edit className="w-4 h-4" />
            </button>
            <button
              onClick={onDelete}
              className="p-1 text-gray-400 hover:text-red-600 dark:hover:text-red-400 transition-colors"
              title="Supprimer"
            >
              <Trash2 className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>
    </motion.div>
  );
};

export default FeatureCard;
