"use client";

import React, { useState, useRef, useEffect } from 'react';
import { Search, ChevronDown, X, User } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';

interface Student {
  _id: string;
  name: string;
  student_id: string;
}

interface SearchableStudentSelectorProps {
  students: Student[];
  selectedStudentId: string;
  onStudentSelect: (studentId: string) => void;
  placeholder?: string;
  disabled?: boolean;
  error?: string;
  className?: string;
}

const SearchableStudentSelector: React.FC<SearchableStudentSelectorProps> = ({
  students,
  selectedStudentId,
  onStudentSelect,
  placeholder = "Search and select a student...",
  disabled = false,
  error,
  className = ""
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Get selected student info
  const selectedStudent = students.find(student => student._id === selectedStudentId);

  // Filter students based on search term
  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.student_id.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle clicking outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Handle keyboard navigation
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'Enter' || e.key === ' ' || e.key === 'ArrowDown') {
        e.preventDefault();
        setIsOpen(true);
        setHighlightedIndex(0);
      }
      return;
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev < filteredStudents.length - 1 ? prev + 1 : 0
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => 
          prev > 0 ? prev - 1 : filteredStudents.length - 1
        );
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredStudents[highlightedIndex]) {
          handleStudentSelect(filteredStudents[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setSearchTerm('');
        setHighlightedIndex(-1);
        break;
    }
  };

  const handleStudentSelect = (student: Student) => {
    onStudentSelect(student._id);
    setIsOpen(false);
    setSearchTerm('');
    setHighlightedIndex(-1);
  };

  const handleClearSelection = (e: React.MouseEvent) => {
    e.stopPropagation();
    onStudentSelect('');
  };

  const handleInputClick = () => {
    if (!disabled) {
      setIsOpen(!isOpen);
      if (!isOpen) {
        setTimeout(() => inputRef.current?.focus(), 100);
      }
    }
  };

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Main Input */}
      <div
        className={`relative w-full px-3 py-2 border rounded-lg cursor-pointer transition-all duration-200 bg-widget text-text ${
          disabled 
            ? 'bg-gray-100 dark:bg-gray-800 cursor-not-allowed opacity-50' 
            : 'hover:border-teal-300 focus-within:border-teal-500 focus-within:ring-2 focus-within:ring-teal-200'
        } ${
          error ? 'border-red-500' : 'border-stroke'
        } ${
          isOpen ? 'border-teal-500 ring-2 ring-teal-200' : ''
        }`}
        onClick={handleInputClick}
      >
        <div className="flex items-center justify-between">
          <div className="flex items-center flex-1 min-w-0">
            {selectedStudent ? (
              <div className="flex items-center space-x-2 flex-1 min-w-0">
                <User className="w-4 h-4 text-teal-500 flex-shrink-0" />
                <div className="flex-1 min-w-0">
                  <div className="font-medium text-sm truncate">{selectedStudent.name}</div>
                  <div className="text-xs text-gray-500 truncate">ID: {selectedStudent.student_id}</div>
                </div>
              </div>
            ) : (
              <div className="flex items-center space-x-2 text-gray-500">
                <Search className="w-4 h-4" />
                <span className="text-sm">{placeholder}</span>
              </div>
            )}
          </div>
          
          <div className="flex items-center space-x-1 ml-2">
            {selectedStudent && !disabled && (
              <button
                type="button"
                onClick={handleClearSelection}
                className="p-1 hover:bg-gray-200 dark:hover:bg-gray-700 rounded transition-colors"
              >
                <X className="w-3 h-3 text-gray-400" />
              </button>
            )}
            <ChevronDown 
              className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                isOpen ? 'rotate-180' : ''
              }`} 
            />
          </div>
        </div>
      </div>

      {/* Dropdown */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, y: -10, scale: 0.95 }}
            animate={{ opacity: 1, y: 0, scale: 1 }}
            exit={{ opacity: 0, y: -10, scale: 0.95 }}
            transition={{ duration: 0.15 }}
            className="absolute z-50 w-full mt-1 bg-widget border border-stroke rounded-lg shadow-lg max-h-60 overflow-hidden"
          >
            {/* Search Input */}
            <div className="p-3 border-b border-stroke">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                <input
                  ref={inputRef}
                  type="text"
                  value={searchTerm}
                  onChange={(e) => {
                    setSearchTerm(e.target.value);
                    setHighlightedIndex(-1);
                  }}
                  onKeyDown={handleKeyDown}
                  placeholder="Type to search students..."
                  className="w-full pl-10 pr-4 py-2 text-sm border border-stroke rounded-md focus:outline-none focus:ring-2 focus:ring-teal-200 focus:border-teal-500 bg-background text-text"
                />
              </div>
            </div>

            {/* Students List */}
            <div className="max-h-48 overflow-y-auto">
              {filteredStudents.length > 0 ? (
                filteredStudents.map((student, index) => (
                  <motion.div
                    key={student._id}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: index * 0.02 }}
                    className={`px-3 py-2 cursor-pointer transition-colors ${
                      index === highlightedIndex
                        ? 'bg-teal-50 dark:bg-teal-900/20'
                        : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                    } ${
                      student._id === selectedStudentId
                        ? 'bg-teal-100 dark:bg-teal-900/30 border-r-2 border-teal-500'
                        : ''
                    }`}
                    onClick={() => handleStudentSelect(student)}
                  >
                    <div className="flex items-center space-x-3">
                      <User className="w-4 h-4 text-teal-500 flex-shrink-0" />
                      <div className="flex-1 min-w-0">
                        <div className="font-medium text-sm text-text truncate">
                          {student.name}
                        </div>
                        <div className="text-xs text-gray-500 truncate">
                          Student ID: {student.student_id}
                        </div>
                      </div>
                      {student._id === selectedStudentId && (
                        <div className="w-2 h-2 bg-teal-500 rounded-full flex-shrink-0"></div>
                      )}
                    </div>
                  </motion.div>
                ))
              ) : (
                <div className="px-3 py-4 text-center text-gray-500 text-sm">
                  {searchTerm ? 'No students found matching your search.' : 'No students available.'}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Error Message */}
      {error && (
        <p className="mt-1 text-sm text-red-500">{error}</p>
      )}
    </div>
  );
};

export default SearchableStudentSelector;
