# Système de Contrôle d'Accès basé sur les Abonnements

Ce système permet de contrôler l'accès aux fonctionnalités de l'application en fonction du niveau d'abonnement de l'école.

## Composants Disponibles

### 1. FeatureGate

Composant qui masque automatiquement son contenu si l'utilisateur n'a pas accès à la fonctionnalité.

```tsx
import FeatureGate from '@/components/subscription/FeatureGate';

<FeatureGate featureId="students_bulk_operations">
  <button>Importer CSV</button>
</FeatureGate>

// Avec fallback personnalisé
<FeatureGate 
  featureId="students_id_card_generation"
  fallback={<div>Mise à niveau requise</div>}
>
  <IDCardGenerator />
</FeatureGate>
```

### 2. FeatureButton

Bouton intelligent qui gère automatiquement les états d'accès et affiche les prompts de mise à niveau.

```tsx
import FeatureButton from '@/components/subscription/FeatureButton';

<FeatureButton
  featureId="students_bulk_operations"
  onClick={handleImport}
  variant="primary"
>
  Importer CSV
</FeatureButton>

// Avec gestion personnalisée de la mise à niveau
<FeatureButton
  featureId="students_id_card_generation"
  onClick={handleIDCards}
  onUpgradeClick={() => window.location.href = '/upgrade'}
>
  Générer cartes d'identité
</FeatureButton>
```

### 3. SubscriptionStatus

Affiche le statut actuel de l'abonnement avec les fonctionnalités disponibles.

```tsx
import SubscriptionStatus from '@/components/subscription/SubscriptionStatus';

// Affichage complet
<SubscriptionStatus 
  showDetails={true}
  showUpgradeButton={true}
/>

// Affichage compact
<SubscriptionStatus 
  compact={true}
  className="mb-4"
/>
```

## Hooks Disponibles

### useSubscription

Hook principal pour gérer les abonnements et vérifier l'accès aux fonctionnalités.

```tsx
import { useSubscription } from '@/hooks/useSubscription';

function MyComponent() {
  const {
    subscription,
    loading,
    error,
    checkFeatureAccess,
    hasFeatureAccess,
    upgradeSubscription
  } = useSubscription();
  
  // Vérification synchrone (utilise le cache)
  const canExport = hasFeatureAccess('grades_export');
  
  // Vérification asynchrone
  const handleAction = async () => {
    const access = await checkFeatureAccess('students_bulk_operations');
    if (access.hasAccess) {
      // Exécuter l'action
    } else {
      // Afficher prompt de mise à niveau
    }
  };
}
```

## Services Disponibles

### ApiInterceptorService

Service pour gérer les appels API avec gestion automatique des erreurs d'abonnement.

```tsx
import ApiInterceptorService from '@/app/services/ApiInterceptorService';

// Appel GET simple
const data = await ApiInterceptorService.get('/api/students');

// Appel POST avec données
const result = await ApiInterceptorService.post('/api/students', studentData);

// Upload de fichier avec gestion d'erreurs
const uploadResult = await ApiInterceptorService.uploadFile(
  '/api/students/import',
  file,
  'file'
);
```

### FeatureRegistryServices

Service pour interagir avec le registre des fonctionnalités.

```tsx
import FeatureRegistryServices from '@/app/services/FeatureRegistryServices';

// Vérifier l'accès à une fonctionnalité
const access = await FeatureRegistryServices.checkFeatureAccess(
  'students_bulk_operations',
  schoolId
);

// Obtenir les fonctionnalités par niveau d'abonnement
const features = await FeatureRegistryServices.getFeaturesBySubscriptionLevel('premium');
```

## Configuration

### 1. Installation du Provider

Ajoutez le provider au niveau racine de votre application :

```tsx
import SubscriptionModalProvider from '@/components/subscription/SubscriptionModalProvider';

function App() {
  return (
    <SubscriptionModalProvider>
      {/* Votre application */}
    </SubscriptionModalProvider>
  );
}
```

### 2. Configuration des traductions

Les traductions sont automatiquement gérées via le hook `useTranslation`. Assurez-vous que les clés suivantes existent dans vos fichiers de traduction :

```json
{
  "subscription": {
    "feature_locked": "Fonctionnalité verrouillée",
    "upgrade_required": "Mise à niveau vers {plan} requise",
    "upgrade_now": "Mettre à niveau maintenant",
    "plans": {
      "basic": "Basique",
      "standard": "Standard",
      "premium": "Premium",
      "enterprise": "Entreprise"
    },
    "features": {
      "students_bulk_operations": "Opérations en lot sur les étudiants",
      "students_id_card_generation": "Génération de cartes d'identité"
    }
  }
}
```

## Niveaux d'Abonnement

### Basic
- Gestion de base des étudiants, personnel, classes
- Enregistrement des notes et présences
- Annonces simples

### Standard
- Toutes les fonctionnalités Basic
- Opérations en lot (import/export)
- Analyses avancées
- Gestion financière
- Emplois du temps

### Premium
- Toutes les fonctionnalités Standard
- Génération de documents (cartes d'identité, bulletins)
- Rapports avancés
- Export de données
- Support prioritaire

### Enterprise
- Toutes les fonctionnalités Premium
- Fonctionnalités personnalisées
- Accès API
- Support dédié

## Gestion des Erreurs

Le système gère automatiquement plusieurs types d'erreurs :

- **402 Payment Required** : Abonnement insuffisant
- **403 Forbidden** : Accès à la fonctionnalité refusé
- **404 Not Found** : Fonctionnalité non trouvée
- **429 Too Many Requests** : Limite d'utilisation dépassée

Les modales appropriées sont automatiquement affichées pour guider l'utilisateur vers la mise à niveau.

## Exemple Complet

Consultez le composant `SubscriptionExample.tsx` pour voir un exemple complet d'utilisation de tous les composants et hooks disponibles.

## Bonnes Pratiques

1. **Utilisez FeatureGate** pour masquer des sections entières
2. **Utilisez FeatureButton** pour des actions spécifiques
3. **Vérifiez l'accès côté client ET serveur** pour la sécurité
4. **Gérez les états de chargement** pendant les vérifications d'accès
5. **Fournissez des alternatives** ou des explications claires quand l'accès est refusé
