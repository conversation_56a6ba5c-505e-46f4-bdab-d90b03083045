"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Crown, Zap, Star, ArrowRight, Check } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useSubscription } from '@/hooks/useSubscription';

interface UpgradeModalProps {
  isOpen: boolean;
  onClose: () => void;
  currentPlan: string;
  requiredPlan: string;
  featureId?: string;
  message?: string;
}

interface UpgradeModalState {
  isOpen: boolean;
  props: Omit<UpgradeModalProps, 'isOpen' | 'onClose'>;
}

// État global pour la modale
let modalState: UpgradeModalState = {
  isOpen: false,
  props: {
    currentPlan: 'basic',
    requiredPlan: 'standard'
  }
};

let setModalState: React.Dispatch<React.SetStateAction<UpgradeModalState>> | null = null;

/**
 * Fonction pour afficher la modale de mise à niveau
 */
export const showUpgradeModal = (props: Omit<UpgradeModalProps, 'isOpen' | 'onClose'>) => {
  if (setModalState) {
    setModalState({
      isOpen: true,
      props
    });
  }
};

/**
 * Fonction pour fermer la modale de mise à niveau
 */
export const hideUpgradeModal = () => {
  if (setModalState) {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }));
  }
};

/**
 * Composant de modale de mise à niveau d'abonnement
 */
const UpgradeModal: React.FC<UpgradeModalProps> = ({
  isOpen,
  onClose,
  currentPlan,
  requiredPlan,
  featureId,
  message
}) => {
  const { t } = useTranslation();
  const { upgradeSubscription } = useSubscription();
  const [isUpgrading, setIsUpgrading] = useState(false);

  const getPlanIcon = (plan: string) => {
    switch (plan) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-8 h-8 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-8 h-8 text-blue-500" />;
      default:
        return <Star className="w-8 h-8 text-gray-500" />;
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      case 'enterprise':
        return 'from-purple-500 to-pink-500';
      case 'standard':
        return 'from-blue-500 to-cyan-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getBenefits = (fromPlan: string, toPlan: string) => {
    const benefitKey = `${fromPlan}_to_${toPlan}`;
    const result = t(`subscription.benefits.${benefitKey}`, { returnObjects: "true" });
    return Array.isArray(result) ? result : [];
  };

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      await upgradeSubscription(requiredPlan);
      onClose();
      // Optionnel: afficher un message de succès
    } catch (error) {
      console.error('Erreur lors de la mise à niveau:', error);
      // Optionnel: afficher un message d'erreur
    } finally {
      setIsUpgrading(false);
    }
  };

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          >
            {/* Header */}
            <div className={`p-6 bg-gradient-to-r ${getPlanColor(requiredPlan)} text-white`}>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  {getPlanIcon(requiredPlan)}
                  <div>
                    <h3 className="text-xl font-bold">
                      {t('subscription.upgrade_required', { plan: t(`subscription.plans.${requiredPlan}`) })}
                    </h3>
                    <p className="text-white/80 text-sm">
                      {t(`subscription.plans.${currentPlan}`)} → {t(`subscription.plans.${requiredPlan}`)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Message */}
              {message && (
                <div className="mb-4 p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <p className="text-sm text-blue-800 dark:text-blue-200">
                    {message}
                  </p>
                </div>
              )}

              {/* Feature info */}
              {featureId && (
                <div className="mb-4">
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {t('subscription.feature_requires_upgrade', { 
                      plan: t(`subscription.plans.${requiredPlan}`) 
                    })}
                  </p>
                  <p className="font-medium text-gray-900 dark:text-gray-100 mt-1">
                    {t(`subscription.features.${featureId}`)}
                  </p>
                </div>
              )}

              {/* Benefits */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  {t('subscription.benefits.title', 'Avantages de la mise à niveau')}:
                </h4>
                <ul className="space-y-2">
                  {getBenefits(currentPlan, requiredPlan).map((benefit, index) => (
                    <li key={index} className="flex items-center space-x-2">
                      <Check className="w-4 h-4 text-green-500 flex-shrink-0" />
                      <span className="text-sm text-gray-700 dark:text-gray-300">
                        {benefit}
                      </span>
                    </li>
                  ))}
                </ul>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {t('common.cancel')}
                </button>
                <button
                  onClick={handleUpgrade}
                  disabled={isUpgrading}
                  className={`flex-1 px-4 py-2 bg-gradient-to-r ${getPlanColor(requiredPlan)} text-white rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center space-x-2 disabled:opacity-50`}
                >
                  {isUpgrading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <span>{t('subscription.upgrade_now')}</span>
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

/**
 * Provider pour la modale de mise à niveau
 */
export const UpgradeModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<UpgradeModalState>(modalState);

  // Enregistrer la fonction setState globalement
  React.useEffect(() => {
    setModalState = setState;
    return () => {
      setModalState = null;
    };
  }, []);

  return (
    <>
      {children}
      <UpgradeModal
        isOpen={state.isOpen}
        onClose={hideUpgradeModal}
        {...state.props}
      />
    </>
  );
};

export default UpgradeModal;
