"use client";

import { useState, useMemo } from 'react';
import { Search, BookOpen, Users, Shield, GraduationCap, UserCheck, Settings, HelpCircle, ArrowRight, Filter, X, Zap, Star, Clock, MessageCircle } from 'lucide-react';
import Link from 'next/link';

interface DocSection {
  id: string;
  title: string;
  description: string;
  icon: any;
  category: 'onboarding' | 'user-roles' | 'features' | 'security' | 'troubleshooting';
  tags: string[];
  href: string;
  priority?: 'high' | 'medium' | 'low';
}

const docSections: DocSection[] = [
  // Onboarding
  {
    id: 'getting-started',
    title: 'Getting Started',
    description: 'Complete setup guide for new users. Learn how to configure your school, invite users, and start using Scholarify.',
    icon: BookOpen,
    category: 'onboarding',
    tags: ['setup', 'configuration', 'first-time', 'installation'],
    href: '/docs/getting-started',
    priority: 'high'
  },
  {
    id: 'school-setup',
    title: 'School Setup Guide',
    description: 'Step-by-step instructions to configure your school profile, classes, subjects, and academic calendar.',
    icon: Settings,
    category: 'onboarding',
    tags: ['school', 'profile', 'classes', 'subjects', 'calendar'],
    href: '/docs/getting-started/school-setup',
    priority: 'high'
  },
  {
    id: 'user-invitation',
    title: 'User Invitation & Onboarding',
    description: 'Learn how to invite teachers, parents, and students to your school platform.',
    icon: UserCheck,
    category: 'onboarding',
    tags: ['invite', 'users', 'registration', 'accounts'],
    href: '/docs/getting-started/user-invitation',
    priority: 'high'
  },

  // User Roles - Removed Super Admin
  {
    id: 'school-admin',
    title: 'School Administrator Guide',
    description: 'Comprehensive guide for school administrators managing their institution.',
    icon: Users,
    category: 'user-roles',
    tags: ['school-admin', 'administration', 'management', 'school-leadership'],
    href: '/docs/user-roles/school-admin',
    priority: 'high'
  },
  {
    id: 'teacher',
    title: 'Teacher Guide',
    description: 'Complete guide for teachers on managing classes, grades, attendance, and parent communication.',
    icon: GraduationCap,
    category: 'user-roles',
    tags: ['teacher', 'classes', 'grades', 'attendance', 'communication'],
    href: '/docs/user-roles/teacher',
    priority: 'high'
  },
  {
    id: 'parent',
    title: 'Parent Guide',
    description: 'Guide for parents to monitor their children\'s progress, communicate with teachers, and manage payments.',
    icon: UserCheck,
    category: 'user-roles',
    tags: ['parent', 'child-progress', 'communication', 'payments'],
    href: '/docs/user-roles/parent',
    priority: 'medium'
  },
  {
    id: 'student',
    title: 'Student Guide',
    description: 'Guide for students to access their academic records, assignments, and school resources.',
    icon: GraduationCap,
    category: 'user-roles',
    tags: ['student', 'academic-records', 'assignments', 'resources'],
    href: '/docs/user-roles/student',
    priority: 'medium'
  },

  // Features
  {
    id: 'academic-management',
    title: 'Academic Management',
    description: 'Learn about managing grades, attendance, timetables, and academic records.',
    icon: BookOpen,
    category: 'features',
    tags: ['grades', 'attendance', 'timetable', 'academic-records'],
    href: '/docs/features/academic-management',
    priority: 'high'
  },
  {
    id: 'financial-management',
    title: 'Financial Management',
    description: 'Guide to managing school fees, payments, and financial reporting.',
    icon: Settings,
    category: 'features',
    tags: ['fees', 'payments', 'finance', 'reporting'],
    href: '/docs/features/financial-management',
    priority: 'medium'
  },
  {
    id: 'communication',
    title: 'Communication Tools',
    description: 'Learn about announcements, messaging, and parent-teacher communication features.',
    icon: MessageCircle,
    category: 'features',
    tags: ['communication', 'announcements', 'messaging', 'notifications'],
    href: '/docs/features/communication',
    priority: 'high'
  },
  {
    id: 'reports-analytics',
    title: 'Reports & Analytics',
    description: 'Comprehensive guide to generating reports and analyzing school data.',
    icon: BookOpen,
    category: 'features',
    tags: ['reports', 'analytics', 'data', 'insights'],
    href: '/docs/features/reports-analytics',
    priority: 'medium'
  },

  // Security
  {
    id: 'permissions',
    title: 'User Permissions & Access Control',
    description: 'Detailed guide on role-based permissions and access control in Scholarify.',
    icon: Shield,
    category: 'security',
    tags: ['permissions', 'access-control', 'security', 'roles'],
    href: '/docs/security/permissions',
    priority: 'high'
  },
  {
    id: 'data-security',
    title: 'Data Security & Privacy',
    description: 'Learn about data protection, privacy policies, and security measures.',
    icon: Shield,
    category: 'security',
    tags: ['security', 'privacy', 'data-protection', 'compliance'],
    href: '/docs/security/data-security',
    priority: 'high'
  },

  // Troubleshooting
  {
    id: 'common-issues',
    title: 'Common Issues & Solutions',
    description: 'Troubleshooting guide for frequently encountered problems and their solutions.',
    icon: HelpCircle,
    category: 'troubleshooting',
    tags: ['troubleshooting', 'issues', 'solutions', 'help'],
    href: '/docs/troubleshooting/common-issues',
    priority: 'medium'
  },
  {
    id: 'support',
    title: 'Getting Help & Support',
    description: 'How to contact support, submit tickets, and get assistance when needed.',
    icon: HelpCircle,
    category: 'troubleshooting',
    tags: ['support', 'help', 'contact', 'tickets'],
    href: '/docs/troubleshooting/support',
    priority: 'medium'
  }
];

const categories = [
  { id: 'onboarding', name: 'Onboarding', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300', icon: BookOpen },
  { id: 'user-roles', name: 'User Roles', color: 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300', icon: Users },
  { id: 'features', name: 'Features', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900/30 dark:text-purple-300', icon: Zap },
  { id: 'security', name: 'Security', color: 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300', icon: Shield },
  { id: 'troubleshooting', name: 'Troubleshooting', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300', icon: HelpCircle }
];

export default function DocsPage() {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [selectedPriority, setSelectedPriority] = useState<string>('all');

  const filteredSections = useMemo(() => {
    return docSections.filter(section => {
      const matchesSearch = searchQuery === '' || 
        section.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        section.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        section.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
      
      const matchesCategory = selectedCategory === 'all' || section.category === selectedCategory;
      const matchesPriority = selectedPriority === 'all' || section.priority === selectedPriority;
      
      return matchesSearch && matchesCategory && matchesPriority;
    });
  }, [searchQuery, selectedCategory, selectedPriority]);

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSelectedPriority('all');
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'medium': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'low': return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Enhanced Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
          <div className="text-center">
            <div className="inline-flex items-center px-4 py-2 bg-teal-100 dark:bg-teal-900/50 text-teal-800 dark:text-teal-100 rounded-full text-sm font-medium mb-6 border border-teal-200 dark:border-teal-800">
              <BookOpen className="w-4 h-4 mr-2" />
              Complete Documentation
            </div>
            <h1 className="text-4xl md:text-6xl font-bold text-gray-900 dark:text-white mb-6">
              Scholarify Documentation
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto leading-relaxed">
              Everything you need to successfully use Scholarify. Find guides, tutorials, and solutions for school administrators, teachers, parents, and students.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Enhanced Search and Filters */}
        <div className="mb-8">
          <div className="flex flex-col lg:flex-row gap-4 mb-6">
            {/* Enhanced Search Bar */}
            <div className="flex-1 relative">
              <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search documentation, features, user roles, or troubleshooting..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="w-full pl-12 pr-4 py-4 border border-gray-300 dark:border-gray-600 rounded-xl focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white text-lg"
              />
            </div>
            
            {/* Enhanced Filters */}
            <div className="flex flex-col sm:flex-row gap-3">
              <div className="flex items-center gap-2">
                <Filter className="h-5 w-5 text-gray-500" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">All Categories</option>
                  {categories.map(category => (
                    <option key={category.id} value={category.id}>
                      {category.name}
                    </option>
                  ))}
                </select>
              </div>
              
              <div className="flex items-center gap-2">
                <Star className="h-5 w-5 text-gray-500" />
                <select
                  value={selectedPriority}
                  onChange={(e) => setSelectedPriority(e.target.value)}
                  className="border border-gray-300 dark:border-gray-600 rounded-lg px-4 py-3 focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white"
                >
                  <option value="all">All Priorities</option>
                  <option value="high">High Priority</option>
                  <option value="medium">Medium Priority</option>
                  <option value="low">Low Priority</option>
                </select>
              </div>
            </div>
          </div>

          {/* Active Filters Display */}
          {(searchQuery || selectedCategory !== 'all' || selectedPriority !== 'all') && (
            <div className="flex items-center gap-2 flex-wrap">
              <span className="text-sm text-gray-600 dark:text-gray-300">Active filters:</span>
              {searchQuery && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300 text-sm rounded-full">
                  Search: "{searchQuery}"
                  <button
                    onClick={() => setSearchQuery('')}
                    className="ml-1 hover:bg-blue-200 dark:hover:bg-blue-800 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {selectedCategory !== 'all' && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300 text-sm rounded-full">
                  Category: {categories.find(c => c.id === selectedCategory)?.name}
                  <button
                    onClick={() => setSelectedCategory('all')}
                    className="ml-1 hover:bg-green-200 dark:hover:bg-green-800 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              {selectedPriority !== 'all' && (
                <span className="inline-flex items-center gap-1 px-3 py-1 bg-yellow-100 dark:bg-yellow-900/30 text-yellow-800 dark:text-yellow-300 text-sm rounded-full">
                  Priority: {selectedPriority.charAt(0).toUpperCase() + selectedPriority.slice(1)}
                  <button
                    onClick={() => setSelectedPriority('all')}
                    className="ml-1 hover:bg-yellow-200 dark:hover:bg-yellow-800 rounded-full p-0.5"
                  >
                    <X className="h-3 w-3" />
                  </button>
                </span>
              )}
              <button
                onClick={clearFilters}
                className="text-sm text-teal-600 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 underline"
              >
                Clear all filters
              </button>
            </div>
          )}
        </div>

        {/* Quick Start Guide */}
        <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-xl p-8 mb-8 border border-teal-200 dark:border-teal-800">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <Zap className="w-6 h-6 mr-3 text-teal-600 dark:text-teal-300" />
            Quick Start Guide
          </h2>
          <div className="grid md:grid-cols-3 gap-6">
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-teal-200 dark:border-teal-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mr-3">
                  <span className="text-teal-600 dark:text-teal-300 font-bold">1</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">School Setup</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">Configure your school profile and basic settings</p>
              <Link href="/docs/getting-started/school-setup" className="text-teal-600 dark:text-teal-300 hover:text-teal-700 dark:hover:text-teal-200 text-sm font-medium flex items-center">
                Get Started
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-teal-200 dark:border-teal-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mr-3">
                  <span className="text-teal-600 dark:text-teal-300 font-bold">2</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Invite Users</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">Add teachers, parents, and students to your platform</p>
              <Link href="/docs/getting-started/user-invitation" className="text-teal-600 dark:text-teal-300 hover:text-teal-700 dark:hover:text-teal-200 text-sm font-medium flex items-center">
                Learn More
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
            <div className="bg-white dark:bg-gray-800 rounded-lg p-6 border border-teal-200 dark:border-teal-700 hover:shadow-lg transition-all duration-200">
              <div className="flex items-center mb-3">
                <div className="w-8 h-8 bg-teal-100 dark:bg-teal-900/30 rounded-full flex items-center justify-center mr-3">
                  <span className="text-teal-600 dark:text-teal-300 font-bold">3</span>
                </div>
                <h3 className="font-semibold text-gray-900 dark:text-white">Start Using</h3>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">Begin managing classes, grades, and communication</p>
              <Link href="/docs/features/academic-management" className="text-teal-600 dark:text-teal-300 hover:text-teal-700 dark:hover:text-teal-200 text-sm font-medium flex items-center">
                Explore Features
                <ArrowRight className="w-4 h-4 ml-1" />
              </Link>
            </div>
          </div>
        </div>

        {/* User Roles Overview - Updated without Super Admin */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <Users className="w-6 h-6 mr-3 text-teal-600 dark:text-teal-300" />
            User Roles & Permissions
          </h2>
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {[
              {
                role: 'School Admin',
                description: 'Manages a single school and its operations',
                permissions: ['School management', 'Staff oversight', 'Academic administration'],
                color: 'bg-blue-50 border-blue-200 dark:bg-blue-900/20 dark:border-blue-700',
                icon: Users
              },
              {
                role: 'Teacher',
                description: 'Manages classes, grades, and student communication',
                permissions: ['Class management', 'Grade entry', 'Parent communication'],
                color: 'bg-green-50 border-green-200 dark:bg-green-900/20 dark:border-green-700',
                icon: GraduationCap
              },
              {
                role: 'Parent',
                description: 'Monitors child progress and communicates with school',
                permissions: ['View child progress', 'Communication', 'Fee payments'],
                color: 'bg-purple-50 border-purple-200 dark:bg-purple-900/20 dark:border-purple-700',
                icon: UserCheck
              },
              {
                role: 'Student',
                description: 'Accesses academic records and school resources',
                permissions: ['View grades', 'Access resources', 'Submit assignments'],
                color: 'bg-orange-50 border-orange-200 dark:bg-orange-900/20 dark:border-orange-700',
                icon: BookOpen
              }
            ].map((role, index) => (
              <div key={index} className={`${role.color} rounded-lg p-6 border hover:shadow-lg transition-all duration-200`}>
                <div className="flex items-center mb-3">
                  <role.icon className="w-6 h-6 text-teal-600 dark:text-teal-300 mr-2" />
                  <h3 className="font-semibold text-gray-900 dark:text-white">{role.role}</h3>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-300 mb-3">{role.description}</p>
                <ul className="text-xs text-gray-700 dark:text-gray-200 space-y-1">
                  {role.permissions.map((permission, pIndex) => (
                    <li key={pIndex} className="flex items-center">
                      <div className="w-1 h-1 bg-teal-400 rounded-full mr-2"></div>
                      {permission}
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </div>

        {/* Documentation Sections */}
        <div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6 flex items-center">
            <BookOpen className="w-6 h-6 mr-3 text-teal-600 dark:text-teal-300" />
            Documentation ({filteredSections.length} articles)
          </h2>
          
          {filteredSections.length === 0 ? (
            <div className="text-center py-16">
              <Search className="h-16 w-16 text-gray-400 dark:text-gray-500 mx-auto mb-4" />
              <h3 className="text-xl font-medium text-gray-900 dark:text-white mb-2">No results found</h3>
              <p className="text-gray-600 dark:text-gray-400 mb-6 max-w-md mx-auto">
                Try adjusting your search terms or filters to find what you're looking for.
              </p>
              <button
                onClick={clearFilters}
                className="text-teal-600 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 font-medium"
              >
                Clear all filters
              </button>
            </div>
          ) : (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-6">
              {filteredSections.map((section) => {
                const category = categories.find(c => c.id === section.category);
                const Icon = section.icon;
                
                return (
                  <Link
                    key={section.id}
                    href={section.href}
                    className="group bg-white dark:bg-gray-800 rounded-xl border border-gray-200 dark:border-gray-700 p-6 hover:border-teal-300 dark:hover:border-teal-600 hover:shadow-lg transition-all duration-200 hover:-translate-y-1"
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="p-3 bg-teal-50 dark:bg-teal-900/30 rounded-lg">
                        <Icon className="h-6 w-6 text-teal-600 dark:text-teal-300" />
                      </div>
                      <div className="flex flex-col gap-2">
                        {category && (
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${category.color}`}>
                            {category.name}
                          </span>
                        )}
                        {section.priority && (
                          <span className={`px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(section.priority)}`}>
                            {section.priority.charAt(0).toUpperCase() + section.priority.slice(1)}
                          </span>
                        )}
                      </div>
                    </div>
                    
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-3 group-hover:text-teal-600 dark:group-hover:text-teal-300 transition-colors">
                      {section.title}
                    </h3>
                    
                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 line-clamp-3">
                      {section.description}
                    </p>
                    
                    <div className="flex items-center justify-between">
                      <div className="flex flex-wrap gap-1">
                        {section.tags.slice(0, 3).map((tag, index) => (
                          <span
                            key={index}
                            className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full"
                          >
                            {tag}
                          </span>
                        ))}
                        {section.tags.length > 3 && (
                          <span className="px-2 py-1 bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-300 text-xs rounded-full">
                            +{section.tags.length - 3}
                          </span>
                        )}
                      </div>
                      <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-teal-600 dark:group-hover:text-teal-300 transition-colors" />
                    </div>
                  </Link>
                );
              })}
            </div>
          )}
        </div>

        {/* Enhanced Help Section */}
        <div className="mt-12 bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-xl p-8 border border-teal-200 dark:border-teal-800">
          <div className="text-center">
            <HelpCircle className="h-16 w-16 text-teal-600 dark:text-teal-300 mx-auto mb-4" />
            <h3 className="text-2xl font-semibold text-gray-900 dark:text-white mb-3">Need Help?</h3>
            <p className="text-gray-600 dark:text-gray-300 mb-6 max-w-2xl mx-auto">
              Can't find what you're looking for? Our support team is here to help you succeed with Scholarify.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                href="/contact"
                className="inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-lg text-white bg-teal-600 hover:bg-teal-700 transition-colors"
              >
                <MessageCircle className="w-5 h-5 mr-2" />
                Contact Support
              </Link>
              <Link
                href="/docs/troubleshooting/common-issues"
                className="inline-flex items-center justify-center px-6 py-3 border border-gray-300 dark:border-gray-600 text-base font-medium rounded-lg text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <HelpCircle className="w-5 h-5 mr-2" />
                Common Issues
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
