import React from 'react';
import { Document, Page, Text, View, StyleSheet, Image } from '@react-pdf/renderer';

// Helper function to truncate text
const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength - 3) + '...';
};

// Define interfaces for the PDF data
interface SchoolInfo {
  name: string;
  logoUrl?: string;
  email?: string;
  address?: string;
  website?: string;
  phone_number?: string;
}

interface TransactionSummary {
  totalCollected: number;
  totalOutstanding: number;
  totalPartiallyPaid: number;
  totalRecords: number;
  currency: string;
}

interface TransactionRecord {
  student: string;
  studentId: string;
  classLevel: string;
  academicYear: string;
  paymentMode: string;
  status: string;
  totalAmount: number;
  paidAmount: number;
  outstandingAmount: number;
  receipt: string;
  createdDate: string;
}

interface TransactionReportPDFProps {
  school: SchoolInfo;
  reportTitle: string;
  generatedDate: string;
  generatedTime: string;
  filters: {
    academicYear: string;
    status: string;
    search: string;
  };
  summary: TransactionSummary;
  transactions: TransactionRecord[];
}

// Create styles
const styles = StyleSheet.create({
  page: {
    flexDirection: 'column',
    backgroundColor: '#FFFFFF',
    padding: 30,
    fontSize: 10,
    fontFamily: 'Helvetica',
  },
  header: {
    flexDirection: 'row',
    marginBottom: 20,
    alignItems: 'center',
  },
  logo: {
    width: 60,
    height: 60,
    marginRight: 15,
  },
  schoolInfo: {
    flex: 1,
  },
  schoolName: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 5,
    color: '#1f2937',
  },
  schoolDetails: {
    fontSize: 9,
    color: '#6b7280',
    marginBottom: 2,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 10,
    color: '#1f2937',
  },
  subtitle: {
    fontSize: 10,
    textAlign: 'center',
    marginBottom: 20,
    color: '#6b7280',
  },
  section: {
    marginBottom: 15,
  },
  sectionTitle: {
    fontSize: 12,
    fontWeight: 'bold',
    marginBottom: 8,
    color: '#374151',
    backgroundColor: '#f3f4f6',
    padding: 5,
  },
  row: {
    flexDirection: 'row',
    marginBottom: 3,
  },
  label: {
    width: 120,
    fontSize: 9,
    color: '#6b7280',
  },
  value: {
    flex: 1,
    fontSize: 9,
    color: '#1f2937',
  },
  table: {
    marginTop: 10,
  },
  tableHeader: {
    flexDirection: 'row',
    backgroundColor: '#f9fafb',
    padding: 4,
    borderBottom: '1px solid #e5e7eb',
    minHeight: 25,
    alignItems: 'center',
  },
  tableRow: {
    flexDirection: 'row',
    padding: 3,
    borderBottom: '1px solid #f3f4f6',
    minHeight: 20,
    alignItems: 'center',
  },
  tableCell: {
    fontSize: 7,
    color: '#374151',
    paddingRight: 3,
    paddingLeft: 2,
    paddingVertical: 2,
    textOverflow: 'ellipsis',
    overflow: 'hidden',
  },
  tableCellHeader: {
    fontSize: 7,
    fontWeight: 'bold',
    color: '#1f2937',
    paddingRight: 3,
    paddingLeft: 2,
    paddingVertical: 3,
  },
  col1: { width: '18%', minWidth: 60 }, // Student Name
  col2: { width: '12%', minWidth: 40 }, // Student ID
  col3: { width: '10%', minWidth: 35 }, // Class
  col4: { width: '8%', minWidth: 30 },  // Year
  col5: { width: '8%', minWidth: 30 },  // Mode
  col6: { width: '10%', minWidth: 35 }, // Status
  col7: { width: '11%', minWidth: 40 }, // Total
  col8: { width: '11%', minWidth: 40 }, // Paid
  col9: { width: '12%', minWidth: 45 }, // Outstanding
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    fontSize: 8,
    color: '#9ca3af',
  },
});

const TransactionReportPDF: React.FC<TransactionReportPDFProps> = ({
  school,
  reportTitle,
  generatedDate,
  generatedTime,
  filters,
  summary,
  transactions,
}) => (
  <Document>
    <Page size="A4" style={styles.page}>
      {/* Header */}
      <View style={styles.header}>
        {school.logoUrl && (
          <Image style={styles.logo} src={school.logoUrl} />
        )}
        <View style={styles.schoolInfo}>
          <Text style={styles.schoolName}>{truncateText(school.name, 40)}</Text>
          {school.address && <Text style={styles.schoolDetails}>{truncateText(school.address, 60)}</Text>}
          {school.email && <Text style={styles.schoolDetails}>Email: {truncateText(school.email, 35)}</Text>}
          {school.phone_number && <Text style={styles.schoolDetails}>Phone: {truncateText(school.phone_number, 20)}</Text>}
        </View>
      </View>

      {/* Title */}
      <Text style={styles.title}>{reportTitle}</Text>
      <Text style={styles.subtitle}>
        Generated on {generatedDate} at {generatedTime}
      </Text>

      {/* Filters Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Report Filters</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Academic Year:</Text>
          <Text style={styles.value}>{filters.academicYear}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Status Filter:</Text>
          <Text style={styles.value}>{filters.status}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Search Filter:</Text>
          <Text style={styles.value}>{filters.search}</Text>
        </View>
      </View>

      {/* Summary Section */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Financial Summary</Text>
        <View style={styles.row}>
          <Text style={styles.label}>Total Collected:</Text>
          <Text style={styles.value}>{summary.currency} {summary.totalCollected.toFixed(2)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Total Outstanding:</Text>
          <Text style={styles.value}>{summary.currency} {summary.totalOutstanding.toFixed(2)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Total Partially Paid:</Text>
          <Text style={styles.value}>{summary.currency} {summary.totalPartiallyPaid.toFixed(2)}</Text>
        </View>
        <View style={styles.row}>
          <Text style={styles.label}>Total Records:</Text>
          <Text style={styles.value}>{summary.totalRecords}</Text>
        </View>
      </View>

      {/* Transactions Table */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Transaction Records</Text>
        <View style={styles.table}>
          {/* Table Header */}
          <View style={styles.tableHeader}>
            <Text style={[styles.tableCellHeader, styles.col1]}>Student</Text>
            <Text style={[styles.tableCellHeader, styles.col2]}>ID</Text>
            <Text style={[styles.tableCellHeader, styles.col3]}>Class</Text>
            <Text style={[styles.tableCellHeader, styles.col4]}>Year</Text>
            <Text style={[styles.tableCellHeader, styles.col5]}>Mode</Text>
            <Text style={[styles.tableCellHeader, styles.col6]}>Status</Text>
            <Text style={[styles.tableCellHeader, styles.col7]}>Total</Text>
            <Text style={[styles.tableCellHeader, styles.col8]}>Paid</Text>
            <Text style={[styles.tableCellHeader, styles.col9]}>Outstanding</Text>
          </View>
          
          {/* Table Rows */}
          {transactions.slice(0, 25).map((transaction, index) => (
            <View key={index} style={styles.tableRow}>
              <Text style={[styles.tableCell, styles.col1]}>{truncateText(transaction.student, 20)}</Text>
              <Text style={[styles.tableCell, styles.col2]}>{truncateText(transaction.studentId, 12)}</Text>
              <Text style={[styles.tableCell, styles.col3]}>{truncateText(transaction.classLevel, 10)}</Text>
              <Text style={[styles.tableCell, styles.col4]}>{truncateText(transaction.academicYear, 9)}</Text>
              <Text style={[styles.tableCell, styles.col5]}>{truncateText(transaction.paymentMode, 8)}</Text>
              <Text style={[styles.tableCell, styles.col6]}>{truncateText(transaction.status, 10)}</Text>
              <Text style={[styles.tableCell, styles.col7]}>{summary.currency} {transaction.totalAmount.toFixed(0)}</Text>
              <Text style={[styles.tableCell, styles.col8]}>{summary.currency} {transaction.paidAmount.toFixed(0)}</Text>
              <Text style={[styles.tableCell, styles.col9]}>{summary.currency} {transaction.outstandingAmount.toFixed(0)}</Text>
            </View>
          ))}
        </View>
        
        {transactions.length > 25 && (
          <Text style={{ fontSize: 8, color: '#6b7280', marginTop: 10, textAlign: 'center' }}>
            Showing first 25 records of {transactions.length} total records. 
            For complete data, please use the CSV export.
          </Text>
        )}
      </View>

      {/* Footer */}
      <Text style={styles.footer}>
        Transaction Report - {school.name} - Page 1
      </Text>
    </Page>
  </Document>
);

export default TransactionReportPDF;
