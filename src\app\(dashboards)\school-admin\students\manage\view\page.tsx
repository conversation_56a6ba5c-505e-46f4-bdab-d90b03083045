"use client";

import React, { ChangeEvent, useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import { deleteStudent, getStudentById, updateStudent, uploadStudentAvatar } from "@/app/services/StudentServices";
import { deleteSchool, getSchoolBy_id, getSchools } from "@/app/services/SchoolServices";
import { getUserBy_id, verifyPassword } from "@/app/services/UserServices";
import { StudentSchema, StudentUpdateSchema } from "@/app/models/StudentModel";
import { SchoolSchema } from "@/app/models/SchoolModel";
import { UserSchema } from "@/app/models/UserModel";
import CircularLoader from "@/components/widgets/CircularLoader";
import SuperLayout from "@/components/Dashboard/Layouts/SuperLayout";
import NotificationCard from "@/components/NotificationCard";
import { ArrowLeft, UploadCloud } from "lucide-react";
import useAuth from "@/app/hooks/useAuth";
import { useRouter } from "next/navigation";


import Link from "next/link";
import { motion } from "framer-motion";
import { getClasses } from "@/app/services/ClassServices";
import { ClassSchema } from "@/app/models/ClassModel";
import DeleteStudentModal from "@/app/(dashboards)/super-admin/students/components/DeleteStudentModal";
import UpdateStudentModal from "@/app/(dashboards)/super-admin/students/components/UpdateStudentModal";
import SchoolLayout from "@/components/Dashboard/Layouts/SchoolLayout";
import BackButton from "@/components/BackButton";
import ActionButton from "@/components/ActionButton";
import IDCardPrinter from "@/components/utils/IDCardPrinter";
import { useAcademicYearContext } from "@/context/AcademicYearContext";
import { ClassLevelSchema } from "@/app/models/ClassLevel";
import { getClassLevelsBySchoolId } from "@/app/services/ClassLevels";

export default function ViewParentPage() {
    const [school, setSchool] = useState<SchoolSchema | null>(null);
    const [classes, setClasses] = useState<ClassSchema[]>([]);
    const [student, setStudent] = useState<StudentSchema | null>(null);
    const [parents, setParents] = useState<UserSchema[]>([]);
    const [loading, setLoading] = useState(true);
    const [isNotificationCard, setIsNotificationCard] = useState(false);
    const [notificationMessage, setNotificationMessage] = useState("");
    const [notificationType, setNotificationType] = useState<"success" | "error" | "info" | "warning">("success");
    const searchParams = useSearchParams();
    const student_id = searchParams.get("studentId");
    const schoolId = searchParams.get("schoolId");
    const router = useRouter();
    const { user } = useAuth();
      const [classesLevels, setClassesLevels] = useState<ClassLevelSchema[]>([]);
    

    const [isUpdateModalOpen, setIsUpdateModalOpen] = useState(false);
    const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
    const [isSubmitting, setIsSubmitting] = useState(false);
    const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
    const { allAcademicYears, currentAcademicYear } = useAcademicYearContext();

    const BASE_URL = "/school-admin";

    const fetchData = async () => {
        setLoading(true);
        try {
            const [studentData, schoolData, classData, levels] = await Promise.all([
                getStudentById(student_id as string),
                getSchoolBy_id(schoolId as string),
                getClasses(),
                getClassLevelsBySchoolId(schoolId as string),
            ]);

            setStudent(studentData);
            setSchool(schoolData);
            setClasses(classData)
            setClassesLevels(levels)
            // Fetch parents if guardian_id exists
            if (studentData?.guardian_id?.length) {
                const guardianUsers = await Promise.all(
                    studentData.guardian_id.map((id: string) => getUserBy_id(id))
                );
                setParents(guardianUsers);
            }
        } catch (error) {
            console.error("Error loading data", error);
        } finally {
            setLoading(false);
        }
    };
    useEffect(() => {
        if (student_id) fetchData();
    }, [student_id]);


  const getClassLevelNames = (ids?: string[]) =>
    ids
      ?.map((id) =>
        classesLevels.find((c) => c._id === id && c.school_id === schoolId)?.name || "Unknown"
      )
      .join(", ") || "No class";

    const [uploadingAvatar, setUploadingAvatar] = useState(false);
    const [avatarUrl, setAvatarUrl] = useState<string | null>(null);
    useEffect(() => {
        if (student?.avatar) setAvatarUrl(student.avatar);
    }, [student]);
    //console.log(student)
    async function handleAvatarUpload(studentId: string, file: File) {
        try {
            setUploadingAvatar(true);
            const result = await uploadStudentAvatar(studentId, file);
            console.log("Avatar uploaded:", result.message);
            setAvatarUrl(result.avatarUrl || null); // update avatar URL with new image from server
            setNotificationMessage("Avatar uploaded successfully!");
            setNotificationType("success");
            setIsNotificationCard(true);
        } catch (err) {
            console.error("Upload failed:", err);
            setNotificationMessage("Avatar upload failed. Please try again.");
            setNotificationType("error");
            setIsNotificationCard(true);
        } finally {
            setUploadingAvatar(false);
            fetchData();
        }
    }
    // Gérer la suppression de l'école
    const handleDelete = async (password: string) => {
        // Simuler une vérification de mot de passe (dans un vrai projet, fais une requête API)
        const passwordVerified = user ? await verifyPassword(password, user.email) : false;
        //console.log("passwordVerified", passwordVerified);
        if (!passwordVerified) {
            setNotificationMessage("Invalid password. Please try again.");
            setNotificationType("error");
            setIsNotificationCard(true);
            return;
        }
        // if (!schoolId) {
        //     throw new Error("School ID is null. Cannot delete school.");
        // }
        const deleted = await deleteStudent(student_id as string);
        if (deleted) {
            setNotificationMessage("Student deleted successfully");
            setNotificationType("success");
            setIsNotificationCard(true);
            router.push(`${BASE_URL}/students/manage?id=${schoolId}`);
        }
    }
    function onAvatarChange(e: ChangeEvent<HTMLInputElement>) {
        if (!student) return;
        const file = e.target.files?.[0];
        if (!file) return;
        handleAvatarUpload(student.student_id, file);
    }

    function formatPhoneNumberToE164(countryCode: string, localNumber: string): string {
        const cleanedCountryCode = countryCode.replace(/\D/g, '');
        const cleanedLocalNumber = localNumber.replace(/\D/g, '');
        return `+${cleanedCountryCode}${cleanedLocalNumber}`;
    }
    const handleSave = async (studentData: StudentSchema) => {
        if (user) {
            setIsSubmitting(true);
            setSubmitStatus(null);
            try {
                const updateStudentData: StudentUpdateSchema = {
                    _id: studentData._id,
                    school_id: studentData.school_id || "",
                    student_id: studentData.student_id || "",
                    first_name: studentData.first_name,
                    last_name: studentData.last_name,
                    middle_name: studentData.middle_name || "",
                    name: `${studentData.first_name} ${studentData.middle_name || ""} ${studentData.last_name}`,
                    gender: studentData.gender || undefined,
                    date_of_birth: studentData.date_of_birth
                        ? (typeof studentData.date_of_birth === "string"
                            ? studentData.date_of_birth
                            : studentData.date_of_birth.toISOString())
                        : undefined,
                    address: studentData.address || "",
                    place_of_birth: studentData.place_of_birth || "",
                    phone: studentData.student_country_code && studentData.student_phone
                        ? formatPhoneNumberToE164(
                            typeof studentData.student_country_code === "string" ? studentData.student_country_code : "",
                            typeof studentData.student_phone === "string" ? studentData.student_phone : ""
                        )
                        : "",
                    class_level: studentData.class_level || "",
                    class_id: studentData.class_id || null,
                    transcript_reportcard: studentData.transcript_reportcard || false,
                    previous_school: studentData.previous_school || "",
                    health_condition: studentData.health_condition || "",
                    doctors_name: studentData.doctors_name || "",
                    doctors_phone: studentData.doctor_country_code && studentData.doctors_phone
                        ? formatPhoneNumberToE164(studentData.doctor_country_code as string, studentData.doctors_phone)
                        : "",
                    emergency_contact_name: studentData.emergency_contact_name || "",
                    emergency_contact_phone: studentData.emergency_contact_country_code && studentData.emergency_contact_phone
                        ? formatPhoneNumberToE164(studentData.emergency_contact_country_code as string, studentData.emergency_contact_phone)
                        : "",
                    emergency_contact_relationship: studentData.emergency_contact_relationship as "Mother" | "Father" | "Brother" | "Sister" | "Aunty" | "Uncle" | "Grand Mother" | "Grand Father" | "Other" | undefined,


                    non_compulsory_sbj: studentData.non_compulsory_sbj || [],
                    registered: studentData.registered || false, // Ensure registered is always a boolean
                };
                console.log("student_country_code:", studentData.student_country_code);
                console.log("student_phone:", studentData.student_phone);

                if (studentData.student_id) {
                    const data = await updateStudent(studentData.student_id, updateStudentData);
                    if (data) {
                        setStudent(data);
                        setSubmitStatus("success");
                        setNotificationMessage("Student updated successfully!");
                        setNotificationType("success");
                        setIsNotificationCard(true);
                    } else {
                        setSubmitStatus("failure");
                        setNotificationMessage("Error updating student.");
                        setNotificationType("error");
                        setIsNotificationCard(true);
                    }
                } else {
                    setSubmitStatus("failure");
                    setNotificationMessage("Student ID is required.");
                    setNotificationType("error");
                    setIsNotificationCard(true);
                }
            } catch (error) {
                const errorMessage = error instanceof Error ? error.message : "Error updating student.";
                setSubmitStatus("failure");
                setNotificationMessage(errorMessage);
                setNotificationType("error");
                setIsNotificationCard(true);
            } finally {
                setIsSubmitting(false);
                setTimeout(() => {
                    setIsUpdateModalOpen(false);
                    setSubmitStatus(null);
                }, 10000);
            }
        }
    };

    return (
        <SchoolLayout
            navigation={{ icon: ArrowLeft, title: "Student Details", baseHref: "/super-admin/students" }}
            showGoPro={true}
            onLogout={() => console.log("Logout")}
        >
            {isUpdateModalOpen && (
                <UpdateStudentModal
                    onClose={() => { setIsUpdateModalOpen(false); setSubmitStatus(null); }}
                    onSave={handleSave}
                    initialData={student as StudentSchema}
                    submitStatus={submitStatus}
                    isSubmitting={isSubmitting}

                />
            )}
            {isNotificationCard && (
                <NotificationCard
                    title="Notification"
                    icon={
                        <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                            <path d="M12 22C17.5 22 22 17.5 22 12C22 6.5 17.5 2 12 2C6.5 2 2 6.5 2 12C2 17.5 6.5 22 12 22Z" stroke="#15803d" strokeWidth="1.5" />
                            <path d="M7.75 11.9999L10.58 14.8299L16.25 9.16992" stroke="#15803d" strokeWidth="1.5" />
                        </svg>
                    }
                    message={notificationMessage}
                    onClose={() => setIsNotificationCard(false)}
                    type={notificationType}
                    isVisible={isNotificationCard}
                    isFixed={true}
                />
            )}

            {loading ? (
                <div className="fixed inset-0 flex items-center justify-center z-[100] bg-black/90">
                    <CircularLoader size={32} color="teal" />
                </div>
            ) : !student ? (
                <div className="text-center text-red-600 font-semibold">Student not found.</div>
            ) : (
                <div className="md:p-6 space-y-6">
                    {/* Avatar Upload Section */}
                    <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 flex flex-col items-center">
                        <h2 className="text-xl font-bold text-foreground mb-4">Student Avatar</h2>

                        <div className="relative w-40 h-40 rounded-full overflow-hidden border-4 border-teal-500 shadow-md">
                            {uploadingAvatar ? (
                                <motion.div
                                    animate={{ rotate: 360 }}
                                    transition={{ repeat: Infinity, duration: 1, ease: "linear" }}
                                    className="w-full h-full flex items-center justify-center bg-teal-100"
                                >
                                    <UploadCloud className="text-teal-600" size={48} />
                                </motion.div>
                            ) : (
                                <img
                                    src={avatarUrl || "/assets/images/student.jpg"}
                                    alt="Student Avatar"
                                    className="w-full h-full object-cover"
                                />
                            )}
                        </div>

                        <label
                            htmlFor="avatar-upload"
                            className="mt-4 inline-flex items-center cursor-pointer text-teal-600 hover:text-teal-800 transition-colors"
                        >
                            <UploadCloud className="mr-2" size={20} />
                            <span>Upload New Avatar</span>
                            <input
                                type="file"
                                id="avatar-upload"
                                accept="image/*"
                                className="hidden"
                                onChange={onAvatarChange}
                                disabled={uploadingAvatar}
                            />
                        </label>
                    </section>
                    {/* Student Info */}
                    <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Student Information
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Student ID" value={student.student_id || "N/A"} />
                            <InfoBox label="Full Name" value={`${student.first_name} ${student.middle_name || ""} ${student.last_name}`} />
                            <InfoBox label="Gender" value={student.gender || "N/A"} />
                            <InfoBox label="Date Of Birth" value={student.date_of_birth ? new Date(student.date_of_birth).toLocaleDateString() : "N/A"} />
                            <InfoBox label="Nationality" value={student.nationality || "N/A"} />
                            <InfoBox label="Place Of Birth" value={student.place_of_birth || "N/A"} />
                            <InfoBox label="Address" value={student.address || "N/A"} />
                            <InfoBox label="Phone" value={student.phone || "N/A"} />
                        </div>
                    </section>

                    {/* Guardian Info */}
                    <section className="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Guardian Information
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Guardian Name" value={student.guardian_name || "N/A"} />
                            <InfoBox label="Guardian Phone" value={student.guardian_phone || "N/A"} />
                            <InfoBox label="Guardian Email" value={student.guardian_email || "N/A"} />
                            <InfoBox label="Guardian Address" value={typeof student.guardian_address === "string" ? student.guardian_address : (student.guardian_address ? JSON.stringify(student.guardian_address) : "N/A")} />
                            <InfoBox label="Guardian Relationship" value={student.guardian_relationship || "N/A"} />
                            <InfoBox label="Guardian Occupation" value={student.guardian_occupation || "N/A"} />
                        </div>
                    </section>

                    {/* Emergency Contact */}
                    <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Emergency Contact
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Emergency Contact Name" value={student.emergency_contact_name || "N/A"} />
                            <InfoBox label="Emergency Contact Phone" value={student.emergency_contact_phone || "N/A"} />
                            <InfoBox label="Emergency Contact Relationship" value={student.emergency_contact_relationship || "N/A"} />
                        </div>
                    </section>

                    {/* Health Info */}
                    <section className="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Health Information
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Health Condition" value={student.health_condition || "N/A"} />
                            <InfoBox label="Doctor's Name" value={student.doctors_name || "N/A"} />
                            <InfoBox label="Doctor's Phone" value={student.doctors_phone || "N/A"} />
                        </div>
                    </section>

                    {/* Previous School and Transcript */}
                    <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Previous School & Transcript
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Previous School" value={student.previous_school || "N/A"} />
                            <InfoBox label="Transcript Report Card Submitted" value={student.transcript_reportcard ? "Yes" : "No"} />
                        </div>
                    </section>

                    {/* Payment Info */}
                    <section className="bg-gray-50 dark:bg-gray-700 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Payment Information
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Payment Mode" value={student.paymentMode || "N/A"} />
                            <InfoBox label="Installments" value={student.installments?.toString() || "1"} />
                            <InfoBox label="Scholarship Applied" value={student.applyScholarship ? "Yes" : "No"} />
                            <InfoBox label="Scholarship Percentage" value={`${student.scholarshipPercentage || 0}%`} />
                            <InfoBox label="Total Fees" value={`$${student.fees?.toFixed(2) || "0.00"}`} />
                        </div>
                    </section>

                    {/* Enrollment */}
                    <section className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6">
                        <h2 className="text-xl font-bold text-foreground border-b border-gray-300 pb-2 mb-4">
                            Enrollment
                        </h2>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                            <InfoBox label="Enrollment Date" value={student.enrollement_date ? new Date(student.enrollement_date).toLocaleDateString() : "N/A"} />
                            <InfoBox label="Status" value={student.status || "N/A"} />
                            <InfoBox label="Registered" value={student.registered ? "Yes" : "No"} />
                        </div>
                    </section>

                    {student.avatar && (
                        <div className="mb-4 flex justify-center">
                            <img
                                src={student.avatar}
                                alt={`${student.first_name}'s avatar`}
                                className="w-24 h-24 rounded-full object-cover border"
                            />
                        </div>
                    )}

                    <div className="flex justify-end space-x-2">
                        <BackButton />
                        {student && ( // Only render if student data is available
                            <IDCardPrinter
                                students={[student]} // Pass the single student in an array
                                school={school || undefined} // Pass the specific school object found
                                acadamicYear={currentAcademicYear}
                                getClassLevelNames={getClassLevelNames} // Pass the helper function
                            />
                        )}
                        <ActionButton
                            action="edit"
                            onClick={() => setIsUpdateModalOpen(true)}
                            label="Edit Student"
                        />

                        <ActionButton
                            action="delete"
                            onClick={() => setIsDeleteModalOpen(true)}
                            label="Delete Student"
                        />
                    </div>
                </div>
            )}
            {isDeleteModalOpen && (
                <DeleteStudentModal
                    studentName={student?.first_name + " " + student?.last_name || ""}
                    onClose={() => setIsDeleteModalOpen(false)}
                    onDelete={handleDelete} submitStatus={null} isSubmitting={false} />
            )}
        </SchoolLayout>
    );
}

const InfoBox = ({ label, value }: { label: string; value: string }) => (
    <div className="bg-gray-100 dark:bg-gray-700 p-4 rounded-md">
        <p className="text-sm font-semibold text-gray-600 dark:text-gray-300">{label}</p>
        <p className="text-sm text-foreground">{value}</p>
    </div>
);
