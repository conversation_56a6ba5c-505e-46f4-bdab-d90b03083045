"use client";

import { useState } from "react";
import { Search, BookOpen, MessageCircle, Phone, Mail, ArrowRight, ChevronDown, ChevronRight, Users, Shield, Clock, Zap } from "lucide-react";
import SharedNavigation from "@/components/layout/SharedNavigation";
import SharedFooter from "@/components/layout/SharedFooter";
import { useRouter } from "next/navigation";

export default function HelpCenterPage() {
    const router = useRouter();
    const [searchQuery, setSearchQuery] = useState("");
    const [activeCategory, setActiveCategory] = useState("getting-started");
    const [expandedFaq, setExpandedFaq] = useState<number | null>(null);

    const categories = [
        {
            id: "getting-started",
            title: "Getting Started",
            icon: <BookOpen className="w-5 h-5" />,
            description: "Learn the basics of Scholarify"
        },
        {
            id: "user-management",
            title: "User Management",
            icon: <Users className="w-5 h-5" />,
            description: "Manage users and permissions"
        },
        {
            id: "security",
            title: "Security & Privacy",
            icon: <Shield className="w-5 h-5" />,
            description: "Data protection and security"
        },
        {
            id: "features",
            title: "Features & Tools",
            icon: <Zap className="w-5 h-5" />,
            description: "Platform features and functionality"
        }
    ];

    const faqs = {
        "getting-started": [
            {
                question: "How do I set up my school on Scholarify?",
                answer: "To set up your school, start by creating an account and completing the initial setup wizard. You'll need to provide basic school information, create admin accounts, and configure your school's settings. Our onboarding team will guide you through the entire process."
            },
            {
                question: "What information do I need to get started?",
                answer: "You'll need your school's basic information (name, address, contact details), administrator details, and an overview of your current student and staff numbers. We'll help you migrate existing data if needed."
            },
            {
                question: "How long does the setup process take?",
                answer: "The initial setup typically takes 1-2 hours. Complete data migration and customization can take 1-2 weeks depending on your school's size and complexity."
            }
        ],
        "user-management": [
            {
                question: "How do I add new teachers to the system?",
                answer: "Go to the Users section in your admin dashboard, click 'Add User', select 'Teacher' role, and fill in their information. Teachers will receive an email invitation to set up their account."
            },
            {
                question: "Can I customize user permissions?",
                answer: "Yes, you can customize permissions for each user role. Go to Settings > User Management to configure specific permissions for administrators, teachers, and other staff members."
            },
            {
                question: "How do I reset a user's password?",
                answer: "In the Users section, find the user, click on their profile, and select 'Reset Password'. They'll receive an email with a temporary password to set up a new one."
            }
        ],
        "security": [
            {
                question: "How secure is my school's data?",
                answer: "Scholarify uses enterprise-grade security including end-to-end encryption, secure data centers, regular security audits, and compliance with FERPA and COPPA standards."
            },
            {
                question: "Can I export my data?",
                answer: "Yes, you can export your data at any time. Go to Settings > Data Management to export student records, grades, attendance, and other information in various formats."
            },
            {
                question: "What happens to my data if I cancel?",
                answer: "Your data is retained for 30 days after cancellation. You can export all your data during this period. After 30 days, data is permanently deleted from our servers."
            }
        ],
        "features": [
            {
                question: "How do I create and manage classes?",
                answer: "Go to Classes in your admin dashboard, click 'Add Class', and fill in the details including class name, teacher assignment, schedule, and student enrollment."
            },
            {
                question: "Can teachers take attendance from mobile devices?",
                answer: "Yes, teachers can take attendance using our mobile app or any web browser. The attendance data syncs in real-time across all devices."
            },
            {
                question: "How do I generate reports?",
                answer: "Go to Reports section and select the type of report you need (attendance, grades, performance, etc.). You can customize date ranges and export in various formats."
            }
        ]
    };

    const contactMethods = [
        {
            icon: <Mail className="w-6 h-6" />,
            title: "Email Support",
            description: "Get help via email within 24 hours",
            action: "<EMAIL>",
            color: "text-blue-600"
        },
        {
            icon: <Phone className="w-6 h-6" />,
            title: "Phone Support",
            description: "Speak with our support team",
            action: "+****************",
            color: "text-green-600"
        },
        {
            icon: <MessageCircle className="w-6 h-6" />,
            title: "Live Chat",
            description: "Chat with us in real-time",
            action: "Start Chat",
            color: "text-purple-600"
        }
    ];

    const filteredFaqs = faqs[activeCategory as keyof typeof faqs] || [];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <SharedNavigation showBackButton={true} />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-r from-teal-600 to-teal-700 dark:from-teal-800 dark:to-teal-900">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                        How can we help you?
                    </h1>
                    <p className="text-xl text-teal-100 mb-8">
                        Find answers to common questions and get the support you need.
                    </p>
                    
                    {/* Search Bar */}
                    <div className="max-w-2xl mx-auto">
                        <div className="relative">
                            <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                            <input
                                type="text"
                                placeholder="Search for help articles..."
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                                className="w-full pl-12 pr-4 py-4 rounded-lg border-0 focus:ring-2 focus:ring-white focus:ring-offset-2 focus:ring-offset-teal-600"
                            />
                        </div>
                    </div>
                </div>
            </section>

            {/* Main Content */}
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="grid lg:grid-cols-4 gap-8">
                    {/* Sidebar */}
                    <div className="lg:col-span-1">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
                            <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                                Help Categories
                            </h2>
                            <div className="space-y-2">
                                {categories.map((category) => (
                                    <button
                                        key={category.id}
                                        onClick={() => setActiveCategory(category.id)}
                                        className={`w-full flex items-center p-3 rounded-lg text-left transition-colors ${
                                            activeCategory === category.id
                                                ? "bg-teal-50 dark:bg-teal-900/20 text-teal-700 dark:text-teal-300"
                                                : "text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700"
                                        }`}
                                    >
                                        <div className="mr-3">{category.icon}</div>
                                        <div>
                                            <div className="font-medium">{category.title}</div>
                                            <div className="text-sm opacity-75">{category.description}</div>
                                        </div>
                                    </button>
                                ))}
                            </div>
                        </div>
                    </div>

                    {/* Main Content */}
                    <div className="lg:col-span-3">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                                {categories.find(c => c.id === activeCategory)?.title}
                            </h2>
                            
                            <div className="space-y-4">
                                {filteredFaqs.map((faq, index) => (
                                    <div
                                        key={index}
                                        className="border border-gray-200 dark:border-gray-600 rounded-lg overflow-hidden"
                                    >
                                        <button
                                            onClick={() => setExpandedFaq(expandedFaq === index ? null : index)}
                                            className="w-full px-6 py-4 text-left flex justify-between items-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                                        >
                                            <span className="font-medium text-gray-900 dark:text-white">
                                                {faq.question}
                                            </span>
                                            {expandedFaq === index ? (
                                                <ChevronDown className="w-5 h-5 text-gray-500" />
                                            ) : (
                                                <ChevronRight className="w-5 h-5 text-gray-500" />
                                            )}
                                        </button>
                                        {expandedFaq === index && (
                                            <div className="px-6 pb-4">
                                                <p className="text-gray-700 dark:text-gray-300 leading-relaxed">
                                                    {faq.answer}
                                                </p>
                                            </div>
                                        )}
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Contact Section */}
                <section className="mt-16">
                    <div className="text-center mb-12">
                        <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                            Still need help?
                        </h2>
                        <p className="text-lg text-gray-600 dark:text-gray-300">
                            Our support team is here to help you succeed.
                        </p>
                    </div>

                    <div className="grid md:grid-cols-3 gap-8">
                        {contactMethods.map((method, index) => (
                            <div
                                key={index}
                                className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 text-center"
                            >
                                <div className={`mx-auto mb-4 ${method.color}`}>
                                    {method.icon}
                                </div>
                                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                                    {method.title}
                                </h3>
                                <p className="text-gray-600 dark:text-gray-300 mb-4">
                                    {method.description}
                                </p>
                                <button className="text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 font-medium flex items-center justify-center transition-colors">
                                    {method.action}
                                    <ArrowRight className="w-4 h-4 ml-1" />
                                </button>
                            </div>
                        ))}
                    </div>
                </section>

                {/* Additional Resources */}
                <section className="mt-16">
                    <div className="bg-gradient-to-r from-teal-50 to-blue-50 dark:from-teal-900/20 dark:to-blue-900/20 rounded-lg p-8">
                        <div className="text-center">
                            <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                                Additional Resources
                            </h3>
                            <p className="text-gray-600 dark:text-gray-300 mb-6">
                                Explore our comprehensive documentation and training materials.
                            </p>
                            <div className="flex flex-col sm:flex-row gap-4 justify-center">
                                <button
                                    onClick={() => router.push("/docs")}
                                    className="bg-teal-600 hover:bg-teal-700 text-white px-6 py-3 rounded-lg font-medium transition-colors flex items-center justify-center"
                                >
                                    <BookOpen className="w-4 h-4 mr-2" />
                                    View Documentation
                                </button>
                                <button
                                    onClick={() => router.push("/contact")}
                                    className="border border-teal-600 text-teal-600 dark:text-teal-400 hover:bg-teal-50 dark:hover:bg-teal-900/20 px-6 py-3 rounded-lg font-medium transition-colors"
                                >
                                    Contact Us
                                </button>
                            </div>
                        </div>
                    </div>
                </section>
            </div>

            {/* Footer */}
            <SharedFooter />
        </div>
    );
} 