"use client";

import { useState } from 'react';
import { CheckCircle, ArrowRight, BookOpen, Users, Settings, Shield, GraduationCap, UserCheck, Clock, AlertCircle } from 'lucide-react';
import Link from 'next/link';

const steps = [
  {
    id: 1,
    title: 'Account Setup',
    description: 'Create your account and verify your email',
    duration: '5 minutes',
    icon: Shield,
    details: [
      'Visit the Scholarify registration page',
      'Enter your school information and contact details',
      'Verify your email address',
      'Set up your password and security preferences'
    ]
  },
  {
    id: 2,
    title: 'School Profile Configuration',
    description: 'Set up your school profile and basic information',
    duration: '15 minutes',
    icon: Settings,
    details: [
      'Add school name, address, and contact information',
      'Upload school logo and branding materials',
      'Configure academic calendar and terms',
      'Set up school policies and guidelines'
    ]
  },
  {
    id: 3,
    title: 'Academic Structure Setup',
    description: 'Configure classes, subjects, and academic structure',
    duration: '30 minutes',
    icon: BookOpen,
    details: [
      'Create grade levels and classes',
      'Add subjects and assign teachers',
      'Set up class schedules and timetables',
      'Configure grading scales and assessment criteria'
    ]
  },
  {
    id: 4,
    title: 'User Invitation',
    description: 'Invite teachers, parents, and students to the platform',
    duration: '20 minutes',
    icon: Users,
    details: [
      'Invite teachers and assign their classes',
      'Send parent invitations with student information',
      'Create student accounts and link to parents',
      'Set up initial passwords and access permissions'
    ]
  },
  {
    id: 5,
    title: 'Feature Configuration',
    description: 'Enable and configure key features for your school',
    duration: '25 minutes',
    icon: GraduationCap,
    details: [
      'Configure attendance tracking settings',
      'Set up grade entry and reporting options',
      'Enable communication features and notifications',
      'Configure financial management tools'
    ]
  },
  {
    id: 6,
    title: 'Training & Go-Live',
    description: 'Train users and launch the platform',
    duration: '1-2 hours',
    icon: UserCheck,
    details: [
      'Conduct training sessions for teachers and staff',
      'Provide parent orientation sessions',
      'Test all features with sample data',
      'Go live with your school community'
    ]
  }
];

const quickActions = [
  {
    title: 'School Setup Guide',
    description: 'Detailed instructions for configuring your school profile',
    href: '/docs/getting-started/school-setup',
    icon: Settings
  },
  {
    title: 'User Invitation Guide',
    description: 'Step-by-step process for inviting users to your platform',
    href: '/docs/getting-started/user-invitation',
    icon: Users
  },
  {
    title: 'Role-Based Permissions',
    description: 'Understand how different user roles work in Scholarify',
    href: '/docs/user-roles',
    icon: Shield
  },
  {
    title: 'Feature Overview',
    description: 'Explore all available features and capabilities',
    href: '/docs/features',
    icon: BookOpen
  }
];

export default function GettingStartedPage() {
  const [activeStep, setActiveStep] = useState(1);

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-4xl font-bold text-gray-900 mb-4">
              Getting Started with Scholarify
            </h1>
            <p className="text-xl text-gray-600">
              Complete setup guide to get your school up and running on Scholarify in under 2 hours.
            </p>
          </div>
        </div>
      </div>

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8">
          <div className="flex items-center mb-4">
            <Clock className="h-6 w-6 text-blue-600 mr-2" />
            <h2 className="text-xl font-semibold text-gray-900">Setup Timeline</h2>
          </div>
          <div className="grid md:grid-cols-3 gap-4 text-sm">
            <div className="bg-white rounded-lg p-3">
              <div className="font-semibold text-gray-900">Total Time</div>
              <div className="text-blue-600 font-medium">2-3 hours</div>
            </div>
            <div className="bg-white rounded-lg p-3">
              <div className="font-semibold text-gray-900">Steps Required</div>
              <div className="text-blue-600 font-medium">6 main steps</div>
            </div>
            <div className="bg-white rounded-lg p-3">
              <div className="font-semibold text-gray-900">Team Involvement</div>
              <div className="text-blue-600 font-medium">IT Admin + School Admin</div>
            </div>
          </div>
        </div>

        {/* Setup Steps */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Setup Process</h2>
          <div className="space-y-6">
            {steps.map((step, index) => {
              const Icon = step.icon;
              const isActive = activeStep === step.id;
              
              return (
                <div
                  key={step.id}
                  className={`bg-white rounded-lg border-2 transition-all duration-200 ${
                    isActive ? 'border-blue-500 shadow-lg' : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center">
                        <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-4 ${
                          isActive ? 'bg-blue-500 text-white' : 'bg-gray-200 text-gray-600'
                        }`}>
                          {step.id}
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{step.title}</h3>
                          <p className="text-gray-600">{step.description}</p>
                        </div>
                      </div>
                      <div className="flex items-center text-sm text-gray-500">
                        <Clock className="h-4 w-4 mr-1" />
                        {step.duration}
                      </div>
                    </div>

                    <div className="flex items-center mb-4">
                      <Icon className="h-5 w-5 text-blue-600 mr-2" />
                      <span className="text-sm font-medium text-gray-700">Key Tasks:</span>
                    </div>

                    <ul className="space-y-2 mb-4">
                      {step.details.map((detail, detailIndex) => (
                        <li key={detailIndex} className="flex items-start">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                          <span className="text-sm text-gray-700">{detail}</span>
                        </li>
                      ))}
                    </ul>

                    <div className="flex justify-between items-center">
                      <button
                        onClick={() => setActiveStep(step.id)}
                        className={`px-4 py-2 rounded-md text-sm font-medium transition-colors ${
                          isActive
                            ? 'bg-blue-500 text-white hover:bg-blue-600'
                            : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                        }`}
                      >
                        {isActive ? 'Current Step' : 'View Details'}
                      </button>
                      
                      {index < steps.length - 1 && (
                        <ArrowRight className="h-5 w-5 text-gray-400" />
                      )}
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
          <div className="grid md:grid-cols-2 gap-6">
            {quickActions.map((action, index) => {
              const Icon = action.icon;
              
              return (
                <Link
                  key={index}
                  href={action.href}
                  className="group bg-white rounded-lg border border-gray-200 p-6 hover:border-blue-300 hover:shadow-md transition-all duration-200"
                >
                  <div className="flex items-start justify-between">
                    <div className="flex items-center">
                      <div className="p-2 bg-blue-50 rounded-lg mr-4">
                        <Icon className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                          {action.title}
                        </h3>
                        <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                      </div>
                    </div>
                    <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                  </div>
                </Link>
              );
            })}
          </div>
        </div>

        {/* Tips & Best Practices */}
        <div className="bg-yellow-50 border border-yellow-200 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Pro Tips</h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Start with a small group of teachers for initial testing before full rollout</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Schedule training sessions during school breaks or professional development days</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Prepare sample data and test scenarios before going live</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Designate a "champion" teacher to help others with the transition</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Support Section */}
        <div className="bg-white rounded-lg border border-gray-200 p-6">
          <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-900 mb-2">Need Help?</h3>
            <p className="text-gray-600 mb-4">
              Our support team is available to help you with the setup process.
            </p>
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Link
                href="/docs/troubleshooting/support"
                className="inline-flex items-center justify-center px-4 py-2 border border-transparent text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700"
              >
                Contact Support
              </Link>
              <Link
                href="/docs/troubleshooting/common-issues"
                className="inline-flex items-center justify-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50"
              >
                Common Issues
              </Link>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
