"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  BookOpen,
  Users,
  Calendar,
  BarChart3,
  MessageCircle,
  CreditCard,
  Shield,
  FileText,
  Settings,
  Bell,
  GraduationCap,
  Clock,
  CheckCircle,
  ArrowRight
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function FeaturesPage() {
  const router = useRouter();
  const [activeCategory, setActiveCategory] = useState('all');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const categories = [
    { id: 'all', name: 'All Features', icon: BookOpen },
    { id: 'academic', name: 'Academic Management', icon: GraduationCap },
    { id: 'financial', name: 'Financial Management', icon: CreditCard },
    { id: 'communication', name: 'Communication', icon: MessageCircle },
    { id: 'analytics', name: 'Reports & Analytics', icon: BarChart3 },
    { id: 'security', name: 'Security & Privacy', icon: Shield },
  ];

  const features = [
    {
      id: 'academic-management',
      category: 'academic',
      title: 'Academic Management',
      description: 'Comprehensive tools for managing classes, subjects, grades, and academic records.',
      icon: GraduationCap,
      href: '/docs/features/academic-management',
      features: [
        'Class and subject management',
        'Grade tracking and reporting',
        'Academic calendar management',
        'Curriculum planning tools',
        'Student performance analytics',
        'Exam and assessment management'
      ]
    },
    {
      id: 'financial-management',
      category: 'financial',
      title: 'Financial Management',
      description: 'Complete financial tracking including fees, payments, and financial reporting.',
      icon: CreditCard,
      href: '/docs/features/financial-management',
      features: [
        'Fee structure management',
        'Payment tracking and processing',
        'Financial reporting and analytics',
        'Invoice generation',
        'Payment history tracking',
        'Financial dashboard'
      ]
    },
    {
      id: 'communication',
      category: 'communication',
      title: 'Communication Tools',
      description: 'Built-in communication tools for parent-teacher communication and announcements.',
      icon: MessageCircle,
      href: '/docs/features/communication',
      features: [
        'Parent-teacher messaging',
        'Announcement system',
        'Notification management',
        'Communication history',
        'Bulk messaging capabilities',
        'Message templates'
      ]
    },
    {
      id: 'reports-analytics',
      category: 'analytics',
      title: 'Reports & Analytics',
      description: 'Advanced reporting and analytics for data-driven decision making.',
      icon: BarChart3,
      href: '/docs/features/reports-analytics',
      features: [
        'Custom report generation',
        'Performance analytics',
        'Attendance reports',
        'Financial reports',
        'Export capabilities',
        'Real-time dashboards'
      ]
    },
    {
      id: 'attendance-tracking',
      category: 'academic',
      title: 'Attendance Tracking',
      description: 'Automated attendance tracking with detailed reporting and analytics.',
      icon: Clock,
      href: '/docs/features/attendance-tracking',
      features: [
        'Automated attendance marking',
        'Attendance reports',
        'Absence tracking',
        'Attendance analytics',
        'Parent notifications',
        'Attendance history'
      ]
    },
    {
      id: 'user-management',
      category: 'security',
      title: 'User Management',
      description: 'Comprehensive user management with role-based access control.',
      icon: Users,
      href: '/docs/user-management',
      features: [
        'Role-based access control',
        'User invitation system',
        'Permission management',
        'User activity tracking',
        'Account security settings',
        'Multi-factor authentication'
      ]
    },
    {
      id: 'security-privacy',
      category: 'security',
      title: 'Security & Privacy',
      description: 'Enterprise-grade security with data protection and privacy controls.',
      icon: Shield,
      href: '/docs/security',
      features: [
        'Data encryption',
        'Privacy controls',
        'Audit logging',
        'GDPR compliance',
        'Secure data storage',
        'Access monitoring'
      ]
    },
    {
      id: 'timetable-management',
      category: 'academic',
      title: 'Timetable Management',
      description: 'Advanced timetable creation and management tools.',
      icon: Calendar,
      href: '/docs/features/timetable-management',
      features: [
        'Automated timetable generation',
        'Conflict detection',
        'Room allocation',
        'Teacher scheduling',
        'Timetable optimization',
        'Export capabilities'
      ]
    }
  ];

  const filteredFeatures = activeCategory === 'all' 
    ? features 
    : features.filter(feature => feature.category === activeCategory);

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Docs" />
      
      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
              Platform Features
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4">
              Discover all the powerful features that make Scholarify the comprehensive 
              school management solution for modern educational institutions.
            </p>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="px-4 sm:px-6 lg:px-8 pb-6 sm:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {categories.map((category) => {
              const Icon = category.icon;
              return (
                <button
                  key={category.id}
                  onClick={() => setActiveCategory(category.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                    activeCategory === category.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>{category.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16">
        <div className="max-w-7xl mx-auto">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
            {filteredFeatures.map((feature) => {
              const Icon = feature.icon;
              return (
                <div
                  key={feature.id}
                  className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                >
                  <div className="p-4 sm:p-6">
                    <div className="flex items-center justify-between mb-4">
                      <div className="p-2 sm:p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg">
                        <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-teal-600 dark:text-teal-400" />
                      </div>
                      <button
                        onClick={() => handleNavigation(feature.href)}
                        className="text-teal-600 dark:text-teal-400 hover:text-teal-700 dark:hover:text-teal-300 transition-colors p-1"
                      >
                        <ArrowRight className="w-4 h-4 sm:w-5 sm:h-5" />
                      </button>
                    </div>
                    
                    <h3 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white mb-2 sm:mb-3">
                      {feature.title}
                    </h3>
                    
                    <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
                      {feature.description}
                    </p>
                    
                    <ul className="space-y-1 sm:space-y-2">
                      {feature.features.slice(0, 4).map((item, index) => (
                        <li key={index} className="flex items-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-teal-500 mr-2 flex-shrink-0" />
                          <span className="line-clamp-1">{item}</span>
                        </li>
                      ))}
                    </ul>
                    
                    <button
                      onClick={() => handleNavigation(feature.href)}
                      className="mt-4 sm:mt-6 w-full bg-teal-600 hover:bg-teal-700 dark:bg-teal-600 dark:hover:bg-teal-700 text-white py-2 px-4 rounded-lg font-medium transition-colors flex items-center justify-center space-x-2 text-sm sm:text-base"
                    >
                      <span>Learn More</span>
                      <ArrowRight className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 dark:bg-teal-600 text-white py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6">
            Ready to Get Started?
          </h2>
          <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-teal-100 dark:text-teal-100 max-w-3xl mx-auto">
            Explore our comprehensive documentation and start transforming your school management today.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 dark:bg-white dark:text-teal-600 dark:hover:bg-gray-100 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Getting Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-teal-600 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 