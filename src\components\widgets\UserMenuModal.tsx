"use client";

import React, { use, useEffect, useState } from "react";
import { Sun, Moon, LogOut, Monitor, Settings, Loader2 } from "lucide-react";
import DarkModeToggle from "./DarkMode";
import useAuth from "@/app/hooks/useAuth";
import Link from "next/link";

interface UserMenuModalProps {
  avatarUrl?: string;
  userName: string;
  onToggleTheme: () => void;
  isDarkMode: boolean;
  role: string;
}

const UserMenuModal: React.FC<UserMenuModalProps> = ({
  avatarUrl,
  userName,
  onToggleTheme,
  isDarkMode,
  role,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const { logout, isLoggingOut } = useAuth();
  const SCHOOL_ADMIN_URL = "/school-admin/settings"
  const SUPER_ADMIN_URL ="/super-admin/settings"
  const TEACHER_URL = "/teacher-dashboard/settings"

  const toggleModal = () => {
    setIsOpen(!isOpen);
  };
  // Fermer le modal lorsque l'utilisateur clique en dehors
  const handleClickOutside = (event: React.MouseEvent<HTMLDivElement>) => {
    if (event.target !== event.currentTarget) {
      setIsOpen(false);
    }
  };

    // Determine the settings URL based on the user's role
    const getSettingsUrl = () => {
        switch (role) {
            case "dean_of_studies":
              return SCHOOL_ADMIN_URL;
            case "bursar":
              return SCHOOL_ADMIN_URL;
            case "school_admin":
              return SCHOOL_ADMIN_URL;
            case "admin": 
                return SCHOOL_ADMIN_URL;
            case "super":
                return SUPER_ADMIN_URL;
            case "teacher":
                return TEACHER_URL;
            default:
                // Fallback for any other roles or if role is not defined
                return "/dashboard/settings"; // Or a more generic settings page
        }
    };

  return (
    <div className="relative">
      {/* Avatar (bouton pour ouvrir le modal) */}
      <button
        onClick={toggleModal}
        className="flex items-center space-x-2 focus:outline-none w-max"
      >
        <div className="w-8 h-8 rounded-full bg-gray-300 flex items-center justify-center text-gray-600 font-semibold">
          {avatarUrl ?
            <img src={avatarUrl} alt={userName} className="w-full h-full rounded-full object-fit" />
            : userName.charAt(0).toUpperCase()}
        </div>
        <span className="text-foreground">{userName}</span>
      </button>

      {/* Modal */}
      {isOpen && (
        <div className="absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-gray-100 text-gray-800 ring-white dark:bg-gray-800 dark:text-white ring-1 dark:ring-black ring-opacity-5 z-50"
          onMouseLeave={handleClickOutside}
        >
          <div className="py-1">
            {/* Nom de l'utilisateur */}
            <div className="px-4 py-2 text-sm border-b dark:border-gray-700 border-gray-300">
              {userName}
            </div>

            {/* Options */}
            <div className="flex items-center justify-between px-1  py-1 text-sm  border-b dark:border-gray-700 border-gray-300">
              <DarkModeToggle>
              </DarkModeToggle>
            </div>


            <Link href={getSettingsUrl()} className="flex items-center justify-between px-4 py-2 text-sm hover:bg-gray-300 dark:hover:bg-gray-700 cursor-pointer">
              <span>Settings</span>
              <Settings size={20} />
            </Link>


            <div
              onClick={async () => {
                if (!isLoggingOut) {
                  setIsOpen(false);
                  await logout();
                }
              }}
              className={`flex items-center px-4 py-2 text-sm border-t dark:border-gray-700 border-gray-300 ${
                isLoggingOut
                  ? 'cursor-not-allowed opacity-50'
                  : 'hover:bg-gray-300 dark:hover:bg-gray-700 cursor-pointer'
              }`}
            >
              {isLoggingOut ? (
                <Loader2 size={16} className="mr-2 animate-spin" />
              ) : (
                <LogOut size={16} className="mr-2" />
              )}
              <span>{isLoggingOut ? "Signing out..." : "Sign out"}</span>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default UserMenuModal;