"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  MessageCircle,
  Bell,
  FileText,
  CheckCircle,
  ArrowRight,
  Send,
  Users,
  Calendar,
  Settings,
  Eye,
  Edit,
  Plus,
  Download,
  Archive,
  Search
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function CommunicationPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'messaging', name: 'Messaging', icon: MessageCircle },
    { id: 'announcements', name: 'Announcements', icon: Bell },
    { id: 'notifications', name: 'Notifications', icon: Bell },
    { id: 'templates', name: 'Templates', icon: FileText },
    { id: 'history', name: 'History', icon: Archive },
  ];

  const features = [
    {
      title: 'Parent-Teacher Messaging',
      description: 'Secure and efficient communication between parents and teachers with real-time messaging.',
      icon: MessageCircle,
      benefits: [
        'Real-time messaging platform',
        'Secure message encryption',
        'Message read receipts',
        'File and media sharing',
        'Message search and filtering',
        'Conversation history'
      ]
    },
    {
      title: 'Announcement System',
      description: 'Broadcast important announcements to parents, teachers, and students with targeted delivery.',
      icon: Bell,
      benefits: [
        'Bulk announcement creation',
        'Targeted audience selection',
        'Scheduled announcements',
        'Announcement templates',
        'Delivery confirmation',
        'Announcement archives'
      ]
    },
    {
      title: 'Notification Management',
      description: 'Comprehensive notification system with customizable alerts and delivery preferences.',
      icon: Bell,
      benefits: [
        'Customizable notification types',
        'Delivery method preferences',
        'Notification scheduling',
        'Priority notification system',
        'Notification history',
        'Opt-out management'
      ]
    },
    {
      title: 'Communication History',
      description: 'Complete audit trail of all communications with search and filtering capabilities.',
      icon: Archive,
      benefits: [
        'Complete message history',
        'Advanced search functionality',
        'Export capabilities',
        'Communication analytics',
        'Audit trail logging',
        'Data retention policies'
      ]
    },
    {
      title: 'Message Templates',
      description: 'Pre-built message templates for common communications to save time and ensure consistency.',
      icon: FileText,
      benefits: [
        'Customizable templates',
        'Template categories',
        'Quick message insertion',
        'Template sharing',
        'Version control',
        'Template analytics'
      ]
    },
    {
      title: 'Bulk Messaging',
      description: 'Send messages to multiple recipients simultaneously with advanced targeting options.',
      icon: Send,
      benefits: [
        'Bulk message creation',
        'Recipient filtering',
        'Scheduled sending',
        'Delivery tracking',
        'Message personalization',
        'Bulk message reports'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Send Message',
      description: 'Send a new message to parents or teachers',
      icon: Send,
      action: () => console.log('Send message')
    },
    {
      title: 'Create Announcement',
      description: 'Create and broadcast an announcement',
      icon: Bell,
      action: () => console.log('Create announcement')
    },
    {
      title: 'Message Templates',
      description: 'Manage message templates',
      icon: FileText,
      action: () => console.log('Manage templates')
    },
    {
      title: 'Communication History',
      description: 'View communication history and reports',
      icon: Archive,
      action: () => console.log('View history')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Features" />
      
      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-4 sm:mb-6">
              <div className="p-3 sm:p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <MessageCircle className="w-8 h-8 sm:w-12 sm:h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
              Communication Tools
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4">
              Built-in communication tools for parent-teacher communication and announcements. 
              Streamline communication with powerful messaging and notification features.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-6 sm:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-8 sm:space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 sm:p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3 sm:mr-4">
                            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-1 sm:space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-teal-500 mr-2 flex-shrink-0" />
                              <span className="line-clamp-2">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-4 sm:p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-2 sm:mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-2 sm:mr-3">
                            <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 text-sm sm:text-base">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'messaging' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Parent-Teacher Messaging
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Message Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Real-time messaging platform</li>
                      <li>• Secure message encryption</li>
                      <li>• Message read receipts</li>
                      <li>• File and media sharing</li>
                      <li>• Message search and filtering</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Communication Tools
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Conversation history</li>
                      <li>• Message templates</li>
                      <li>• Bulk messaging</li>
                      <li>• Notification alerts</li>
                      <li>• Message archiving</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'announcements' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Announcement System
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Announcement Creation
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Bulk announcement creation</li>
                      <li>• Targeted audience selection</li>
                      <li>• Scheduled announcements</li>
                      <li>• Announcement templates</li>
                      <li>• Rich text formatting</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Delivery Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Delivery confirmation</li>
                      <li>• Announcement archives</li>
                      <li>• Read receipt tracking</li>
                      <li>• Priority announcements</li>
                      <li>• Announcement analytics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'notifications' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Notification Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Notification Types
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Customizable notification types</li>
                      <li>• Delivery method preferences</li>
                      <li>• Notification scheduling</li>
                      <li>• Priority notification system</li>
                      <li>• Notification history</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      User Preferences
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Opt-out management</li>
                      <li>• Notification frequency</li>
                      <li>• Channel preferences</li>
                      <li>• Quiet hours settings</li>
                      <li>• Notification grouping</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'templates' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Message Templates
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Template Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Customizable templates</li>
                      <li>• Template categories</li>
                      <li>• Quick message insertion</li>
                      <li>• Template sharing</li>
                      <li>• Version control</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Template Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Template analytics</li>
                      <li>• Personalization variables</li>
                      <li>• Template approval workflow</li>
                      <li>• Template search</li>
                      <li>• Template export/import</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'history' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Communication History
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      History Features
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Complete message history</li>
                      <li>• Advanced search functionality</li>
                      <li>• Export capabilities</li>
                      <li>• Communication analytics</li>
                      <li>• Audit trail logging</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Data Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Data retention policies</li>
                      <li>• Privacy controls</li>
                      <li>• Backup and recovery</li>
                      <li>• Compliance reporting</li>
                      <li>• Data archiving</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 dark:bg-teal-600 text-white py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6">
            Ready to Improve Your Communication?
          </h2>
          <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-teal-100 dark:text-teal-100 max-w-3xl mx-auto">
            Start using these powerful communication tools to enhance parent-teacher relationships.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 dark:bg-white dark:text-teal-600 dark:hover:bg-gray-100 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-teal-600 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 