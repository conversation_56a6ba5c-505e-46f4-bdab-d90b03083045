"use client";

import { ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function UserInvitationPage() {
  const router = useRouter();

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <SharedNavigation showBackButton={true} />

      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            User Invitation Guide
          </h1>
          
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            Learn how to invite teachers, parents, and other users to your Scholarify platform.
          </p>

          <div className="space-y-8">
            <section>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Teacher Invitation
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                  <li>1. Navigate to Dashboard → Teachers → Add New Teacher</li>
                  <li>2. Enter teacher information (name, email, phone, subjects)</li>
                  <li>3. Set permissions and class assignments</li>
                  <li>4. Send invitation email with login credentials</li>
                </ol>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Parent Invitation
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                  <li>1. Select the student whose parent you want to invite</li>
                  <li>2. Add parent information (name, email, phone)</li>
                  <li>3. Link parent account to student record</li>
                  <li>4. Send invitation email with login credentials</li>
                </ol>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Bulk Invitation
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <ol className="space-y-3 text-gray-600 dark:text-gray-300">
                  <li>1. Download CSV template and fill in user information</li>
                  <li>2. Upload CSV file and select user type</li>
                  <li>3. Review and validate user details</li>
                  <li>4. Bulk send invitation emails</li>
                </ol>
              </div>
            </section>

            <section>
              <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
                Permission Management
              </h2>
              <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                <div className="space-y-4">
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">School Administrator</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Full system access, user management, system configuration</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Teacher</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">Class management, grade entry, attendance tracking</p>
                  </div>
                  <div>
                    <h3 className="font-semibold text-gray-900 dark:text-white mb-2">Parent</h3>
                    <p className="text-gray-600 dark:text-gray-300 text-sm">View student information, communicate with teachers, receive notifications</p>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>

      {/* Footer */}
      <SharedFooter />
    </div>
  );
} 