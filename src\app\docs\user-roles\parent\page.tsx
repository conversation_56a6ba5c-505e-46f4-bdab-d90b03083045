"use client";

import { useState } from 'react';
import { Users, GraduationCap, MessageSquare, CreditCard, FileText, ArrowRight, CheckCircle, AlertCircle, BookOpen, Eye } from 'lucide-react';
import Link from 'next/link';

const capabilities = [
  {
    category: 'Child Progress Monitoring',
    icon: Eye,
    description: 'Monitor your child\'s academic performance and development',
    features: [
      'View real-time grades and academic progress',
      'Track attendance records and patterns',
      'Monitor assignment completion and scores',
      'Review teacher feedback and comments',
      'Access progress reports and transcripts'
    ]
  },
  {
    category: 'Communication',
    icon: MessageSquare,
    description: 'Communicate with teachers and school administration',
    features: [
      'Send messages to teachers about concerns',
      'Receive school announcements and updates',
      'Schedule parent-teacher meetings',
      'Respond to teacher communications',
      'Access school-wide notifications'
    ]
  },
  {
    category: 'Financial Management',
    icon: CreditCard,
    description: 'Manage school fees and payments',
    features: [
      'View outstanding fee balances',
      'Make online fee payments',
      'Track payment history and receipts',
      'Set up payment plans and schedules',
      'Access financial statements and reports'
    ]
  },
  {
    category: 'Academic Records',
    icon: FileText,
    description: 'Access and review academic documentation',
    features: [
      'View report cards and transcripts',
      'Access attendance records',
      'Review assignment submissions',
      'Download academic documents',
      'Track academic milestones'
    ]
  },
  {
    category: 'School Information',
    icon: BookOpen,
    description: 'Access school-related information and resources',
    features: [
      'View school calendar and events',
      'Access school policies and guidelines',
      'Review class schedules and timetables',
      'Access educational resources',
      'Stay updated on school news'
    ]
  },
  {
    category: 'Profile Management',
    icon: Users,
    description: 'Manage your account and family information',
    features: [
      'Update contact information',
      'Manage multiple children\'s accounts',
      'Set communication preferences',
      'Update payment methods',
      'Manage account security settings'
    ]
  }
];

const permissions = [
  {
    module: 'Child Data',
    permissions: [
      'view_child_data',
      'view_child_progress',
      'view_child_attendance',
      'view_child_grades',
      'view_child_reports'
    ]
  },
  {
    module: 'Communication',
    permissions: [
      'communicate_school',
      'send_messages_teachers',
      'receive_announcements',
      'schedule_meetings',
      'view_notifications'
    ]
  },
  {
    module: 'Financial',
    permissions: [
      'view_fee_balances',
      'make_payments',
      'view_payment_history',
      'manage_payment_methods',
      'view_financial_reports'
    ]
  },
  {
    module: 'Academic Records',
    permissions: [
      'view_report_cards',
      'view_attendance_records',
      'view_assignments',
      'download_documents',
      'view_transcripts'
    ]
  },
  {
    module: 'School Information',
    permissions: [
      'view_school_calendar',
      'view_school_policies',
      'view_class_schedules',
      'access_resources',
      'view_school_news'
    ]
  },
  {
    module: 'Profile',
    permissions: [
      'update_profile',
      'manage_children',
      'update_contact_info',
      'manage_preferences',
      'update_security'
    ]
  }
];

const quickActions = [
  {
    title: 'View Child Progress',
    description: 'Monitor academic performance and grades',
    href: '/parent-dashboard/progress',
    icon: Eye
  },
  {
    title: 'Communicate',
    description: 'Send messages to teachers and school',
    href: '/parent-dashboard/communication',
    icon: MessageSquare
  },
  {
    title: 'Pay Fees',
    description: 'Manage school fees and payments',
    href: '/parent-dashboard/finance',
    icon: CreditCard
  },
  {
    title: 'Academic Records',
    description: 'View report cards and transcripts',
    href: '/parent-dashboard/records',
    icon: FileText
  }
];

export default function ParentGuide() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <Users className="h-8 w-8 text-purple-600 mr-3" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Parent Guide</h1>
              <p className="text-xl text-gray-600">Complete guide for monitoring your child's progress and communicating with the school</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Role Overview */}
        <div className="bg-gradient-to-r from-purple-50 to-pink-50 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-purple-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Role Overview</h2>
              <p className="text-gray-700 mb-4">
                Parents have access to monitor their children's academic progress, communicate with teachers, 
                manage school fees, and stay informed about school activities. This role focuses on parental 
                involvement and oversight of their children's education.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Access Level</div>
                  <div className="text-purple-600 font-medium">Child-Specific Access</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Children Managed</div>
                  <div className="text-purple-600 font-medium">Own Children Only</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Communication</div>
                  <div className="text-purple-600 font-medium">With Teachers & School</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'overview', name: 'Overview', icon: BookOpen },
              { id: 'capabilities', name: 'Capabilities', icon: Users },
              { id: 'permissions', name: 'Permissions', icon: Eye },
              { id: 'quick-actions', name: 'Quick Actions', icon: ArrowRight }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-purple-500 text-purple-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200">
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Parent Overview</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Responsibilities</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Monitor children's academic progress regularly</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Communicate with teachers about concerns</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Manage school fee payments and finances</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Stay informed about school activities</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Support children's educational development</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Areas</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Personal parent dashboard</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Children's academic records and progress</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Communication tools with teachers</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Financial management and payment tools</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>School information and announcements</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'capabilities' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Parent Capabilities</h2>
              
              <div className="space-y-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon;
                  
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start mb-4">
                        <div className="p-2 bg-purple-50 rounded-lg mr-4">
                          <Icon className="h-6 w-6 text-purple-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{capability.category}</h3>
                          <p className="text-gray-600">{capability.description}</p>
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {capability.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Permissions</h2>
              
              <div className="space-y-6">
                {permissions.map((module, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{module.module}</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {module.permissions.map((permission, permIndex) => (
                        <div key={permIndex} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 font-mono bg-gray-100 px-2 py-1 rounded">
                            {permission}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'quick-actions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  
                  return (
                    <Link
                      key={index}
                      href={action.href}
                      className="group border border-gray-200 rounded-lg p-6 hover:border-purple-300 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          <div className="p-2 bg-purple-50 rounded-lg mr-4">
                            <Icon className="h-6 w-6 text-purple-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 group-hover:text-purple-600 transition-colors">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-purple-600 transition-colors" />
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Best Practices */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Practices</h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Check your child's progress regularly, at least weekly</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Communicate proactively with teachers about concerns</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Keep your contact information updated</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Stay informed about school events and announcements</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Documentation */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Documentation</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <Link href="/docs/user-roles/teacher" className="text-blue-600 hover:text-blue-800 text-sm">
              → Teacher Guide
            </Link>
            <Link href="/docs/user-roles/school-admin" className="text-blue-600 hover:text-blue-800 text-sm">
              → School Administrator Guide
            </Link>
            <Link href="/docs/features/communication" className="text-blue-600 hover:text-blue-800 text-sm">
              → Communication Tools
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 