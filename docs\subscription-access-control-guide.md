# Guide d'utilisation du système de contrôle d'accès basé sur les abonnements

## Vue d'ensemble

Ce système permet de contrôler l'accès aux fonctionnalités de l'application en fonction du niveau d'abonnement de l'école. Il fournit une approche granulaire pour gérer les fonctionnalités premium et encourager les mises à niveau d'abonnement.

## Architecture du système

### Composants principaux

1. **FeatureRegistry** - Registre centralisé de toutes les fonctionnalités
2. **SubscriptionMiddleware** - Middleware pour la validation d'accès
3. **FeatureRegistryServices** - Services pour les appels API
4. **Composants UI** - FeatureGate, FeatureButton, SubscriptionStatus
5. **Hooks React** - useSubscription, useFeatureAccess

## Configuration initiale

### 1. Installation des dépendances

```bash
npm install node-cache@5.1.2
```

### 2. Initialisation de la base de données

```bash
# Exécuter le script de seeding
node src/scripts/seedFeatureRegistry.js
```

### 3. Configuration des variables d'environnement

```env
MONGODB_URI=mongodb://localhost:27017/scholarify
NEXT_PUBLIC_API_URL=http://localhost:5000/api
```

## Utilisation côté Backend

### Protection d'endpoints avec middleware

```javascript
const SubscriptionMiddleware = require('../middleware/subscriptionMiddleware');

// Protection simple d'une fonctionnalité
router.post('/api/student/import-csv',
  authenticate,
  authorize(['school_admin']),
  SubscriptionMiddleware.checkFeatureAccess('students_bulk_operations'),
  async (req, res) => {
    // Logique métier ici
    // req.feature et req.subscription sont disponibles
  }
);

// Protection avec plusieurs fonctionnalités (OR)
router.get('/api/reports/advanced',
  authenticate,
  SubscriptionMiddleware.checkAnyFeatureAccess([
    'usage_analytics',
    'advanced_reporting'
  ]),
  async (req, res) => {
    // req.accessibleFeatures contient les fonctionnalités accessibles
  }
);
```

### Vérification manuelle dans les services

```javascript
const SubscriptionMiddleware = require('../middleware/subscriptionMiddleware');

class MyService {
  static async performAction(schoolId, userId) {
    // Vérification d'accès
    const accessResult = await SubscriptionMiddleware.validateFeatureAccess(
      'feature_id',
      schoolId,
      { id: userId }
    );
    
    if (!accessResult.hasAccess) {
      throw new Error(`Accès refusé: ${accessResult.reason}`);
    }
    
    // Logique métier...
  }
}
```

### Réponses standardisées

```javascript
const ResponseFormatter = require('../utils/responseFormatter');

// Succès
return ResponseFormatter.success(res, data, 'Message de succès');

// Erreur d'abonnement
return ResponseFormatter.subscriptionRequired(res, featureId, requiredLevel, currentLevel);

// Fonctionnalité non trouvée
return ResponseFormatter.featureNotFound(res, featureId);

// Erreur de validation
return ResponseFormatter.validationError(res, errors);
```

## Utilisation côté Frontend

### Hook useSubscription

```typescript
import { useSubscription } from '@/hooks/useSubscription';

function MyComponent() {
  const {
    subscription,
    loading,
    error,
    checkFeatureAccess,
    hasFeatureAccess,
    upgradeSubscription
  } = useSubscription();
  
  // Vérification d'accès asynchrone
  const handleAction = async () => {
    const access = await checkFeatureAccess('students_bulk_operations');
    if (access.hasAccess) {
      // Exécuter l'action
    } else {
      // Afficher prompt de mise à niveau
    }
  };
  
  // Vérification synchrone (utilise le cache)
  const canExport = hasFeatureAccess('grades_export');
}
```

### Composant FeatureGate

```tsx
import FeatureGate from '@/components/subscription/FeatureGate';

function StudentManagement() {
  return (
    <div>
      {/* Fonctionnalité de base - toujours visible */}
      <button>Ajouter un étudiant</button>
      
      {/* Fonctionnalité premium - conditionnelle */}
      <FeatureGate featureId="students_bulk_operations">
        <button>Importer CSV</button>
      </FeatureGate>
      
      {/* Avec fallback personnalisé */}
      <FeatureGate 
        featureId="students_id_card_generation"
        fallback={<div>Mise à niveau requise pour les cartes d'identité</div>}
      >
        <IDCardGenerator />
      </FeatureGate>
    </div>
  );
}
```

### Composant FeatureButton

```tsx
import FeatureButton from '@/components/subscription/FeatureButton';

function ActionButtons() {
  return (
    <div>
      <FeatureButton
        featureId="students_bulk_operations"
        onClick={handleImport}
        variant="primary"
      >
        Importer CSV
      </FeatureButton>
      
      <FeatureButton
        featureId="students_id_card_generation"
        onClick={handleIDCards}
        onUpgradeClick={() => window.location.href = '/upgrade'}
      >
        Générer cartes d'identité
      </FeatureButton>
    </div>
  );
}
```

### Composant SubscriptionStatus

```tsx
import SubscriptionStatus from '@/components/subscription/SubscriptionStatus';

function Dashboard() {
  return (
    <div>
      {/* Affichage complet */}
      <SubscriptionStatus 
        showDetails={true}
        showUpgradeButton={true}
      />
      
      {/* Affichage compact */}
      <SubscriptionStatus 
        compact={true}
        className="mb-4"
      />
    </div>
  );
}
```

## Gestion des fonctionnalités

### Ajout d'une nouvelle fonctionnalité

1. **Ajouter au registre des fonctionnalités**

```javascript
const newFeature = {
  feature_id: 'new_feature_id',
  name: 'Nouvelle fonctionnalité',
  description: 'Description de la fonctionnalité',
  module: 'students',
  category: 'enhanced',
  subscription_level: 'standard',
  api_endpoints: [
    { method: 'POST', path: '/api/new-endpoint', description: 'Nouvel endpoint' }
  ],
  permissions_required: [
    { module: 'students', permission: 'manage_students' }
  ]
};

await FeatureRegistryServices.createOrUpdateFeature(newFeature);
```

2. **Protéger les endpoints**

```javascript
router.post('/api/new-endpoint',
  authenticate,
  authorize(['school_admin']),
  SubscriptionMiddleware.checkFeatureAccess('new_feature_id'),
  async (req, res) => {
    // Logique de la nouvelle fonctionnalité
  }
);
```

3. **Ajouter les traductions**

```json
// fr/common.json
{
  "subscription": {
    "features": {
      "new_feature_id": "Nouvelle fonctionnalité"
    }
  }
}
```

4. **Utiliser dans l'interface**

```tsx
<FeatureGate featureId="new_feature_id">
  <NewFeatureComponent />
</FeatureGate>
```

### Modification des niveaux d'abonnement

```javascript
// Changer le niveau requis pour une fonctionnalité
await FeatureRegistryServices.createOrUpdateFeature({
  feature_id: 'existing_feature',
  subscription_level: 'premium' // Changé de 'standard' à 'premium'
});

// Vider le cache pour appliquer les changements
await FeatureRegistryServices.clearCache();
```

## Niveaux d'abonnement

### Basic
- Fonctionnalités essentielles
- Gestion de base des étudiants, personnel, classes
- Enregistrement des notes et présences

### Standard
- Toutes les fonctionnalités Basic
- Analyses avancées
- Opérations en lot (import/export)
- Gestion financière
- Emplois du temps

### Premium
- Toutes les fonctionnalités Standard
- Génération de documents (cartes d'identité, bulletins)
- Rapports avancés
- Export de données
- Support prioritaire

### Enterprise
- Toutes les fonctionnalités Premium
- Fonctionnalités personnalisées
- Accès API
- Support dédié

## Bonnes pratiques

### 1. Gestion des erreurs

```javascript
try {
  const result = await checkFeatureAccess('feature_id');
  if (!result.hasAccess) {
    // Gérer l'accès refusé avec des messages clairs
    showUpgradePrompt(result.required_level);
  }
} catch (error) {
  // Gérer les erreurs de réseau/système
  console.error('Erreur de vérification d\'accès:', error);
}
```

### 2. Performance

```javascript
// Utiliser le cache pour les vérifications fréquentes
const { hasFeatureAccess } = useSubscription();
const canExport = hasFeatureAccess('grades_export'); // Utilise le cache

// Vérifier plusieurs fonctionnalités en une fois
const accessResults = await checkMultipleFeatures([
  'feature1', 'feature2', 'feature3'
]);
```

### 3. Expérience utilisateur

```tsx
// Toujours fournir des alternatives ou des explications
<FeatureGate 
  featureId="premium_feature"
  fallback={
    <div className="upgrade-prompt">
      <p>Cette fonctionnalité nécessite un abonnement Premium</p>
      <button onClick={upgradeSubscription}>Mettre à niveau</button>
    </div>
  }
>
  <PremiumFeature />
</FeatureGate>
```

### 4. Tests

```javascript
// Tester les différents niveaux d'abonnement
describe('Feature Access Control', () => {
  test('should allow basic features for all plans', async () => {
    const access = await checkFeatureAccess('students_crud_operations');
    expect(access.hasAccess).toBe(true);
  });
  
  test('should restrict premium features', async () => {
    const access = await checkFeatureAccess('students_id_card_generation');
    expect(access.hasAccess).toBe(false);
    expect(access.required_level).toBe('premium');
  });
});
```

## Dépannage

### Problèmes courants

1. **Fonctionnalité non trouvée**
   - Vérifier que la fonctionnalité existe dans le registre
   - Exécuter le script de seeding si nécessaire

2. **Cache obsolète**
   - Vider le cache : `FeatureRegistryServices.clearCache()`
   - Redémarrer l'application

3. **Erreurs de permission**
   - Vérifier les permissions utilisateur
   - Vérifier l'association école-utilisateur

4. **Problèmes de traduction**
   - Vérifier que les clés de traduction existent
   - Vérifier la configuration du hook useTranslation

### Logs et monitoring

```javascript
// Activer les logs détaillés
console.log('Feature access check:', {
  featureId,
  schoolId,
  result: accessResult
});

// Surveiller les tentatives d'accès refusé
if (!accessResult.hasAccess) {
  console.warn('Access denied:', {
    feature: featureId,
    reason: accessResult.reason,
    required: accessResult.required_level,
    current: accessResult.current_level
  });
}
```
