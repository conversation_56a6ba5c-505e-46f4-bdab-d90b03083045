"use client";

import { FileText, Shield, Users, Globe, AlertTriangle, CheckCircle, Clock, Mail } from "lucide-react";
import SharedNavigation from "@/components/layout/SharedNavigation";
import SharedFooter from "@/components/layout/SharedFooter";
import { useRouter } from "next/navigation";

export default function TermsOfServicePage() {
    const router = useRouter();

    const sections = [
        {
            id: "acceptance",
            title: "Acceptance of Terms",
            icon: <CheckCircle className="w-6 h-6" />,
            content: [
                "By accessing and using Scholarify, you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.",
                "These Terms of Service govern your use of our school management platform and any related services provided by Scholarify."
            ]
        },
        {
            id: "description",
            title: "Description of Service",
            icon: <FileText className="w-6 h-6" />,
            content: [
                "Scholarify provides a comprehensive school management platform that includes:",
                "• Student information management and record keeping",
                "• Grade tracking and academic performance monitoring",
                "• Attendance management and reporting",
                "• Communication tools for teachers and parents",
                "• Administrative tools for school management",
                "• Mobile applications for various user types",
                "• Analytics and reporting capabilities"
            ]
        },
        {
            id: "user-accounts",
            title: "User Accounts and Registration",
            icon: <Users className="w-6 h-6" />,
            content: [
                "To access certain features of Scholarify, you must create an account. You agree to:",
                "• Provide accurate, current, and complete information during registration",
                "• Maintain and update your account information to keep it accurate",
                "• Protect your account credentials and not share them with others",
                "• Accept responsibility for all activities under your account",
                "• Notify us immediately of any unauthorized use of your account",
                "• Ensure you have the authority to create accounts for your organization"
            ]
        },
        {
            id: "acceptable-use",
            title: "Acceptable Use Policy",
            icon: <Shield className="w-6 h-6" />,
            content: [
                "You agree to use Scholarify only for lawful purposes and in accordance with these Terms. You agree not to:",
                "• Use the service for any illegal or unauthorized purpose",
                "• Violate any applicable laws or regulations",
                "• Infringe upon the rights of others",
                "• Upload or transmit harmful, offensive, or inappropriate content",
                "• Attempt to gain unauthorized access to our systems",
                "• Interfere with or disrupt the service or servers",
                "• Use the service to send spam or unsolicited communications",
                "• Reverse engineer or attempt to extract source code"
            ]
        },
        {
            id: "intellectual-property",
            title: "Intellectual Property Rights",
            icon: <FileText className="w-6 h-6" />,
            content: [
                "Scholarify and its original content, features, and functionality are owned by Scholarify and are protected by international copyright, trademark, patent, trade secret, and other intellectual property laws.",
                "You retain ownership of content you upload to the platform, but you grant us a license to use, store, and display that content as necessary to provide our services.",
                "You may not reproduce, distribute, modify, or create derivative works of our platform without our express written consent."
            ]
        },
        {
            id: "privacy",
            title: "Privacy and Data Protection",
            icon: <Shield className="w-6 h-6" />,
            content: [
                "Your privacy is important to us. Our collection and use of personal information is governed by our Privacy Policy, which is incorporated into these Terms by reference.",
                "We implement appropriate security measures to protect your data, but no method of transmission over the internet is 100% secure.",
                "You are responsible for maintaining the confidentiality of your account information and for all activities under your account."
            ]
        },
        {
            id: "payment",
            title: "Payment Terms",
            icon: <CheckCircle className="w-6 h-6" />,
            content: [
                "Subscription fees are billed in advance on a monthly or annual basis. You agree to pay all fees associated with your subscription.",
                "Prices may change with 30 days' notice. Continued use after price changes constitutes acceptance of new pricing.",
                "All payments are non-refundable except as required by law. We may offer refunds at our discretion.",
                "Failure to pay may result in suspension or termination of your account."
            ]
        },
        {
            id: "termination",
            title: "Termination and Cancellation",
            icon: <AlertTriangle className="w-6 h-6" />,
            content: [
                "You may cancel your account at any time through your account settings or by contacting our support team.",
                "We may terminate or suspend your account immediately, without prior notice, for conduct that we believe violates these Terms or is harmful to other users or our service.",
                "Upon termination, your right to use the service will cease immediately. We may delete your account and data, though we will retain data as required by law or our Privacy Policy."
            ]
        },
        {
            id: "disclaimers",
            title: "Disclaimers and Limitations",
            icon: <AlertTriangle className="w-6 h-6" />,
            content: [
                "THE SERVICE IS PROVIDED 'AS IS' AND 'AS AVAILABLE' WITHOUT WARRANTIES OF ANY KIND, EITHER EXPRESS OR IMPLIED.",
                "We do not guarantee that the service will be uninterrupted, secure, or error-free. We may modify, suspend, or discontinue the service at any time.",
                "IN NO EVENT SHALL SCHOLARIFY BE LIABLE FOR ANY INDIRECT, INCIDENTAL, SPECIAL, CONSEQUENTIAL, OR PUNITIVE DAMAGES.",
                "Our total liability to you for any claims arising from these Terms or your use of the service shall not exceed the amount you paid us in the 12 months preceding the claim."
            ]
        },
        {
            id: "indemnification",
            title: "Indemnification",
            icon: <Shield className="w-6 h-6" />,
            content: [
                "You agree to indemnify and hold harmless Scholarify, its officers, directors, employees, and agents from any claims, damages, losses, or expenses arising from:",
                "• Your use of the service",
                "• Your violation of these Terms",
                "• Your violation of any rights of another person or entity",
                "• Any content you upload or transmit through the service"
            ]
        },
        {
            id: "governing-law",
            title: "Governing Law and Disputes",
            icon: <Globe className="w-6 h-6" />,
            content: [
                "These Terms shall be governed by and construed in accordance with the laws of the jurisdiction where Scholarify is incorporated.",
                "Any disputes arising from these Terms or your use of the service shall be resolved through binding arbitration in accordance with the rules of the American Arbitration Association.",
                "You agree to resolve disputes individually and waive any right to participate in a class action lawsuit."
            ]
        },
        {
            id: "changes",
            title: "Changes to Terms",
            icon: <Clock className="w-6 h-6" />,
            content: [
                "We reserve the right to modify these Terms at any time. We will notify users of material changes by:",
                "• Posting the updated Terms on our platform",
                "• Sending email notifications to registered users",
                "• Displaying prominent notices on our website",
                "Your continued use of the service after such changes constitutes acceptance of the updated Terms."
            ]
        },
        {
            id: "contact",
            title: "Contact Information",
            icon: <Mail className="w-6 h-6" />,
            content: [
                "If you have any questions about these Terms of Service, please contact us:",
                "Email: <EMAIL>",
                "Phone: +****************",
                "Address: 123 Education Street, Tech City, TC 12345",
                "We will respond to your inquiry within 30 days."
            ]
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <SharedNavigation showBackButton={true} />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-r from-teal-600 to-teal-700 dark:from-teal-800 dark:to-teal-900">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                        Terms of Service
                    </h1>
                    <p className="text-xl text-teal-100 mb-4">
                        The terms and conditions governing your use of Scholarify
                    </p>
                    <p className="text-teal-200">
                        Last updated: December 2024
                    </p>
                </div>
            </section>

            {/* Content */}
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                    <div className="space-y-12">
                        {sections.map((section, index) => (
                            <section key={section.id} className="border-b border-gray-200 dark:border-gray-700 pb-8 last:border-b-0">
                                <div className="flex items-center mb-6">
                                    <div className="text-teal-600 dark:text-teal-400 mr-3">
                                        {section.icon}
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                        {section.title}
                                    </h2>
                                </div>
                                
                                <div className="space-y-4 text-gray-700 dark:text-gray-300 leading-relaxed">
                                    {section.content.map((paragraph, pIndex) => (
                                        <p key={pIndex} className={paragraph.startsWith('•') ? 'ml-4' : ''}>
                                            {paragraph}
                                        </p>
                                    ))}
                                </div>
                            </section>
                        ))}
                    </div>

                    {/* Additional Information */}
                    <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Important Notices
                        </h3>
                        <div className="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Legal Compliance
                                </h4>
                                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                                    <li>• FERPA Compliance</li>
                                    <li>• COPPA Compliance</li>
                                    <li>• GDPR Compliance</li>
                                    <li>• State Education Laws</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Related Documents
                                </h4>
                                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                                    <li>• Privacy Policy</li>
                                    <li>• Data Processing Agreement</li>
                                    <li>• Service Level Agreement</li>
                                    <li>• Security Policy</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Footer CTA */}
            <section className="bg-gray-100 dark:bg-gray-800 py-16">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Questions About Our Terms?
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                        Our legal team is here to help you understand our terms and conditions.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button
                            onClick={() => router.push("/contact")}
                            className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
                        >
                            Contact Us
                        </button>
                        <button
                            onClick={() => router.push("/privacy-policy")}
                            className="border border-teal-600 text-teal-600 dark:text-teal-400 hover:bg-teal-50 dark:hover:bg-teal-900/20 px-8 py-3 rounded-lg font-medium transition-colors"
                        >
                            Privacy Policy
                        </button>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <SharedFooter />
        </div>
    );
} 