"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  CheckCircle, 
  XCircle, 
  Trash2, 
  Download, 
  Upload, 
  RefreshCw,
  AlertTriangle,
  Settings
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface BulkActionsPanelProps {
  selectedCount: number;
  selectedFeatures: string[];
  onAction: (action: string, featureIds: string[]) => Promise<void>;
}

/**
 * Panneau d'actions en lot pour les fonctionnalités sélectionnées
 */
const BulkActionsPanel: React.FC<BulkActionsPanelProps> = ({
  selectedCount,
  selectedFeatures,
  onAction
}) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState<string | null>(null);

  const handleAction = async (action: string) => {
    if (selectedFeatures.length === 0) return;

    setLoading(action);
    try {
      await onAction(action, selectedFeatures);
    } catch (error) {
      console.error(`Error performing bulk action ${action}:`, error);
    } finally {
      setLoading(null);
    }
  };

  const actions = [
    {
      id: 'enable',
      label: 'Activer',
      icon: CheckCircle,
      color: 'bg-green-600 hover:bg-green-700',
      description: 'Activer les fonctionnalités sélectionnées'
    },
    {
      id: 'disable',
      label: 'Désactiver',
      icon: XCircle,
      color: 'bg-yellow-600 hover:bg-yellow-700',
      description: 'Désactiver les fonctionnalités sélectionnées'
    },
    {
      id: 'export',
      label: 'Exporter',
      icon: Download,
      color: 'bg-blue-600 hover:bg-blue-700',
      description: 'Exporter la configuration des fonctionnalités'
    },
    {
      id: 'duplicate',
      label: 'Dupliquer',
      icon: RefreshCw,
      color: 'bg-indigo-600 hover:bg-indigo-700',
      description: 'Créer des copies des fonctionnalités sélectionnées'
    },
    {
      id: 'delete',
      label: 'Supprimer',
      icon: Trash2,
      color: 'bg-red-600 hover:bg-red-700',
      description: 'Supprimer définitivement les fonctionnalités',
      dangerous: true
    }
  ];

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 mb-6"
    >
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="flex items-center space-x-2">
            <Settings className="w-5 h-5 text-blue-600" />
            <span className="font-medium text-gray-900 dark:text-gray-100">
              Actions en lot
            </span>
          </div>
          <div className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-400 px-2 py-1 rounded-full text-sm">
            {selectedCount} sélectionnée{selectedCount > 1 ? 's' : ''}
          </div>
        </div>

        <div className="flex items-center space-x-2">
          {actions.map((action) => {
            const Icon = action.icon;
            const isLoading = loading === action.id;
            
            return (
              <div key={action.id} className="relative group">
                <button
                  onClick={() => handleAction(action.id)}
                  disabled={isLoading || selectedFeatures.length === 0}
                  className={`
                    px-3 py-2 text-white rounded-lg transition-all duration-200 flex items-center space-x-2
                    ${action.color}
                    ${isLoading || selectedFeatures.length === 0 
                      ? 'opacity-50 cursor-not-allowed' 
                      : 'hover:shadow-md transform hover:scale-105'
                    }
                    ${action.dangerous ? 'ring-2 ring-red-200 dark:ring-red-800' : ''}
                  `}
                  title={action.description}
                >
                  {isLoading ? (
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  ) : (
                    <Icon className="w-4 h-4" />
                  )}
                  <span className="text-sm font-medium">{action.label}</span>
                </button>

                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 px-3 py-2 bg-gray-900 dark:bg-gray-700 text-white text-xs rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-200 pointer-events-none whitespace-nowrap z-10">
                  {action.description}
                  <div className="absolute top-full left-1/2 transform -translate-x-1/2 border-4 border-transparent border-t-gray-900 dark:border-t-gray-700"></div>
                </div>
              </div>
            );
          })}
        </div>
      </div>

      {/* Avertissement pour les actions dangereuses */}
      {selectedFeatures.length > 0 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="flex items-start space-x-2 text-sm text-gray-600 dark:text-gray-400">
            <AlertTriangle className="w-4 h-4 text-yellow-500 mt-0.5 flex-shrink-0" />
            <div>
              <p className="font-medium text-gray-700 dark:text-gray-300">
                Actions disponibles pour {selectedCount} fonctionnalité{selectedCount > 1 ? 's' : ''}
              </p>
              <p className="mt-1">
                Les actions en lot affecteront toutes les fonctionnalités sélectionnées. 
                Certaines actions comme la suppression sont irréversibles.
              </p>
            </div>
          </div>
        </motion.div>
      )}

      {/* Détails des fonctionnalités sélectionnées */}
      {selectedFeatures.length > 0 && selectedFeatures.length <= 5 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p className="font-medium text-gray-700 dark:text-gray-300 mb-2">
              Fonctionnalités sélectionnées :
            </p>
            <div className="flex flex-wrap gap-2">
              {selectedFeatures.map((featureId) => (
                <span
                  key={featureId}
                  className="bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 px-2 py-1 rounded text-xs font-mono"
                >
                  {featureId}
                </span>
              ))}
            </div>
          </div>
        </motion.div>
      )}

      {/* Indicateur pour beaucoup de sélections */}
      {selectedFeatures.length > 5 && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          className="mt-3 pt-3 border-t border-gray-200 dark:border-gray-700"
        >
          <div className="text-sm text-gray-600 dark:text-gray-400">
            <p className="font-medium text-gray-700 dark:text-gray-300">
              {selectedCount} fonctionnalités sélectionnées
            </p>
            <p className="mt-1">
              Trop de fonctionnalités pour les afficher individuellement. 
              Utilisez les actions en lot pour les gérer efficacement.
            </p>
          </div>
        </motion.div>
      )}
    </motion.div>
  );
};

export default BulkActionsPanel;
