"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  X, 
  Save, 
  Plus, 
  Trash2, 
  AlertCircle,
  Info,
  Settings,
  Shield,
  Zap,
  Crown,
  Star
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { FeatureInfo } from '@/app/services/FeatureRegistryServices';

interface FeatureModalProps {
  feature?: FeatureInfo | null;
  onSave: () => void;
  onClose: () => void;
}

interface FeatureFormData {
  feature_id: string;
  name: string;
  description: string;
  module: string;
  category: string;
  subscription_level: string;
  status: string;
  api_endpoints: string[];
  ui_components: string[];
  usage_limits: {
    daily_limit?: number;
    monthly_limit?: number;
    concurrent_limit?: number;
  };
  metadata: {
    version: string;
    dependencies: string[];
    tags: string[];
  };
}

/**
 * Modal pour créer/éditer une fonctionnalité
 */
const FeatureModal: React.FC<FeatureModalProps> = ({ feature, onSave, onClose }) => {
  const { t } = useTranslation();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const [formData, setFormData] = useState<FeatureFormData>({
    feature_id: '',
    name: '',
    description: '',
    module: '',
    category: '',
    subscription_level: 'basic',
    status: 'active',
    api_endpoints: [],
    ui_components: [],
    usage_limits: {},
    metadata: {
      version: '1.0.0',
      dependencies: [],
      tags: []
    }
  });

  const [newEndpoint, setNewEndpoint] = useState('');
  const [newComponent, setNewComponent] = useState('');
  const [newDependency, setNewDependency] = useState('');
  const [newTag, setNewTag] = useState('');

  // Charger les données de la fonctionnalité si en mode édition
  useEffect(() => {
    if (feature) {
      setFormData({
        feature_id: feature.feature_id,
        name: feature.name,
        description: feature.description,
        module: feature.module,
        category: feature.category,
        subscription_level: feature.subscription_level,
        status: feature.status,
        api_endpoints: feature.api_endpoints || [],
        ui_components: feature.ui_components || [],
        usage_limits: feature.usage_limits || {},
        metadata: {
          version: feature.metadata?.version || '1.0.0',
          dependencies: feature.metadata?.dependencies || [],
          tags: feature.metadata?.tags || []
        }
      });
    }
  }, [feature]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      // Validation
      if (!formData.feature_id || !formData.name || !formData.description) {
        throw new Error('Veuillez remplir tous les champs obligatoires');
      }

      // Ici, vous ajouteriez l'appel API pour créer/mettre à jour la fonctionnalité
      console.log('Saving feature:', formData);
      
      // Simuler un délai
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      onSave();
    } catch (err) {
      console.error('Error saving feature:', err);
      setError(err instanceof Error ? err.message : 'Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const addEndpoint = () => {
    if (newEndpoint.trim()) {
      setFormData(prev => ({
        ...prev,
        api_endpoints: [...prev.api_endpoints, newEndpoint.trim()]
      }));
      setNewEndpoint('');
    }
  };

  const removeEndpoint = (index: number) => {
    setFormData(prev => ({
      ...prev,
      api_endpoints: prev.api_endpoints.filter((_, i) => i !== index)
    }));
  };

  const addComponent = () => {
    if (newComponent.trim()) {
      setFormData(prev => ({
        ...prev,
        ui_components: [...prev.ui_components, newComponent.trim()]
      }));
      setNewComponent('');
    }
  };

  const removeComponent = (index: number) => {
    setFormData(prev => ({
      ...prev,
      ui_components: prev.ui_components.filter((_, i) => i !== index)
    }));
  };

  const addDependency = () => {
    if (newDependency.trim()) {
      setFormData(prev => ({
        ...prev,
        metadata: {
          ...prev.metadata,
          dependencies: [...prev.metadata.dependencies, newDependency.trim()]
        }
      }));
      setNewDependency('');
    }
  };

  const removeDependency = (index: number) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        dependencies: prev.metadata.dependencies.filter((_, i) => i !== index)
      }
    }));
  };

  const addTag = () => {
    if (newTag.trim()) {
      setFormData(prev => ({
        ...prev,
        metadata: {
          ...prev.metadata,
          tags: [...prev.metadata.tags, newTag.trim()]
        }
      }));
      setNewTag('');
    }
  };

  const removeTag = (index: number) => {
    setFormData(prev => ({
      ...prev,
      metadata: {
        ...prev.metadata,
        tags: prev.metadata.tags.filter((_, i) => i !== index)
      }
    }));
  };

  const getSubscriptionIcon = (level: string) => {
    switch (level) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-4 h-4 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-4 h-4 text-blue-500" />;
      default:
        return <Star className="w-4 h-4 text-gray-500" />;
    }
  };

  return (
    <AnimatePresence>
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
        <motion.div
          initial={{ opacity: 0, scale: 0.95 }}
          animate={{ opacity: 1, scale: 1 }}
          exit={{ opacity: 0, scale: 0.95 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
            <div className="flex items-center space-x-3">
              <Settings className="w-6 h-6 text-blue-600" />
              <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
                {feature ? 'Modifier la fonctionnalité' : 'Nouvelle fonctionnalité'}
              </h2>
            </div>
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 dark:hover:text-gray-300 transition-colors"
            >
              <X className="w-5 h-5" />
            </button>
          </div>

          {/* Content */}
          <div className="overflow-y-auto max-h-[calc(90vh-140px)]">
            <form onSubmit={handleSubmit} className="p-6 space-y-6">
              {/* Erreur */}
              {error && (
                <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 flex items-center space-x-2">
                  <AlertCircle className="w-5 h-5 text-red-600 dark:text-red-400" />
                  <span className="text-red-700 dark:text-red-400">{error}</span>
                </div>
              )}

              {/* Informations de base */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    ID de la fonctionnalité *
                  </label>
                  <input
                    type="text"
                    value={formData.feature_id}
                    onChange={(e) => setFormData(prev => ({ ...prev, feature_id: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                    placeholder="ex: student_management"
                    disabled={!!feature} // Désactiver en mode édition
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Nom *
                  </label>
                  <input
                    type="text"
                    value={formData.name}
                    onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                    placeholder="Nom de la fonctionnalité"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Module *
                  </label>
                  <select
                    value={formData.module}
                    onChange={(e) => setFormData(prev => ({ ...prev, module: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  >
                    <option value="">Sélectionner un module</option>
                    <option value="students">Étudiants</option>
                    <option value="staff">Personnel</option>
                    <option value="classes">Classes</option>
                    <option value="announcements">Annonces</option>
                    <option value="reports">Rapports</option>
                    <option value="settings">Paramètres</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Catégorie *
                  </label>
                  <input
                    type="text"
                    value={formData.category}
                    onChange={(e) => setFormData(prev => ({ ...prev, category: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                    placeholder="ex: management, reporting, communication"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Niveau d'abonnement *
                  </label>
                  <div className="relative">
                    <select
                      value={formData.subscription_level}
                      onChange={(e) => setFormData(prev => ({ ...prev, subscription_level: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100 pl-10"
                    >
                      <option value="basic">Basic</option>
                      <option value="standard">Standard</option>
                      <option value="premium">Premium</option>
                      <option value="enterprise">Enterprise</option>
                    </select>
                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                      {getSubscriptionIcon(formData.subscription_level)}
                    </div>
                  </div>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                    Statut *
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData(prev => ({ ...prev, status: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  >
                    <option value="active">Actif</option>
                    <option value="inactive">Inactif</option>
                  </select>
                </div>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Description *
                </label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                  rows={3}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                  placeholder="Description détaillée de la fonctionnalité"
                />
              </div>

              {/* Endpoints API */}
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                  Endpoints API
                </label>
                <div className="space-y-2">
                  <div className="flex space-x-2">
                    <input
                      type="text"
                      value={newEndpoint}
                      onChange={(e) => setNewEndpoint(e.target.value)}
                      className="flex-1 px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                      placeholder="/api/endpoint"
                      onKeyPress={(e) => e.key === 'Enter' && (e.preventDefault(), addEndpoint())}
                    />
                    <button
                      type="button"
                      onClick={addEndpoint}
                      className="px-3 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                    >
                      <Plus className="w-4 h-4" />
                    </button>
                  </div>
                  <div className="space-y-1">
                    {formData.api_endpoints.map((endpoint, index) => (
                      <div key={index} className="flex items-center justify-between bg-gray-50 dark:bg-gray-700 px-3 py-2 rounded">
                        <span className="text-sm font-mono text-gray-700 dark:text-gray-300">{endpoint}</span>
                        <button
                          type="button"
                          onClick={() => removeEndpoint(index)}
                          className="text-red-600 hover:text-red-700 dark:text-red-400 dark:hover:text-red-300"
                        >
                          <Trash2 className="w-4 h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </form>
          </div>

          {/* Footer */}
          <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
            >
              Annuler
            </button>
            <button
              onClick={handleSubmit}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center space-x-2"
            >
              {loading ? (
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              ) : (
                <Save className="w-4 h-4" />
              )}
              <span>{loading ? 'Sauvegarde...' : 'Sauvegarder'}</span>
            </button>
          </div>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default FeatureModal;
