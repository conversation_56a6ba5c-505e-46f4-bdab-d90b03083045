"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { Lock, Crown, Zap, Star } from 'lucide-react';
import { useFeatureAccess } from '@/hooks/useSubscription';
import { useTranslation } from '@/hooks/useTranslation';

interface FeatureButtonProps {
  featureId: string;
  children: React.ReactNode;
  onClick?: () => void;
  className?: string;
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost';
  size?: 'sm' | 'md' | 'lg';
  disabled?: boolean;
  showLockIcon?: boolean;
  onUpgradeClick?: () => void;
}

/**
 * FeatureButton Component
 * A button that automatically handles feature access control
 */
const FeatureButton: React.FC<FeatureButtonProps> = ({
  featureId,
  children,
  onClick,
  className = '',
  variant = 'primary',
  size = 'md',
  disabled = false,
  showLockIcon = true,
  onUpgradeClick
}) => {
  const { t } = useTranslation();
  const { hasAccess, loading, requiredLevel, reason } = useFeatureAccess(featureId);

  const getVariantClasses = () => {
    const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2';
    
    switch (variant) {
      case 'primary':
        return `${baseClasses} bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500`;
      case 'secondary':
        return `${baseClasses} bg-gray-600 text-white hover:bg-gray-700 focus:ring-gray-500`;
      case 'outline':
        return `${baseClasses} border border-gray-300 text-gray-700 bg-white hover:bg-gray-50 focus:ring-blue-500`;
      case 'ghost':
        return `${baseClasses} text-gray-700 hover:bg-gray-100 focus:ring-gray-500`;
      default:
        return baseClasses;
    }
  };

  const getSizeClasses = () => {
    switch (size) {
      case 'sm':
        return 'px-3 py-1.5 text-sm';
      case 'lg':
        return 'px-6 py-3 text-lg';
      default:
        return 'px-4 py-2 text-base';
    }
  };

  const getSubscriptionIcon = (level: string) => {
    switch (level) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-4 h-4" />;
      case 'standard':
        return <Zap className="w-4 h-4" />;
      default:
        return <Star className="w-4 h-4" />;
    }
  };

  const handleClick = () => {
    if (hasAccess && onClick) {
      onClick();
    } else if (!hasAccess && onUpgradeClick) {
      onUpgradeClick();
    } else if (!hasAccess) {
      // Default upgrade action
      const upgradeUrl = `/subscription/upgrade?plan=${requiredLevel}&feature=${featureId}`;
      window.location.href = upgradeUrl;
    }
  };

  const isDisabled = disabled || loading || (reason === 'error');
  const isLocked = !hasAccess && !loading;

  if (loading) {
    return (
      <button
        disabled
        className={`${getVariantClasses()} ${getSizeClasses()} opacity-50 cursor-not-allowed ${className}`}
      >
        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
        {children}
      </button>
    );
  }

  return (
    <motion.button
      whileHover={!isDisabled ? { scale: 1.02 } : {}}
      whileTap={!isDisabled ? { scale: 0.98 } : {}}
      onClick={handleClick}
      disabled={isDisabled}
      className={`
        ${getVariantClasses()} 
        ${getSizeClasses()} 
        ${isLocked ? 'relative overflow-hidden' : ''}
        ${isDisabled ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
        ${className}
      `}
      title={isLocked ? t('subscription.feature_requires_upgrade', { plan: requiredLevel }) : ''}
    >
      {isLocked && (
        <>
          {/* Locked overlay */}
          <div className="absolute inset-0 bg-gray-900 bg-opacity-20 flex items-center justify-center">
            {showLockIcon && <Lock className="w-4 h-4 text-gray-600" />}
          </div>
          
          {/* Subscription badge */}
          <div className="absolute -top-1 -right-1 flex items-center justify-center w-5 h-5 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full">
            {getSubscriptionIcon(requiredLevel || 'standard')}
          </div>
        </>
      )}
      
      <span className={isLocked ? 'opacity-50' : ''}>
        {children}
      </span>
      
      {isLocked && (
        <span className="ml-2 text-xs opacity-75">
          ({requiredLevel})
        </span>
      )}
    </motion.button>
  );
};

export default FeatureButton;
