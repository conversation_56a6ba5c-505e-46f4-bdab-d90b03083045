'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Crown, 
  Users, 
  Calendar, 
  FileText, 
  BarChart3, 
  Settings,
  AlertTriangle,
  CheckCircle,
  Clock,
  TrendingUp
} from 'lucide-react';
// import { getSchoolSubscription, getCreditUsageStats } from '@/app/services/SubscriptionServices';

interface SubscriptionInfo {
  plan: string;
  status: string;
  expires_at: string;
  features_used: number;
  features_limit: number;
  students_count: number;
  students_limit: number;
  storage_used: number;
  storage_limit: number;
}

interface FeatureUsage {
  feature_id: string;
  name: string;
  used: number;
  limit: number;
  percentage: number;
  status: 'ok' | 'warning' | 'exceeded';
}

export default function SubscriptionDashboard() {
  const [subscriptionInfo, setSubscriptionInfo] = useState<SubscriptionInfo | null>(null);
  const [featureUsages, setFeatureUsages] = useState<FeatureUsage[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    loadSubscriptionData();
  }, []);

  const loadSubscriptionData = async () => {
    try {
      setLoading(true);

      // Pour l'instant, utilisons des données mock
      // TODO: Implémenter avec les vrais services
      const mockSubInfo = {
        plan: 'free',
        status: 'active',
        expires_at: '2024-12-31',
        features_used: 3,
        features_limit: 5,
        students_count: 45,
        students_limit: 50,
        storage_used: 2048,
        storage_limit: 5120
      };
      setSubscriptionInfo(mockSubInfo);

      // Mock feature usages
      const mockUsages = [
        { feature_id: 'students', name: 'Étudiants', used: 45, limit: 50, percentage: 90, status: 'warning' as const },
        { feature_id: 'classes', name: 'Classes', used: 8, limit: 10, percentage: 80, status: 'ok' as const },
        { feature_id: 'reports', name: 'Rapports', used: 15, limit: 20, percentage: 75, status: 'ok' as const }
      ];
      setFeatureUsages(mockUsages);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement');
    } finally {
      setLoading(false);
    }
  };

  const getPlanColor = (plan: string) => {
    switch (plan.toLowerCase()) {
      case 'free': return 'bg-gray-100 text-gray-800';
      case 'basic': return 'bg-blue-100 text-blue-800';
      case 'premium': return 'bg-purple-100 text-purple-800';
      case 'enterprise': return 'bg-gold-100 text-gold-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ok': return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'warning': return <AlertTriangle className="w-4 h-4 text-yellow-500" />;
      case 'exceeded': return <AlertTriangle className="w-4 h-4 text-red-500" />;
      default: return <Clock className="w-4 h-4 text-gray-500" />;
    }
  };

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-gray-200 rounded w-1/3"></div>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {[1, 2, 3].map(i => (
              <div key={i} className="h-32 bg-gray-200 rounded"></div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="p-6">
        <Card className="border-red-200">
          <CardContent className="p-6">
            <div className="flex items-center space-x-2 text-red-600">
              <AlertTriangle className="w-5 h-5" />
              <span>{error}</span>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Abonnement</h1>
          <p className="text-gray-600">Gérez votre plan et surveillez l'utilisation</p>
        </div>
        <Button className="bg-purple-600 hover:bg-purple-700">
          <Crown className="w-4 h-4 mr-2" />
          Mettre à niveau
        </Button>
      </div>

      {/* Plan actuel */}
      {subscriptionInfo && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Plan actuel</span>
              <Badge className={getPlanColor(subscriptionInfo.plan)}>
                {subscriptionInfo.plan.toUpperCase()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
              <div className="text-center">
                <Users className="w-8 h-8 mx-auto mb-2 text-blue-500" />
                <div className="text-2xl font-bold">{subscriptionInfo.students_count}</div>
                <div className="text-sm text-gray-600">
                  sur {subscriptionInfo.students_limit} étudiants
                </div>
              </div>
              <div className="text-center">
                <FileText className="w-8 h-8 mx-auto mb-2 text-green-500" />
                <div className="text-2xl font-bold">{subscriptionInfo.features_used}</div>
                <div className="text-sm text-gray-600">
                  sur {subscriptionInfo.features_limit} fonctionnalités
                </div>
              </div>
              <div className="text-center">
                <BarChart3 className="w-8 h-8 mx-auto mb-2 text-purple-500" />
                <div className="text-2xl font-bold">{Math.round(subscriptionInfo.storage_used / 1024)} GB</div>
                <div className="text-sm text-gray-600">
                  sur {Math.round(subscriptionInfo.storage_limit / 1024)} GB stockage
                </div>
              </div>
              <div className="text-center">
                <Calendar className="w-8 h-8 mx-auto mb-2 text-orange-500" />
                <div className="text-2xl font-bold">
                  {new Date(subscriptionInfo.expires_at).toLocaleDateString()}
                </div>
                <div className="text-sm text-gray-600">Expiration</div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Utilisation des fonctionnalités */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="w-5 h-5 mr-2" />
            Utilisation des fonctionnalités
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {featureUsages.map((usage) => (
              <div key={usage.feature_id} className="flex items-center justify-between p-4 border rounded-lg">
                <div className="flex items-center space-x-3">
                  {getStatusIcon(usage.status)}
                  <div>
                    <div className="font-medium">{usage.name}</div>
                    <div className="text-sm text-gray-600">
                      {usage.used} / {usage.limit} utilisé
                    </div>
                  </div>
                </div>
                <div className="flex items-center space-x-3">
                  <Progress value={usage.percentage} className="w-24" />
                  <span className="text-sm font-medium">{usage.percentage}%</span>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Actions rapides */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Crown className="w-8 h-8 mx-auto mb-3 text-purple-500" />
            <h3 className="font-semibold mb-2">Mettre à niveau</h3>
            <p className="text-sm text-gray-600">Débloquez plus de fonctionnalités</p>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <BarChart3 className="w-8 h-8 mx-auto mb-3 text-blue-500" />
            <h3 className="font-semibold mb-2">Rapports d'usage</h3>
            <p className="text-sm text-gray-600">Analysez votre utilisation</p>
          </CardContent>
        </Card>
        
        <Card className="cursor-pointer hover:shadow-md transition-shadow">
          <CardContent className="p-6 text-center">
            <Settings className="w-8 h-8 mx-auto mb-3 text-gray-500" />
            <h3 className="font-semibold mb-2">Paramètres</h3>
            <p className="text-sm text-gray-600">Gérez votre abonnement</p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
