const PDFDocument = require('pdfkit');
const XLSX = require('xlsx');
const moment = require('moment');

// Cache simple pour les rapports (en production, utiliser Redis)
const reportCache = new Map();
const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes

// Fonction pour générer une clé de cache
const generateCacheKey = (type, options) => {
  return `${type}_${JSON.stringify(options)}_${moment().format('YYYY-MM-DD-HH')}`;
};

// Fonction pour vérifier le cache
const getCachedReport = (cacheKey) => {
  const cached = reportCache.get(cacheKey);
  if (cached && (Date.now() - cached.timestamp) < CACHE_DURATION) {
    return cached.data;
  }
  return null;
};

// Fonction pour mettre en cache un rapport
const setCachedReport = (cacheKey, data) => {
  reportCache.set(cacheKey, {
    data,
    timestamp: Date.now()
  });
  
  // Nettoyer le cache périodiquement
  if (reportCache.size > 100) {
    const oldestKey = reportCache.keys().next().value;
    reportCache.delete(oldestKey);
  }
};

// Générer un rapport PDF avec optimisations
const generatePDFReport = async (res, reportData, type) => {
  try {
    // Vérifier le cache
    const cacheKey = generateCacheKey(`pdf_${type}`, reportData);
    const cachedReport = getCachedReport(cacheKey);
    
    if (cachedReport) {
      console.log('📋 Utilisation du rapport en cache');
      res.setHeader('Content-Type', 'application/pdf');
      res.setHeader('Content-Disposition', `attachment; filename="${type}-report-${moment().format('YYYY-MM-DD')}.pdf"`);
      return res.send(cachedReport);
    }

    const doc = new PDFDocument({
      margin: 50,
      bufferPages: true, // Optimisation pour les gros documents
      font: 'Helvetica' // Utiliser une police standard pour éviter les problèmes d'encodage
    });
    
    // Configuration de la réponse
    res.setHeader('Content-Type', 'application/pdf');
    res.setHeader('Content-Disposition', `attachment; filename="${type}-report-${moment().format('YYYY-MM-DD')}.pdf"`);
    
    // Buffer pour le cache
    const buffers = [];
    doc.on('data', buffers.push.bind(buffers));
    doc.on('end', () => {
      const pdfBuffer = Buffer.concat(buffers);
      setCachedReport(cacheKey, pdfBuffer);
    });

    // Pipe le document vers la réponse
    doc.pipe(res);

    // En-tête du document avec style amélioré
    doc.fontSize(24).fillColor('#2563eb').text(reportData.title, { align: 'center' });
    doc.fontSize(12).fillColor('#6b7280').text(`Généré le: ${moment(reportData.generated_at).format('DD/MM/YYYY HH:mm')}`, { align: 'center' });
    doc.moveDown(2);

    if (type === 'global') {
      await generateGlobalPDFContent(doc, reportData);
    } else if (type === 'school') {
      await generateSchoolPDFContent(doc, reportData);
    }

    // Pied de page
    doc.fontSize(8).fillColor('#9ca3af').text(
      `Rapport généré par Scholarify - ${moment().format('DD/MM/YYYY HH:mm')}`,
      50,
      doc.page.height - 50,
      { align: 'center' }
    );

    // Finaliser le document
    doc.end();

  } catch (error) {
    console.error('Error generating PDF report:', error);
    res.status(500).json({ message: 'Erreur lors de la génération du rapport PDF' });
  }
};

// Contenu PDF pour rapport global avec optimisations
const generateGlobalPDFContent = async (doc, data) => {
  // Résumé des statistiques avec style
  doc.fontSize(18).fillColor('#1f2937').text('RESUME GLOBAL', { underline: true });
  doc.moveDown();
  
  // Créer un tableau stylé pour les statistiques
  const stats = [
    ['Total Écoles', data.summary.total_schools.toString()],
    ['Revenus Total', formatCurrency(data.summary.total_revenue)],
    ['Total Crédits', data.summary.total_credits.toLocaleString()],
    ['ARPU', formatCurrency(data.summary.arpu)]
  ];

  let yPosition = doc.y;
  stats.forEach(([label, value], index) => {
    doc.fontSize(12).fillColor('#374151').text(`${label}:`, 50, yPosition);
    doc.fontSize(12).fillColor('#059669').text(value, 200, yPosition);
    yPosition += 20;
  });

  doc.y = yPosition + 20;

  // Distribution des plans avec graphique simple
  doc.fontSize(18).fillColor('#1f2937').text('DISTRIBUTION DES PLANS', { underline: true });
  doc.moveDown();
  
  Object.entries(data.plan_distribution).forEach(([plan, count]) => {
    const percentage = ((count / data.summary.total_schools) * 100).toFixed(1);
    doc.fontSize(12).fillColor('#374151').text(`${plan.charAt(0).toUpperCase() + plan.slice(1)}:`, 50);
    doc.fillColor('#059669').text(`${count} écoles (${percentage}%)`, 200);
    doc.moveDown(0.5);
  });

  doc.moveDown(2);

  // Liste des écoles avec pagination
  doc.fontSize(18).fillColor('#1f2937').text('ECOLES (Top 20)', { underline: true });
  doc.moveDown();
  
  const schoolsToShow = data.schools.slice(0, 20);
  schoolsToShow.forEach((school, index) => {
    if (doc.y > doc.page.height - 100) {
      doc.addPage();
    }
    
    doc.fontSize(10).fillColor('#374151');
    doc.text(`${index + 1}. ${school.name}`, 50);
    doc.text(`Plan: ${school.plan} | Crédits: ${school.credits_balance} | Statut: ${school.status}`, 70);
    doc.moveDown(0.3);
  });
};

// Contenu PDF pour rapport école avec optimisations
const generateSchoolPDFContent = async (doc, data) => {
  // Informations de l'école avec style
  doc.fontSize(18).fillColor('#1f2937').text('INFORMATIONS DE L\'ECOLE', { underline: true });
  doc.moveDown();
  
  const schoolInfo = [
    ['Nom', data.school.name || 'Nom non spécifié'],
    ['Email', data.school.email || 'Email non spécifié'],
    ['Adresse', data.school.address || 'Adresse non spécifiée'],
    ['Plan', data.school.plan_type ? data.school.plan_type.charAt(0).toUpperCase() + data.school.plan_type.slice(1) : 'Plan non spécifié'],
    ['Statut', data.school.status || 'Statut non spécifié']
  ];

  let yPosition = doc.y;
  schoolInfo.forEach(([label, value]) => {
    doc.fontSize(12).fillColor('#374151').text(`${label}:`, 50, yPosition);
    doc.fontSize(12).fillColor('#059669').text(value, 150, yPosition);
    yPosition += 20;
  });

  doc.y = yPosition + 20;

  // Résumé des crédits avec indicateurs visuels
  doc.fontSize(18).fillColor('#1f2937').text('RESUME DES CREDITS', { underline: true });
  doc.moveDown();
  
  const creditStats = [
    ['Crédits Restants', (data.summary.credits_balance || 0).toString(), '#059669'],
    ['Crédits Achetés', (data.summary.credits_purchased || 0).toString(), '#2563eb'],
    ['Crédits Utilisés', (data.summary.credits_used || 0).toString(), '#dc2626'],
    ['Total Payé', formatCurrency(data.summary.total_paid || 0), '#7c3aed'],
    ['Efficacité', `${data.summary.efficiency || 0}%`, '#ea580c']
  ];

  yPosition = doc.y;
  creditStats.forEach(([label, value, color]) => {
    doc.fontSize(12).fillColor('#374151').text(`${label}:`, 50, yPosition);
    doc.fontSize(12).fillColor(color).text(value, 200, yPosition);
    yPosition += 20;
  });

  doc.y = yPosition + 20;

  // Historique des achats avec tableau
  if (data.purchases && data.purchases.length > 0) {
    doc.fontSize(18).fillColor('#1f2937').text('HISTORIQUE DES ACHATS (10 derniers)', { underline: true });
    doc.moveDown();

    data.purchases.slice(0, 10).forEach((purchase) => {
      if (doc.y > doc.page.height - 100) {
        doc.addPage();
      }

      // Vérification et formatage sécurisé des données
      const purchaseDate = purchase.date ? moment(purchase.date).format('DD/MM/YYYY') : 'Date inconnue';
      const purchaseAmount = purchase.amount ? formatCurrency(purchase.amount) : '0 XAF';
      const purchaseCredits = purchase.credits || purchase.credits_amount || 0;

      doc.fontSize(10).fillColor('#374151');
      doc.text(purchaseDate, 50);
      doc.text(purchaseAmount, 150);
      doc.text(`${purchaseCredits} credits`, 250);
      doc.moveDown(0.3);
    });

    doc.moveDown(2);
  }

  // Historique d'utilisation
  if (data.usage_history && data.usage_history.length > 0) {
    doc.fontSize(18).fillColor('#1f2937').text('HISTORIQUE D\'UTILISATION (10 derniers)', { underline: true });
    doc.moveDown();

    data.usage_history.slice(0, 10).forEach((usage) => {
      if (doc.y > doc.page.height - 100) {
        doc.addPage();
      }

      // Vérification et formatage sécurisé des données
      const usageDate = usage.date ? moment(usage.date).format('DD/MM/YYYY') : 'Date inconnue';
      const usageCredits = usage.credits || usage.credits_used || 0;
      const usageType = usage.type || usage.usage_type || 'Type inconnu';
      const usageDescription = usage.description || '';

      doc.fontSize(10).fillColor('#374151');
      doc.text(usageDate, 50);
      doc.text(`${usageCredits} credits`, 150);
      doc.text(usageType, 220);
      doc.text(usageDescription, 300);
      doc.moveDown(0.3);
    });
  }
};

// Générer un rapport Excel avec optimisations
const generateExcelReport = async (res, reportData, type) => {
  try {
    // Vérifier le cache
    const cacheKey = generateCacheKey(`excel_${type}`, reportData);
    const cachedReport = getCachedReport(cacheKey);
    
    if (cachedReport) {
      console.log('📋 Utilisation du rapport Excel en cache');
      res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
      res.setHeader('Content-Disposition', `attachment; filename="${type}-report-${moment().format('YYYY-MM-DD')}.xlsx"`);
      return res.send(cachedReport);
    }

    const workbook = XLSX.utils.book_new();

    if (type === 'global') {
      generateGlobalExcelContent(workbook, reportData);
    } else if (type === 'school') {
      generateSchoolExcelContent(workbook, reportData);
    }

    // Configuration de la réponse
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
    res.setHeader('Content-Disposition', `attachment; filename="${type}-report-${moment().format('YYYY-MM-DD')}.xlsx"`);

    // Écrire le fichier Excel
    const buffer = XLSX.write(workbook, { type: 'buffer', bookType: 'xlsx' });
    
    // Mettre en cache
    setCachedReport(cacheKey, buffer);
    
    res.send(buffer);

  } catch (error) {
    console.error('Error generating Excel report:', error);
    res.status(500).json({ message: 'Erreur lors de la génération du rapport Excel' });
  }
};

// Contenu Excel pour rapport global avec optimisations
const generateGlobalExcelContent = (workbook, data) => {
  // Feuille de résumé avec formatage
  const summaryData = [
    ['📊 RÉSUMÉ GLOBAL', ''],
    ['Métrique', 'Valeur'],
    ['Total Écoles', data.summary.total_schools],
    ['Revenus Total (XAF)', data.summary.total_revenue],
    ['Total Crédits', data.summary.total_credits],
    ['ARPU (XAF)', data.summary.arpu],
    ['', ''],
    ['Généré le', moment(data.generated_at).format('DD/MM/YYYY HH:mm')]
  ];
  const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
  
  // Appliquer du style (largeur des colonnes)
  summarySheet['!cols'] = [{ width: 20 }, { width: 15 }];
  
  XLSX.utils.book_append_sheet(workbook, summarySheet, 'Résumé');

  // Feuille de distribution des plans
  const planData = [['📈 DISTRIBUTION DES PLANS', ''], ['Plan', 'Nombre d\'Écoles', 'Pourcentage']];
  Object.entries(data.plan_distribution).forEach(([plan, count]) => {
    const percentage = ((count / data.summary.total_schools) * 100).toFixed(1);
    planData.push([plan.charAt(0).toUpperCase() + plan.slice(1), count, `${percentage}%`]);
  });
  const planSheet = XLSX.utils.aoa_to_sheet(planData);
  planSheet['!cols'] = [{ width: 15 }, { width: 15 }, { width: 12 }];
  XLSX.utils.book_append_sheet(workbook, planSheet, 'Distribution Plans');

  // Feuille des écoles avec plus de détails
  const schoolsData = [['🏫 LISTE DES ÉCOLES', '', '', ''], ['Nom', 'Plan', 'Crédits Restants', 'Statut']];
  data.schools.forEach(school => {
    schoolsData.push([school.name, school.plan, school.credits_balance, school.status]);
  });
  const schoolsSheet = XLSX.utils.aoa_to_sheet(schoolsData);
  schoolsSheet['!cols'] = [{ width: 30 }, { width: 12 }, { width: 15 }, { width: 12 }];
  XLSX.utils.book_append_sheet(workbook, schoolsSheet, 'Écoles');
};

// Contenu Excel pour rapport école avec optimisations
const generateSchoolExcelContent = (workbook, data) => {
  // Feuille d'informations de l'école
  const schoolInfoData = [
    ['🏫 INFORMATIONS ÉCOLE', ''],
    ['Information', 'Valeur'],
    ['Nom', data.school.name],
    ['Email', data.school.email],
    ['Adresse', data.school.address || 'Non spécifiée'],
    ['Plan', data.school.plan_type],
    ['Statut', data.school.status],
    ['', ''],
    ['Généré le', moment(data.generated_at).format('DD/MM/YYYY HH:mm')]
  ];
  const schoolInfoSheet = XLSX.utils.aoa_to_sheet(schoolInfoData);
  schoolInfoSheet['!cols'] = [{ width: 20 }, { width: 30 }];
  XLSX.utils.book_append_sheet(workbook, schoolInfoSheet, 'Informations École');

  // Feuille de résumé des crédits
  const creditsData = [
    ['💳 RÉSUMÉ CRÉDITS', ''],
    ['Métrique', 'Valeur'],
    ['Crédits Restants', data.summary.credits_balance],
    ['Crédits Achetés', data.summary.credits_purchased],
    ['Crédits Utilisés', data.summary.credits_used],
    ['Total Payé (XAF)', data.summary.total_paid],
    ['Efficacité (%)', data.summary.efficiency]
  ];
  const creditsSheet = XLSX.utils.aoa_to_sheet(creditsData);
  creditsSheet['!cols'] = [{ width: 20 }, { width: 15 }];
  XLSX.utils.book_append_sheet(workbook, creditsSheet, 'Résumé Crédits');

  // Feuille des achats avec plus de détails
  if (data.purchases && data.purchases.length > 0) {
    const purchasesData = [['💰 HISTORIQUE ACHATS', '', '', ''], ['Date', 'Montant (XAF)', 'Crédits', 'Statut']];
    data.purchases.forEach(purchase => {
      purchasesData.push([
        moment(purchase.date).format('DD/MM/YYYY'),
        purchase.amount,
        purchase.credits,
        purchase.status
      ]);
    });
    const purchasesSheet = XLSX.utils.aoa_to_sheet(purchasesData);
    purchasesSheet['!cols'] = [{ width: 12 }, { width: 15 }, { width: 10 }, { width: 12 }];
    XLSX.utils.book_append_sheet(workbook, purchasesSheet, 'Achats');
  }

  // Feuille d'utilisation avec plus de détails
  if (data.usage_history && data.usage_history.length > 0) {
    const usageData = [['📊 HISTORIQUE UTILISATION', '', '', ''], ['Date', 'Crédits', 'Type', 'Description']];
    data.usage_history.forEach(usage => {
      usageData.push([
        moment(usage.date).format('DD/MM/YYYY'),
        usage.credits,
        usage.type,
        usage.description || ''
      ]);
    });
    const usageSheet = XLSX.utils.aoa_to_sheet(usageData);
    usageSheet['!cols'] = [{ width: 12 }, { width: 10 }, { width: 20 }, { width: 30 }];
    XLSX.utils.book_append_sheet(workbook, usageSheet, 'Utilisation');
  }
};

// Fonction utilitaire pour formater la devise avec optimisation
const formatCurrency = (amount) => {
  if (typeof amount !== 'number') return '0 XAF';
  
  return new Intl.NumberFormat('fr-FR', {
    style: 'currency',
    currency: 'XAF',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount);
};

// Fonction pour nettoyer le cache périodiquement
const cleanupCache = () => {
  const now = Date.now();
  for (const [key, value] of reportCache.entries()) {
    if (now - value.timestamp > CACHE_DURATION) {
      reportCache.delete(key);
    }
  }
};

// Nettoyer le cache toutes les 10 minutes
setInterval(cleanupCache, 10 * 60 * 1000);

module.exports = {
  generatePDFReport,
  generateExcelReport,
  cleanupCache,
  reportCache
};
