import ApiInterceptorService, { SubscriptionRequiredError, FeatureAccessDeniedError } from "./ApiInterceptorService";

/**
 * Helper générique pour gérer les erreurs de subscription de manière cohérente
 * à travers tous les services
 */
export const handleSubscriptionError = (error: any, defaultMessage: string): never => {
  console.error('Service error:', error);
  
  if (error instanceof SubscriptionRequiredError) {
    throw new Error(`Mise à niveau requise: ${error.subscriptionError.subscription_required}`);
  }
  
  if (error instanceof FeatureAccessDeniedError) {
    throw new Error(`Accès refusé: Cette fonctionnalité nécessite un abonnement ${error.subscriptionError.subscription_required}`);
  }
  
  throw new Error(defaultMessage);
};

/**
 * Wrapper générique pour les appels GET avec gestion d'erreurs de subscription
 */
export const subscriptionAwareGet = async <T>(endpoint: string, errorMessage: string = "Erreur lors de la récupération des données"): Promise<T> => {
  try {
    return await ApiInterceptorService.get(endpoint);
  } catch (error) {
    return handleSubscriptionError(error, errorMessage);
  }
};

/**
 * Wrapper générique pour les appels POST avec gestion d'erreurs de subscription
 */
export const subscriptionAwarePost = async <T>(endpoint: string, data: any, errorMessage: string = "Erreur lors de la création"): Promise<T> => {
  try {
    return await ApiInterceptorService.post(endpoint, data);
  } catch (error) {
    return handleSubscriptionError(error, errorMessage);
  }
};

/**
 * Wrapper générique pour les appels PUT avec gestion d'erreurs de subscription
 */
export const subscriptionAwarePut = async <T>(endpoint: string, data: any, errorMessage: string = "Erreur lors de la mise à jour"): Promise<T> => {
  try {
    return await ApiInterceptorService.put(endpoint, data);
  } catch (error) {
    return handleSubscriptionError(error, errorMessage);
  }
};

/**
 * Wrapper générique pour les appels DELETE avec gestion d'erreurs de subscription
 */
export const subscriptionAwareDelete = async <T>(endpoint: string, errorMessage: string = "Erreur lors de la suppression"): Promise<T> => {
  try {
    return await ApiInterceptorService.delete(endpoint);
  } catch (error) {
    return handleSubscriptionError(error, errorMessage);
  }
};

/**
 * Wrapper pour les appels avec FormData (upload de fichiers)
 */
export const subscriptionAwareFormData = async <T>(endpoint: string, formData: FormData, method: 'POST' | 'PUT' = 'POST', errorMessage: string = "Erreur lors de l'upload"): Promise<T> => {
  try {
    if (method === 'POST') {
      return await ApiInterceptorService.postFormData(endpoint, formData);
    } else {
      return await ApiInterceptorService.putFormData(endpoint, formData);
    }
  } catch (error) {
    return handleSubscriptionError(error, errorMessage);
  }
};

/**
 * Helper pour construire des URLs avec paramètres de requête
 */
export const buildQueryUrl = (baseUrl: string, params: Record<string, any>): string => {
  const url = new URL(baseUrl, 'http://localhost'); // Base temporaire pour la construction
  
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined && value !== null && value !== '') {
      url.searchParams.append(key, String(value));
    }
  });
  
  return url.pathname + url.search;
};

/**
 * Helper pour les appels avec pagination
 */
export const subscriptionAwarePaginated = async <T>(
  endpoint: string, 
  params: { page?: number; limit?: number; [key: string]: any } = {},
  errorMessage: string = "Erreur lors de la récupération des données paginées"
): Promise<T> => {
  const queryUrl = buildQueryUrl(endpoint, params);
  return subscriptionAwareGet<T>(queryUrl, errorMessage);
};

/**
 * Helper pour les appels de recherche
 */
export const subscriptionAwareSearch = async <T>(
  endpoint: string,
  searchParams: Record<string, any>,
  errorMessage: string = "Erreur lors de la recherche"
): Promise<T> => {
  const queryUrl = buildQueryUrl(endpoint, searchParams);
  return subscriptionAwareGet<T>(queryUrl, errorMessage);
};

/**
 * Helper pour les appels avec retry automatique
 */
export const subscriptionAwareWithRetry = async <T>(
  operation: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: any;
  
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await operation();
    } catch (error) {
      lastError = error;
      
      // Ne pas retry sur les erreurs de subscription
      if (error instanceof SubscriptionRequiredError || error instanceof FeatureAccessDeniedError) {
        throw error;
      }
      
      if (attempt === maxRetries) {
        break;
      }
      
      // Attendre avant le prochain essai
      await new Promise(resolve => setTimeout(resolve, delay * attempt));
    }
  }
  
  throw lastError;
};

/**
 * Helper pour les opérations en lot
 */
export const subscriptionAwareBatch = async <T>(
  operations: Array<() => Promise<T>>,
  batchSize: number = 5
): Promise<T[]> => {
  const results: T[] = [];
  
  for (let i = 0; i < operations.length; i += batchSize) {
    const batch = operations.slice(i, i + batchSize);
    const batchResults = await Promise.all(batch.map(op => op()));
    results.push(...batchResults);
  }
  
  return results;
};

/**
 * Types utilitaires pour les réponses API standardisées
 */
export interface ApiResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface SearchResponse<T> {
  data: T[];
  total: number;
  query: string;
  filters?: Record<string, any>;
}

/**
 * Helper pour extraire les données des réponses API
 */
export const extractApiData = <T>(response: ApiResponse<T> | T): T => {
  if (response && typeof response === 'object' && 'data' in response) {
    return (response as ApiResponse<T>).data;
  }
  return response as T;
};

/**
 * Helper pour valider les réponses API
 */
export const validateApiResponse = <T>(response: any): ApiResponse<T> => {
  if (!response) {
    throw new Error('Réponse API vide');
  }
  
  if (typeof response === 'object' && 'success' in response && !response.success) {
    throw new Error(response.message || 'Erreur API');
  }
  
  return response;
};
