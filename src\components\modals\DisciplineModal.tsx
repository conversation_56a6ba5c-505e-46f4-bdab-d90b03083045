"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Save, Loader2 } from 'lucide-react';
import { DisciplineSchema, DisciplineCreateSchema, DisciplineUpdateSchema } from '@/app/models/Discipline';
import useAuth from '@/app/hooks/useAuth';
import { useTranslation } from '@/hooks/useTranslation';
import SearchableStudentSelector from '@/components/ui/SearchableStudentSelector';

interface DisciplineModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSave: (data: DisciplineCreateSchema | DisciplineUpdateSchema) => Promise<void>;
  discipline?: DisciplineSchema | null;
  isSubmitting?: boolean;
  students?: Array<{ _id: string; name: string; student_id: string }>;
}

const DisciplineModal: React.FC<DisciplineModalProps> = ({
  isOpen,
  onClose,
  onSave,
  discipline,
  isSubmitting = false,
  students = []
}) => {
  const { user } = useAuth();
  const { t, tDashboard } = useTranslation();
  const [formData, setFormData] = useState({
    student_id: '',
    comments: '',
    school_id: ''
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  // Initialize form data when modal opens or discipline changes
  useEffect(() => {
    if (isOpen) {
      if (discipline) {
        // Edit mode
        setFormData({
          student_id: discipline.student_id || '',
          comments: discipline.comments || '',
          school_id: discipline.school_id || ''
        });
      } else {
        // Create mode
        setFormData({
          student_id: '',
          comments: '',
          school_id: user?.school_ids?.[0] || ''
        });
      }
      setErrors({});
    }
  }, [isOpen, discipline, user]);

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {};

    if (!formData.student_id.trim()) {
      newErrors.student_id = tDashboard('school-admin', 'discipline', 'student_required');
    }

    if (!formData.school_id.trim()) {
      newErrors.school_id = tDashboard('school-admin', 'discipline', 'school_id_required');
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    try {
      if (discipline) {
        // Update mode
        const updateData: DisciplineUpdateSchema = {
          _id: discipline._id,
          student_id: formData.student_id,
          comments: formData.comments,
          school_id: formData.school_id
        };
        await onSave(updateData);
      } else {
        // Create mode
        const createData: DisciplineCreateSchema = {
          discipline_id: `disc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          student_id: formData.student_id,
          comments: formData.comments,
          school_id: formData.school_id
        };
        await onSave(createData);
      }
    } catch (error) {
      console.error('Error saving discipline:', error);
    }
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  if (!isOpen) return null;

  return (
    <AnimatePresence>
      <div className="fixed inset-0 z-50 flex items-center justify-center">
        {/* Backdrop */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          className="absolute inset-0 bg-black bg-opacity-50"
          onClick={onClose}
        />

        {/* Modal */}
        <motion.div
          initial={{ opacity: 0, scale: 0.95, y: 20 }}
          animate={{ opacity: 1, scale: 1, y: 0 }}
          exit={{ opacity: 0, scale: 0.95, y: 20 }}
          className="relative bg-widget rounded-lg shadow-xl w-full max-w-md mx-4 max-h-[90vh] overflow-y-auto"
        >
          {/* Header */}
          <div className="flex items-center justify-between p-6 border-b border-stroke">
            <h2 className="text-xl font-semibold text-text">
              {discipline ? tDashboard('school-admin', 'discipline', 'edit_discipline_record') : tDashboard('school-admin', 'discipline', 'add_discipline_record')}
            </h2>
            <button
              onClick={onClose}
              className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-lg transition-colors"
              disabled={isSubmitting}
            >
              <X className="w-5 h-5 text-text" />
            </button>
          </div>

          {/* Form */}
          <form onSubmit={handleSubmit} className="p-6 space-y-4">
            {/* Student Selection */}
            <div>
              <label htmlFor="student_id" className="block text-sm font-medium text-text mb-2">
                {tDashboard('school-admin', 'discipline', 'student')} <span className="text-red-500">*</span>
              </label>
              <SearchableStudentSelector
                students={students}
                selectedStudentId={formData.student_id}
                onStudentSelect={(studentId) => handleInputChange('student_id', studentId)}
                placeholder={tDashboard('school-admin', 'discipline', 'select_student')}
                disabled={isSubmitting}
                error={errors.student_id}
                className="w-full"
              />
            </div>

            {/* Comments Field */}
            <div>
              <label htmlFor="comments" className="block text-sm font-medium text-text mb-2">
                {tDashboard('school-admin', 'discipline', 'comments')}
              </label>
              <textarea
                id="comments"
                value={formData.comments}
                onChange={(e) => handleInputChange('comments', e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-stroke rounded-lg focus:outline-none focus:ring-2 focus:ring-teal-500 bg-widget text-text resize-none"
                placeholder={tDashboard('school-admin', 'discipline', 'comments_placeholder')}
                disabled={isSubmitting}
              />
            </div>

            {/* School ID Field (hidden, auto-filled) */}
            <input
              type="hidden"
              value={formData.school_id}
            />

            {/* Action Buttons */}
            <div className="flex justify-end space-x-3 pt-4">
              <button
                type="button"
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-text bg-gray-100 dark:bg-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 rounded-lg transition-colors"
                disabled={isSubmitting}
              >
                {t('common.cancel')}
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="px-4 py-2 text-sm font-medium text-white bg-teal hover:bg-teal-600 rounded-lg transition-colors flex items-center space-x-2 disabled:opacity-50 disabled:cursor-not-allowed"
              >
                {isSubmitting ? (
                  <Loader2 className="w-4 h-4 animate-spin" />
                ) : (
                  <Save className="w-4 h-4" />
                )}
                <span>{isSubmitting ? t('common.saving') : t('common.save')}</span>
              </button>
            </div>
          </form>
        </motion.div>
      </div>
    </AnimatePresence>
  );
};

export default DisciplineModal;
