import { useState, useEffect } from 'react';

// Importation des fichiers de traduction
import frCommon from '../locales/fr/common.json';
import enCommon from '../locales/en/common.json';

// Type pour les langues supportées
type Locale = 'fr' | 'en';

// Interface pour les informations de langue
interface LanguageInfo {
  code: Locale;
  name: string;
  flag: string;
}

// Langues disponibles avec leurs informations
export const AVAILABLE_LANGUAGES: LanguageInfo[] = [
  { code: 'fr', name: 'Français', flag: '🇫🇷' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

/**
 * Hook pour gérer les traductions et le changement de langue
 */
export function useTranslation() {
  // Récupérer la langue actuelle depuis localStorage ou utiliser le français par défaut
  const [currentLocale, setCurrentLocale] = useState<Locale>(() => {
    if (typeof window !== 'undefined') {
      const storedLocale = localStorage.getItem('scholarify-locale');
      return AVAILABLE_LANGUAGES.find(lang => lang.code === storedLocale)?.code || 'fr';
    }
    return 'fr';
  });

  // État pour forcer le re-render
  const [, forceUpdate] = useState({});

  // Mettre à jour localStorage lorsque la langue change
  useEffect(() => {
    if (typeof window !== 'undefined') {
      localStorage.setItem('scholarify-locale', currentLocale);
    }
  }, [currentLocale]);

  // Écouter les changements de langue depuis d'autres composants
  useEffect(() => {
    const handleLanguageChange = (event: CustomEvent) => {
      const newLocale = event.detail.locale;
      if (newLocale !== currentLocale) {
        setCurrentLocale(newLocale);
        forceUpdate({}); // Forcer le re-render
      }
    };

    if (typeof window !== 'undefined') {
      window.addEventListener('languageChanged', handleLanguageChange as EventListener);
      return () => {
        window.removeEventListener('languageChanged', handleLanguageChange as EventListener);
      };
    }
  }, [currentLocale]);

  /**
   * Change la langue de l'application
   *
   * @param locale - Code de la langue
   */
  const setLocale = (locale: Locale) => {
    setCurrentLocale(locale);
    forceUpdate({}); // Forcer le re-render immédiat
    
    // Émettre un événement personnalisé pour notifier les autres composants
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('languageChanged', {
        detail: { locale }
      });
      window.dispatchEvent(event);
    }
  };

  /**
   * Récupère une traduction à partir d'une clé imbriquée
   * Exemple: t('dashboard.title') va chercher translations.dashboard.title
   *
   * Si la traduction n'existe pas dans la langue actuelle, essaie avec l'autre langue.
   * Si aucune traduction n'est trouvée, retourne la valeur par défaut ou la clé d'origine.
   *
   * @param key - Clé de traduction (format: 'section.sous_section.clé')
   * @param defaultValueOrParams - Valeur par défaut (string) ou paramètres (object)
   * @param params - Paramètres à insérer dans la traduction (si defaultValueOrParams est une string)
   * @returns La traduction correspondante, la valeur par défaut, ou la clé si aucune traduction n'est trouvée
   */
  const translate = (
    key: string,
    defaultValueOrParams?: string | Record<string, string | number>,
    params?: Record<string, string | number>
  ): string => {
    // Si la clé est vide, retourner une chaîne vide
    if (!key) {
      console.warn('Translation key is empty');
      return '';
    }

    // Déterminer les paramètres et la valeur par défaut
    let defaultValue: string | undefined;
    let translationParams: Record<string, string | number> = {};

    if (typeof defaultValueOrParams === 'string') {
      // Premier paramètre est une valeur par défaut
      defaultValue = defaultValueOrParams;
      translationParams = params || {};
    } else if (typeof defaultValueOrParams === 'object' && defaultValueOrParams !== null) {
      // Premier paramètre est un objet de paramètres
      translationParams = defaultValueOrParams;
    }

    // Fusionner les traductions des fichiers JSON importés
    const translations: Record<Locale, Record<string, unknown>> = {
      fr: frCommon,
      en: enCommon,
    };

    // Diviser la clé en parties (ex: 'dashboard.title' -> ['dashboard', 'title'])
    const keyParts = key.split('.');

    // Récupérer les traductions pour la langue actuelle
    const localeTranslations = translations[currentLocale];

    // Fonction pour récupérer une traduction à partir d'un objet et d'une liste de parties de clé
    const getTranslation = (obj: Record<string, unknown>, parts: string[]): string | null => {
      let current = obj;

      for (const part of parts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part] as Record<string, unknown>;
        } else {
          return null;
        }
      }

      return typeof current === 'string' ? current : null;
    };

    // Essayer de trouver la traduction dans la langue actuelle
    let translation = getTranslation(localeTranslations, keyParts);

    // Si la traduction n'existe pas dans la langue actuelle, essayer avec l'autre langue comme fallback
    if (translation === null) {
      const fallbackLocale = currentLocale === 'fr' ? 'en' : 'fr';
      translation = getTranslation(translations[fallbackLocale], keyParts);
    }

    // Si aucune traduction n'est trouvée, utiliser la valeur par défaut ou la clé comme fallback
    if (translation === null) {
      if (defaultValue !== undefined) {
        // Utiliser la valeur par défaut et remplacer les paramètres
        let result = defaultValue;
        for (const [paramKey, paramValue] of Object.entries(translationParams)) {
          const regex = new RegExp(`{${paramKey}}`, 'g');
          result = result.replace(regex, String(paramValue));
        }
        return result;
      } else {
        console.warn(`Translation missing for key: ${key}`);
        return key;
      }
    }

    // Remplacer les paramètres dans la traduction
    let result = translation;

    // Parcourir tous les paramètres et les remplacer dans la chaîne
    for (const [paramKey, paramValue] of Object.entries(translationParams)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  };

  /**
   * Vérifie si une clé de traduction existe
   *
   * @param key - Clé de traduction à vérifier
   * @returns true si la clé existe, false sinon
   */
  const hasTranslation = (key: string): boolean => {
    if (!key) return false;

    const translations: Record<Locale, Record<string, unknown>> = {
      fr: frCommon,
      en: enCommon,
    };

    const keyParts = key.split('.');
    const localeTranslations = translations[currentLocale];

    // Fonction pour vérifier si une traduction existe
    const checkTranslation = (obj: Record<string, unknown>, parts: string[]): boolean => {
      let current = obj;

      for (const part of parts) {
        if (current && typeof current === 'object' && part in current) {
          current = current[part] as Record<string, unknown>;
        } else {
          return false;
        }
      }

      return typeof current === 'string';
    };

    // Vérifier dans la langue actuelle
    if (checkTranslation(localeTranslations, keyParts)) {
      return true;
    }

    // Vérifier dans l'autre langue comme fallback
    const fallbackLocale = currentLocale === 'fr' ? 'en' : 'fr';
    return checkTranslation(translations[fallbackLocale], keyParts);
  };

  /**
   * Traduit une clé en fonction de la langue actuelle avec une valeur par défaut
   *
   * @param key - Clé de traduction
   * @param defaultValue - Valeur par défaut à utiliser si la traduction n'existe pas
   * @param params - Paramètres à insérer dans la traduction
   * @returns La traduction correspondante ou la valeur par défaut
   */
  const translateWithDefault = (
    key: string,
    defaultValue: string,
    params: Record<string, string | number> = {}
  ): string => {
    if (hasTranslation(key)) {
      return translate(key, params);
    }

    // Remplacer les paramètres dans la valeur par défaut
    let result = defaultValue;
    for (const [paramKey, paramValue] of Object.entries(params)) {
      const regex = new RegExp(`{${paramKey}}`, 'g');
      result = result.replace(regex, String(paramValue));
    }

    return result;
  };

  // Obtenir les informations de la langue actuelle
  const getCurrentLanguageInfo = (): LanguageInfo => {
    return AVAILABLE_LANGUAGES.find(lang => lang.code === currentLocale) || AVAILABLE_LANGUAGES[0];
  };

  /**
   * Fonction utilitaire pour traduire les pages de dashboard
   * @param dashboardType - Type de dashboard ('super-admin', 'school-admin', 'teacher-dashboard')
   * @param page - Nom de la page
   * @param key - Clé de traduction dans la page
   * @param params - Paramètres optionnels
   */
  const tDashboard = (
    dashboardType: 'super-admin' | 'school-admin' | 'teacher-dashboard',
    page: string,
    key: string,
    params: Record<string, string | number> = {}
  ): string => {
    return translate(`dashboard.${dashboardType}.pages.${page}.${key}`, params);
  };

  return {
    currentLocale,
    setLocale,
    t: translate, // Alias pour la compatibilité avec le code existant
    translate,    // Nouveau nom plus explicite
    traducteur: translate, // Alias en français
    tDashboard, // Fonction utilitaire pour les dashboards
    hasTranslation, // Fonction pour vérifier si une clé existe
    translateWithDefault, // Fonction pour traduire avec une valeur par défaut
    tDefault: translateWithDefault, // Alias plus court
    getCurrentLanguageInfo, // Obtenir les infos de la langue actuelle
    availableLanguages: AVAILABLE_LANGUAGES, // Liste des langues disponibles
  };
}
