"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, AlertTriangle, TrendingUp, Clock, ArrowRight } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useSubscription } from '@/hooks/useSubscription';

interface LimitExceededModalProps {
  isOpen: boolean;
  onClose: () => void;
  featureId: string;
  limitDetails: {
    daily_usage?: number;
    daily_limit?: number;
    monthly_usage?: number;
    monthly_limit?: number;
    reset_time?: string;
  };
  upgradeUrl?: string;
}

interface LimitExceededModalState {
  isOpen: boolean;
  props: Omit<LimitExceededModalProps, 'isOpen' | 'onClose'>;
}

// État global pour la modale
let modalState: LimitExceededModalState = {
  isOpen: false,
  props: {
    featureId: '',
    limitDetails: {}
  }
};

let setModalState: React.Dispatch<React.SetStateAction<LimitExceededModalState>> | null = null;

/**
 * Fonction pour afficher la modale de limite dépassée
 */
export const showLimitExceededModal = (props: Omit<LimitExceededModalProps, 'isOpen' | 'onClose'>) => {
  if (setModalState) {
    setModalState({
      isOpen: true,
      props
    });
  }
};

/**
 * Fonction pour fermer la modale de limite dépassée
 */
export const hideLimitExceededModal = () => {
  if (setModalState) {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }));
  }
};

/**
 * Composant de modale pour les limites d'utilisation dépassées
 */
const LimitExceededModal: React.FC<LimitExceededModalProps> = ({
  isOpen,
  onClose,
  featureId,
  limitDetails,
  upgradeUrl
}) => {
  const { t } = useTranslation();
  const { upgradeSubscription } = useSubscription();
  const [isUpgrading, setIsUpgrading] = useState(false);

  const handleUpgrade = async () => {
    if (upgradeUrl) {
      window.open(upgradeUrl, '_blank');
      return;
    }

    setIsUpgrading(true);
    try {
      await upgradeSubscription('premium');
      onClose();
    } catch (error) {
      console.error('Erreur lors de la mise à niveau:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  const getResetTimeText = () => {
    if (!limitDetails.reset_time) return null;
    
    const resetTime = new Date(limitDetails.reset_time);
    const now = new Date();
    const diffMs = resetTime.getTime() - now.getTime();
    const diffHours = Math.ceil(diffMs / (1000 * 60 * 60));
    
    if (diffHours <= 0) return 'Bientôt';
    if (diffHours < 24) return `Dans ${diffHours}h`;
    
    const diffDays = Math.ceil(diffHours / 24);
    return `Dans ${diffDays} jour${diffDays > 1 ? 's' : ''}`;
  };

  const getUsagePercentage = (usage: number, limit: number) => {
    return Math.min((usage / limit) * 100, 100);
  };

  const featureName = t(`subscription.features.${featureId}`, featureId);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          >
            {/* Header */}
            <div className="p-6 bg-gradient-to-r from-orange-500 to-red-500 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white bg-opacity-20 rounded-full">
                    <AlertTriangle className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">
                      {t('subscription.feature_limit_exceeded')}
                    </h3>
                    <p className="text-white/80 text-sm">
                      Limite d'utilisation atteinte
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Feature info */}
              <div className="mb-4">
                <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-2">
                  {featureName}
                </h4>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Vous avez atteint la limite d'utilisation pour cette fonctionnalité.
                </p>
              </div>

              {/* Usage Statistics */}
              <div className="mb-6 space-y-4">
                {/* Daily Usage */}
                {limitDetails.daily_limit && (
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Utilisation quotidienne
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {limitDetails.daily_usage || 0} / {limitDetails.daily_limit}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-orange-500 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${getUsagePercentage(
                            limitDetails.daily_usage || 0,
                            limitDetails.daily_limit
                          )}%`
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* Monthly Usage */}
                {limitDetails.monthly_limit && (
                  <div className="p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                        Utilisation mensuelle
                      </span>
                      <span className="text-sm text-gray-500 dark:text-gray-400">
                        {limitDetails.monthly_usage || 0} / {limitDetails.monthly_limit}
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 dark:bg-gray-600 rounded-full h-2">
                      <div
                        className="bg-red-500 h-2 rounded-full transition-all duration-300"
                        style={{
                          width: `${getUsagePercentage(
                            limitDetails.monthly_usage || 0,
                            limitDetails.monthly_limit
                          )}%`
                        }}
                      />
                    </div>
                  </div>
                )}

                {/* Reset Time */}
                {limitDetails.reset_time && (
                  <div className="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-400">
                    <Clock className="w-4 h-4" />
                    <span>Réinitialisation : {getResetTimeText()}</span>
                  </div>
                )}
              </div>

              {/* Solutions */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Solutions disponibles :
                </h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Attendez la réinitialisation automatique</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-green-500 rounded-full"></div>
                    <span>Mettez à niveau votre abonnement pour des limites plus élevées</span>
                  </li>
                </ul>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {t('common.close')}
                </button>
                <button
                  onClick={handleUpgrade}
                  disabled={isUpgrading}
                  className="flex-1 px-4 py-2 bg-gradient-to-r from-blue-500 to-purple-500 text-white rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center space-x-2 disabled:opacity-50"
                >
                  {isUpgrading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <TrendingUp className="w-4 h-4" />
                      <span>Mettre à niveau</span>
                      <ArrowRight className="w-4 h-4" />
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

/**
 * Provider pour la modale de limite dépassée
 */
export const LimitExceededModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<LimitExceededModalState>(modalState);

  // Enregistrer la fonction setState globalement
  React.useEffect(() => {
    setModalState = setState;
    return () => {
      setModalState = null;
    };
  }, []);

  return (
    <>
      {children}
      <LimitExceededModal
        isOpen={state.isOpen}
        onClose={hideLimitExceededModal}
        {...state.props}
      />
    </>
  );
};

export default LimitExceededModal;
