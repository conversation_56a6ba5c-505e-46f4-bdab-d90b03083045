'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { getSchoolSubscription, updateSchoolSubscription } from '@/app/services/SubscriptionServices';
import { PlanType } from '@/app/models/SchoolSubscriptionModel';
// import { FeatureRegistryServices } from '@/app/services/FeatureRegistryServices';

interface SubscriptionInfo {
  plan: string;
  status: string;
  expires_at: string;
  features: string[];
  limits: Record<string, number>;
  usage: Record<string, number>;
}

interface FeatureAccess {
  hasAccess: boolean;
  reason?: string;
  requiredPlan?: string;
}

interface SubscriptionContextType {
  // État de l'abonnement
  subscription: SubscriptionInfo | null;
  isLoading: boolean;
  error: string | null;
  schoolId: string | null;

  // Méthodes de vérification d'accès
  hasFeatureAccess: (featureId: string) => FeatureAccess;
  canUseFeature: (featureId: string) => boolean;
  getFeatureLimit: (featureId: string) => number;
  getFeatureUsage: (featureId: string) => number;
  isFeatureLimitReached: (featureId: string) => boolean;

  // Actions
  refreshSubscription: (schoolId?: string) => Promise<void>;
  upgradeSubscription: (newPlan: string, schoolId?: string) => Promise<void>;
  setSchoolId: (schoolId: string) => void;

  // Helpers
  getPlanLevel: (plan: string) => number;
  comparePlans: (plan1: string, plan2: string) => number;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

interface SubscriptionProviderProps {
  children: ReactNode;
}

export function SubscriptionProvider({ children }: SubscriptionProviderProps) {
  const [subscription, setSubscription] = useState<SubscriptionInfo | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [schoolId, setSchoolIdState] = useState<string | null>(null);

  // Hiérarchie des plans (basée sur les types réels du modèle)
  const planHierarchy = {
    'basic': 1,
    'standard': 2,
    'custom': 3
  };

  // Méthode pour définir le schoolId
  const setSchoolId = (newSchoolId: string) => {
    setSchoolIdState(newSchoolId);
  };

  // Charger les informations d'abonnement
  const loadSubscription = async (targetSchoolId?: string) => {
    const currentSchoolId = targetSchoolId || schoolId;

    if (!currentSchoolId) {
      setError('School ID non défini');
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);

      const subInfo = await getSchoolSubscription(currentSchoolId);

      if (subInfo) {
        // Adapter les données du service aux interfaces du contexte
        const adaptedInfo: SubscriptionInfo = {
          plan: (subInfo.plan_type as string) || 'basic',
          status: (subInfo.subscription_status as string) || 'active',
          expires_at: (subInfo.expires_at as string) || '2024-12-31',
          features: ['basic_features'], // TODO: Mapper depuis les données réelles
          limits: {
            students: 50,
            classes: 10,
            reports: 20
          }, // TODO: Récupérer depuis les données réelles
          usage: {
            students: 45,
            classes: 8,
            reports: 15
          } // TODO: Récupérer depuis les données réelles
        };
        setSubscription(adaptedInfo);
      } else {
        // Données par défaut si pas d'abonnement
        setSubscription({
          plan: 'basic',
          status: 'active',
          expires_at: '2024-12-31',
          features: ['basic_features'],
          limits: { students: 50, classes: 10, reports: 20 },
          usage: { students: 0, classes: 0, reports: 0 }
        });
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement de l\'abonnement');
    } finally {
      setIsLoading(false);
    }
  };

  // Rafraîchir l'abonnement
  const refreshSubscription = async (targetSchoolId?: string) => {
    await loadSubscription(targetSchoolId);
  };

  // Mettre à niveau l'abonnement
  const upgradeSubscription = async (newPlan: string, targetSchoolId?: string) => {
    const currentSchoolId = targetSchoolId || schoolId;

    if (!currentSchoolId) {
      throw new Error('School ID non défini');
    }

    try {
      await updateSchoolSubscription(currentSchoolId, { plan_type: newPlan as PlanType });
      await refreshSubscription(currentSchoolId);
    } catch (err) {
      throw new Error(err instanceof Error ? err.message : 'Erreur lors de la mise à niveau');
    }
  };

  // Obtenir le niveau d'un plan
  const getPlanLevel = (plan: string): number => {
    return planHierarchy[plan.toLowerCase() as keyof typeof planHierarchy] ?? 0;
  };

  // Comparer deux plans
  const comparePlans = (plan1: string, plan2: string): number => {
    return getPlanLevel(plan1) - getPlanLevel(plan2);
  };

  // Vérifier l'accès à une fonctionnalité
  const hasFeatureAccess = (featureId: string): FeatureAccess => {
    if (!subscription) {
      return {
        hasAccess: false,
        reason: 'Abonnement non chargé',
        requiredPlan: 'standard'
      };
    }

    // Vérifier si la fonctionnalité est incluse dans le plan actuel
    if (subscription.features.includes(featureId)) {
      return { hasAccess: true };
    }

    // Déterminer le plan requis pour cette fonctionnalité
    // Cette logique devrait être basée sur votre configuration de fonctionnalités
    const requiredPlan = getRequiredPlanForFeature(featureId);
    
    if (comparePlans(subscription.plan, requiredPlan) >= 0) {
      return { hasAccess: true };
    }

    return {
      hasAccess: false,
      reason: `Plan ${requiredPlan} requis`,
      requiredPlan
    };
  };

  // Version simplifiée pour vérifier l'accès
  const canUseFeature = (featureId: string): boolean => {
    return hasFeatureAccess(featureId).hasAccess;
  };

  // Obtenir la limite d'une fonctionnalité
  const getFeatureLimit = (featureId: string): number => {
    return subscription?.limits[featureId] ?? 0;
  };

  // Obtenir l'utilisation actuelle d'une fonctionnalité
  const getFeatureUsage = (featureId: string): number => {
    return subscription?.usage[featureId] ?? 0;
  };

  // Vérifier si la limite d'une fonctionnalité est atteinte
  const isFeatureLimitReached = (featureId: string): boolean => {
    const usage = getFeatureUsage(featureId);
    const limit = getFeatureLimit(featureId);
    return limit > 0 && usage >= limit;
  };

  // Déterminer le plan requis pour une fonctionnalité
  const getRequiredPlanForFeature = (featureId: string): string => {
    // Cette logique devrait être basée sur votre configuration de fonctionnalités
    // Pour l'instant, utilisons une logique simple avec les vrais types de plans
    const featurePlanMapping: Record<string, string> = {
      'basic_features': 'basic',
      'advanced_reports': 'standard',
      'bulk_operations': 'custom',
      'api_access': 'custom',
      'advanced_analytics': 'standard',
      'custom_branding': 'custom',
      'priority_support': 'standard',
      'unlimited_storage': 'custom'
    };

    return featurePlanMapping[featureId] ?? 'standard';
  };

  // Charger l'abonnement quand le schoolId change
  useEffect(() => {
    if (schoolId) {
      loadSubscription();
    }
  }, [schoolId]);

  const contextValue: SubscriptionContextType = {
    subscription,
    isLoading,
    error,
    schoolId,
    hasFeatureAccess,
    canUseFeature,
    getFeatureLimit,
    getFeatureUsage,
    isFeatureLimitReached,
    refreshSubscription,
    upgradeSubscription,
    setSchoolId,
    getPlanLevel,
    comparePlans
  };

  return (
    <SubscriptionContext.Provider value={contextValue}>
      {children}
    </SubscriptionContext.Provider>
  );
}

// Hook pour utiliser le contexte de subscription
export function useSubscription() {
  const context = useContext(SubscriptionContext);
  if (context === undefined) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
}

// Hook pour vérifier l'accès à une fonctionnalité spécifique
export function useFeatureAccess(featureId: string, requiredPlan: string = 'standard') {
  const { hasFeatureAccess, canUseFeature, subscription, isLoading } = useSubscription();
  
  const access = hasFeatureAccess(featureId);
  
  return {
    hasAccess: access.hasAccess,
    canUse: canUseFeature(featureId),
    reason: access.reason,
    requiredPlan: access.requiredPlan || requiredPlan,
    currentPlan: subscription?.plan || 'basic',
    isLoading
  };
}

// Hook pour vérifier les limites d'utilisation
export function useFeatureLimit(featureId: string) {
  const { getFeatureLimit, getFeatureUsage, isFeatureLimitReached } = useSubscription();
  
  const limit = getFeatureLimit(featureId);
  const usage = getFeatureUsage(featureId);
  const isLimitReached = isFeatureLimitReached(featureId);
  const percentage = limit > 0 ? Math.min((usage / limit) * 100, 100) : 0;
  
  return {
    limit,
    usage,
    remaining: Math.max(limit - usage, 0),
    percentage,
    isLimitReached,
    isNearLimit: percentage >= 80
  };
}

export default SubscriptionContext;
