"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Code,
  Database,
  Key,
  FileText,
  CheckCircle,
  ArrowRight,
  Globe,
  Shield,
  Settings,
  Eye,
  Download,
  BookOpen,
  Terminal,
  Zap,
  Plus
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function ApiReferencePage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'authentication', name: 'Authentication', icon: Key },
    { id: 'endpoints', name: 'Endpoints', icon: Globe },
    { id: 'examples', name: 'Examples', icon: Code },
    { id: 'sdk', name: 'SDK', icon: Terminal },
    { id: 'webhooks', name: 'Webhooks', icon: Zap },
  ];

  const features = [
    {
      title: 'RESTful API',
      description: 'Modern REST API with comprehensive endpoints for all platform features.',
      icon: Globe,
      benefits: [
        'RESTful design principles',
        'JSON request/response format',
        'HTTP status codes',
        'Pagination support',
        'Filtering and sorting',
        'Rate limiting'
      ]
    },
    {
      title: 'Authentication',
      description: 'Secure API authentication using API keys and OAuth 2.0.',
      icon: Key,
      benefits: [
        'API key authentication',
        'OAuth 2.0 support',
        'JWT token management',
        'Role-based access control',
        'Token refresh mechanism',
        'Secure credential storage'
      ]
    },
    {
      title: 'Comprehensive Endpoints',
      description: 'Complete API coverage for all platform functionality.',
      icon: Database,
      benefits: [
        'Student management endpoints',
        'Academic data endpoints',
        'Financial endpoints',
        'Communication endpoints',
        'Reporting endpoints',
        'User management endpoints'
      ]
    },
    {
      title: 'SDK & Libraries',
      description: 'Official SDKs and libraries for popular programming languages.',
      icon: Code,
      benefits: [
        'JavaScript/TypeScript SDK',
        'Python SDK',
        'PHP SDK',
        'Java SDK',
        'C# SDK',
        'Mobile SDKs'
      ]
    },
    {
      title: 'Webhooks',
      description: 'Real-time event notifications via webhooks.',
      icon: Zap,
      benefits: [
        'Event-driven notifications',
        'Custom webhook endpoints',
        'Event filtering',
        'Retry mechanisms',
        'Webhook security',
        'Event history tracking'
      ]
    },
    {
      title: 'Documentation',
      description: 'Comprehensive API documentation with examples and guides.',
      icon: BookOpen,
      benefits: [
        'Interactive API explorer',
        'Code examples',
        'Request/response schemas',
        'Error handling guides',
        'Best practices',
        'SDK documentation'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Get API Key',
      description: 'Generate your API key for authentication',
      icon: Key,
      action: () => console.log('Get API key')
    },
    {
      title: 'View Endpoints',
      description: 'Browse available API endpoints',
      icon: Globe,
      action: () => console.log('View endpoints')
    },
    {
      title: 'Download SDK',
      description: 'Download SDK for your language',
      icon: Download,
      action: () => console.log('Download SDK')
    },
    {
      title: 'API Explorer',
      description: 'Test API endpoints interactively',
      icon: Terminal,
      action: () => console.log('API explorer')
    }
  ];

  const apiEndpoints = [
    {
      category: 'Students',
      endpoints: [
        { method: 'GET', path: '/api/students', description: 'List all students' },
        { method: 'POST', path: '/api/students', description: 'Create a new student' },
        { method: 'GET', path: '/api/students/{id}', description: 'Get student details' },
        { method: 'PUT', path: '/api/students/{id}', description: 'Update student' },
        { method: 'DELETE', path: '/api/students/{id}', description: 'Delete student' }
      ]
    },
    {
      category: 'Classes',
      endpoints: [
        { method: 'GET', path: '/api/classes', description: 'List all classes' },
        { method: 'POST', path: '/api/classes', description: 'Create a new class' },
        { method: 'GET', path: '/api/classes/{id}', description: 'Get class details' },
        { method: 'PUT', path: '/api/classes/{id}', description: 'Update class' },
        { method: 'DELETE', path: '/api/classes/{id}', description: 'Delete class' }
      ]
    },
    {
      category: 'Grades',
      endpoints: [
        { method: 'GET', path: '/api/grades', description: 'List all grades' },
        { method: 'POST', path: '/api/grades', description: 'Create a new grade' },
        { method: 'GET', path: '/api/grades/{id}', description: 'Get grade details' },
        { method: 'PUT', path: '/api/grades/{id}', description: 'Update grade' },
        { method: 'DELETE', path: '/api/grades/{id}', description: 'Delete grade' }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Docs" />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <Code className="w-12 h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              API Reference
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comprehensive API documentation for integrating Scholarify into your applications. 
              Access all platform features through our RESTful API with secure authentication.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-4">
                            <Icon className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-4 h-4 text-teal-500 mr-2 flex-shrink-0" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
                            <Icon className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'authentication' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Authentication
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      API Key Authentication
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Generate API keys from dashboard</li>
                      <li>• Include in Authorization header</li>
                      <li>• Bearer token format</li>
                      <li>• Key rotation support</li>
                      <li>• Permission-based access</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      OAuth 2.0
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Standard OAuth 2.0 flow</li>
                      <li>• Authorization code grant</li>
                      <li>• Refresh token support</li>
                      <li>• Scope-based permissions</li>
                      <li>• Secure token storage</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'endpoints' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                API Endpoints
              </h3>
              <div className="space-y-8">
                {apiEndpoints.map((category, index) => (
                  <div key={index}>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                      {category.category}
                    </h4>
                    <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4">
                      <div className="space-y-3">
                        {category.endpoints.map((endpoint, idx) => (
                          <div key={idx} className="flex items-center space-x-4">
                            <span className={`px-2 py-1 rounded text-xs font-mono ${
                              endpoint.method === 'GET' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' :
                              endpoint.method === 'POST' ? 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' :
                              endpoint.method === 'PUT' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200' :
                              'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
                            }`}>
                              {endpoint.method}
                            </span>
                            <code className="text-sm bg-gray-100 dark:bg-gray-600 px-2 py-1 rounded">
                              {endpoint.path}
                            </code>
                            <span className="text-sm text-gray-600 dark:text-gray-400">
                              {endpoint.description}
                            </span>
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'examples' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Code Examples
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      JavaScript Example
                    </h4>
                    <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm overflow-x-auto">
{`// Get all students
const response = await fetch('/api/students', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY',
    'Content-Type': 'application/json'
  }
});

const students = await response.json();`}
                    </pre>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Python Example
                    </h4>
                    <pre className="bg-gray-100 dark:bg-gray-700 p-4 rounded-lg text-sm overflow-x-auto">
{`import requests

# Get all students
response = requests.get(
    'https://api.scholarify.com/students',
    headers={
        'Authorization': 'Bearer YOUR_API_KEY',
        'Content-Type': 'application/json'
    }
)

students = response.json()`}
                    </pre>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'sdk' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                SDK & Libraries
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Available SDKs
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• JavaScript/TypeScript SDK</li>
                      <li>• Python SDK</li>
                      <li>• PHP SDK</li>
                      <li>• Java SDK</li>
                      <li>• C# SDK</li>
                      <li>• Mobile SDKs (iOS/Android)</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      SDK Features
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Type-safe API calls</li>
                      <li>• Automatic authentication</li>
                      <li>• Error handling</li>
                      <li>• Request/response models</li>
                      <li>• Documentation and examples</li>
                      <li>• Community support</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'webhooks' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Webhooks
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Available Events
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• student.created</li>
                      <li>• student.updated</li>
                      <li>• grade.added</li>
                      <li>• attendance.recorded</li>
                      <li>• payment.received</li>
                      <li>• announcement.sent</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Webhook Features
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Real-time notifications</li>
                      <li>• Event filtering</li>
                      <li>• Retry mechanisms</li>
                      <li>• Security verification</li>
                      <li>• Event history</li>
                      <li>• Custom endpoints</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Integrate Scholarify?
          </h2>
          <p className="text-xl mb-8 text-teal-100">
            Start building powerful integrations with our comprehensive API and SDKs.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 