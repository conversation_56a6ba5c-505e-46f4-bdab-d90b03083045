"use client";

import React from 'react';
import { UpgradeModalProvider } from './UpgradeModal';
import { FeatureLockedModalProvider } from './FeatureLockedModal';
import { LimitExceededModalProvider } from './LimitExceededModal';

/**
 * Provider global pour toutes les modales de subscription
 * Ce composant doit être placé au niveau racine de l'application
 * pour que les modales puissent être affichées depuis n'importe où
 */
export const SubscriptionModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  return (
    <UpgradeModalProvider>
      <FeatureLockedModalProvider>
        <LimitExceededModalProvider>
          {children}
        </LimitExceededModalProvider>
      </FeatureLockedModalProvider>
    </UpgradeModalProvider>
  );
};

export default SubscriptionModalProvider;
