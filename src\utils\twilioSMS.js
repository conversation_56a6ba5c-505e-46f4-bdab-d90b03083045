// utils/sendSMS.js
const twilio = require('twilio');

const client = twilio(
  process.env.TWILIO_ACCOUNT_SID,
  process.env.TWILIO_AUTH_TOKEN
);

/**
 * Send SMS using Twilio
 * @param {string} to - Recipient phone number in E.164 format (e.g., +2376XXXXXXXX)
 * @param {string} message - The message text to send
 * @param {string} from - Sender phone number from your Twilio account
 */
const twilioSMS = async (to, message, from = process.env.TWILIO_PHONE_NUMBER) => {
  try {
    const response = await client.messages.create({
      body: message,
      from,
      to,
    });
    console.log('Twilio SMS response:', response);
    return response;
  } catch (err) {
    console.error('Twilio SMS error:', err);
    throw err;
  }
};

module.exports = twilioSMS;
