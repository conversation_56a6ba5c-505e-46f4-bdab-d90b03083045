import { getTokenFromCookie } from './UserServices';
import { useTranslation } from '@/hooks/useTranslation';

export interface ApiError {
  message: string;
  error_code: string;
  status: number;
  data?: any;
}

export interface SubscriptionError extends ApiError {
  feature_id?: string;
  subscription_required?: string;
  current_subscription?: string;
  upgrade_url?: string;
}

export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  message?: string;
  error_code?: string;
  subscription_required?: string;
  current_subscription?: string;
  feature_id?: string;
}

/**
 * Service pour intercepter et gérer les appels API avec gestion des erreurs d'abonnement
 */
export class ApiInterceptorService {
  private static baseUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api';

  /**
   * Effectue un appel API avec gestion automatique des erreurs d'abonnement
   */
  static async apiCall<T = any>(
    endpoint: string,
    options: RequestInit = {},
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    try {
      const token = getTokenFromCookie('idToken');
      
      const defaultHeaders = {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`,
        ...options.headers
      };

      const response = await fetch(`${this.baseUrl}${endpoint}`, {
        ...options,
        headers: defaultHeaders
      });

      const data: ApiResponse<T> = await response.json();

      if (!response.ok) {
        await this.handleApiError(response, data, showUpgradePrompt);
      }

      if (!data.success) {
        throw new Error(data.message || 'API call failed');
      }

      return data.data as T;
    } catch (error) {
      console.error('API call error:', error);
      throw error;
    }
  }

  /**
   * Gère les erreurs d'API avec traitement spécial pour les erreurs d'abonnement
   */
  private static async handleApiError(
    response: Response,
    data: ApiResponse,
    showUpgradePrompt: boolean
  ): Promise<void> {
    switch (response.status) {
      case 402: // Payment Required - Subscription issue
        if (showUpgradePrompt) {
          await this.handleSubscriptionError(data as SubscriptionError);
        }
        throw new SubscriptionRequiredError(data as SubscriptionError);

      case 403: // Forbidden - Feature access denied
        if (data.error_code === 'FEATURE_ACCESS_DENIED' && showUpgradePrompt) {
          await this.handleFeatureAccessDenied(data as SubscriptionError);
        }
        throw new FeatureAccessDeniedError(data as SubscriptionError);

      case 404:
        if (data.error_code === 'FEATURE_NOT_FOUND') {
          throw new FeatureNotFoundError(data);
        }
        throw new NotFoundError(data);

      case 429: // Too Many Requests - Feature limit exceeded
        if (data.error_code === 'FEATURE_LIMIT_EXCEEDED') {
          await this.handleFeatureLimitExceeded(data as SubscriptionError);
        }
        throw new FeatureLimitExceededError(data as SubscriptionError);

      default:
        throw new ApiError(data);
    }
  }

  /**
   * Gère les erreurs d'abonnement insuffisant
   */
  private static async handleSubscriptionError(error: SubscriptionError): Promise<void> {
    const { showUpgradeModal } = await import('@/components/subscription/UpgradeModal');

    showUpgradeModal({
      currentPlan: error.current_subscription || 'basic',
      requiredPlan: error.subscription_required || 'standard',
      featureId: error.feature_id,
      message: error.message
    });
  }

  /**
   * Gère les erreurs d'accès aux fonctionnalités
   */
  private static async handleFeatureAccessDenied(error: SubscriptionError): Promise<void> {
    const { showFeatureLockedModal } = await import('@/components/subscription/FeatureLockedModal');

    showFeatureLockedModal({
      featureId: error.feature_id || '',
      requiredPlan: error.subscription_required || 'standard',
      currentPlan: error.current_subscription || 'basic'
    });
  }

  /**
   * Gère les erreurs de limite de fonctionnalité dépassée
   */
  private static async handleFeatureLimitExceeded(error: SubscriptionError): Promise<void> {
    const { showLimitExceededModal } = await import('@/components/subscription/LimitExceededModal');

    showLimitExceededModal({
      featureId: error.feature_id || '',
      limitDetails: error.data || {},
      upgradeUrl: error.upgrade_url
    });
  }

  /**
   * Appel API GET avec gestion d'erreurs
   */
  static async get<T = any>(endpoint: string, showUpgradePrompt: boolean = true): Promise<T> {
    return this.apiCall<T>(endpoint, { method: 'GET' }, showUpgradePrompt);
  }

  /**
   * Appel API POST avec gestion d'erreurs
   */
  static async post<T = any>(
    endpoint: string, 
    data?: any, 
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    return this.apiCall<T>(
      endpoint, 
      { 
        method: 'POST', 
        body: data ? JSON.stringify(data) : undefined 
      }, 
      showUpgradePrompt
    );
  }

  /**
   * Appel API PUT avec gestion d'erreurs
   */
  static async put<T = any>(
    endpoint: string, 
    data?: any, 
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    return this.apiCall<T>(
      endpoint, 
      { 
        method: 'PUT', 
        body: data ? JSON.stringify(data) : undefined 
      }, 
      showUpgradePrompt
    );
  }

  /**
   * Appel API DELETE avec gestion d'erreurs
   */
  static async delete<T = any>(endpoint: string, showUpgradePrompt: boolean = true): Promise<T> {
    return this.apiCall<T>(endpoint, { method: 'DELETE' }, showUpgradePrompt);
  }

  /**
   * Upload de fichier avec gestion d'erreurs
   */
  static async uploadFile<T = any>(
    endpoint: string,
    file: File,
    fieldName: string = 'file',
    additionalData?: Record<string, string>,
    showUpgradePrompt: boolean = true
  ): Promise<T> {
    const token = getTokenFromCookie('idToken');
    const formData = new FormData();
    
    formData.append(fieldName, file);
    
    if (additionalData) {
      Object.entries(additionalData).forEach(([key, value]) => {
        formData.append(key, value);
      });
    }

    return this.apiCall<T>(
      endpoint,
      {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`
          // Ne pas définir Content-Type pour FormData
        },
        body: formData
      },
      showUpgradePrompt
    );
  }

  /**
   * Vérification silencieuse d'accès à une fonctionnalité
   */
  static async checkFeatureAccess(featureId: string, schoolId: string): Promise<boolean> {
    try {
      await this.get(`/feature-registry/check/${featureId}/school/${schoolId}`, false);
      return true;
    } catch (error) {
      if (error instanceof SubscriptionRequiredError || error instanceof FeatureAccessDeniedError) {
        return false;
      }
      throw error;
    }
  }
}

// Classes d'erreur personnalisées
export class SubscriptionRequiredError extends Error {
  public readonly subscriptionError: SubscriptionError;

  constructor(error: SubscriptionError) {
    super(error.message);
    this.name = 'SubscriptionRequiredError';
    this.subscriptionError = error;
  }
}

export class FeatureAccessDeniedError extends Error {
  public readonly subscriptionError: SubscriptionError;

  constructor(error: SubscriptionError) {
    super(error.message);
    this.name = 'FeatureAccessDeniedError';
    this.subscriptionError = error;
  }
}

export class FeatureLimitExceededError extends Error {
  public readonly subscriptionError: SubscriptionError;

  constructor(error: SubscriptionError) {
    super(error.message);
    this.name = 'FeatureLimitExceededError';
    this.subscriptionError = error;
  }
}

export class FeatureNotFoundError extends Error {
  public readonly apiError: ApiError;

  constructor(error: ApiError) {
    super(error.message);
    this.name = 'FeatureNotFoundError';
    this.apiError = error;
  }
}

export class NotFoundError extends Error {
  public readonly apiError: ApiError;

  constructor(error: ApiError) {
    super(error.message);
    this.name = 'NotFoundError';
    this.apiError = error;
  }
}

export class ApiError extends Error {
  public readonly apiError: ApiError;

  constructor(error: ApiError) {
    super(error.message);
    this.name = 'ApiError';
    this.apiError = error;
  }
}

export default ApiInterceptorService;
