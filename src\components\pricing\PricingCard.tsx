"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Check, Star, ArrowRight, MessageCircle, Mail, Phone, Loader2 } from 'lucide-react';
import { SubscriptionPlanSchema } from '@/app/models/SchoolSubscriptionModel';
import { useRouter } from 'next/navigation';
import useAuth from '@/app/hooks/useAuth';

interface PricingCardProps {
  plan: SubscriptionPlanSchema;
  onPlanSelect?: (plan: SubscriptionPlanSchema) => void;
}

export default function PricingCard({ plan, onPlanSelect }: PricingCardProps) {
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [loading, setLoading] = useState(false);

  const handleSelectPlan = async () => {
    if (authLoading || loading) return;

    setLoading(true);

    try {
      if (plan.plan_name === 'custom') {
        // Redirect to contact form or show contact modal
        window.location.href = `mailto:${plan.contact_info?.email}?subject=Demande de plan personnalisé`;
        return;
      }

      // Check if user is authenticated
      if (!user) {
        // User not logged in - redirect to login with selected plan
        router.push(`/login?plan=${plan.plan_name}`);
        return;
      }

      // User is logged in - check if they have school admin role
      if (user.role === 'admin' && user.school_ids && user.school_ids.length > 0) {
        // User is school admin - show plan selection modal or redirect to buy credits
        if (onPlanSelect) {
          onPlanSelect(plan);
        } else {
          // Fallback: redirect to buy credits page with selected plan
          router.push(`/school-admin/buy-credit?plan=${plan.plan_name}`);
        }
      } else {
        // User is logged in but not school admin - redirect to login
        router.push(`/login?plan=${plan.plan_name}`);
      }
    } catch (error) {
      console.error('Error handling plan selection:', error);
      // Fallback to registration page
      router.push(`/login?plan=${plan.plan_name}`);
    } finally {
      setLoading(false);
    }
  };

  const getCardStyle = () => {
    const baseStyle = "relative bg-white dark:bg-gray-800 rounded-2xl shadow-lg border-2 transition-all duration-300 hover:shadow-xl";

    if (plan.is_popular) {
      return `${baseStyle} border-teal dark:border-teal transform hover:scale-105`;
    }

    if (plan.plan_name === 'custom') {
      return `${baseStyle} border-tealdarker dark:border-teal bg-gradient-to-br from-teal-50 to-white dark:from-teal-900/20 dark:to-gray-800`;
    }

    return `${baseStyle} border-gray-200 dark:border-gray-700 hover:border-gray-300 dark:hover:border-gray-600`;
  };

  const getButtonStyle = () => {
    const baseStyle = "w-full py-3 px-6 font-semibold rounded-lg transition-all duration-300 flex items-center justify-center";
    const disabledStyle = "disabled:opacity-50 disabled:cursor-not-allowed";

    if (plan.plan_name === 'custom') {
      return `${baseStyle} bg-gradient-to-r from-tealdarker to-teal text-white hover:from-teal hover:to-tealdarker ${disabledStyle}`;
    }

    if (plan.is_popular) {
      return `${baseStyle} bg-teal text-white hover:bg-tealdarker ${disabledStyle}`;
    }

    return `${baseStyle} border-2 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:border-gray-400 dark:hover:border-gray-500 hover:bg-gray-50 dark:hover:bg-gray-700 ${disabledStyle}`;
  };

  const getButtonText = () => {
    if (loading) return '';

    if (plan.plan_name === 'custom') {
      return 'Nous contacter';
    }

    if (!user) {
      return 'Choisir ce plan';
    }

    if (user.role === 'admin' && user.school_ids && user.school_ids.length > 0) {
      return 'Sélectionner ce plan';
    }

    return 'Choisir ce plan';
  };

  return (
    <motion.div
      className={getCardStyle()}
      whileHover={{ y: -5 }}
      transition={{ duration: 0.3 }}
    >
      {/* Popular Badge */}
      {plan.is_popular && (
        <div className="absolute -top-4 left-1/2 transform -translate-x-1/2">
          <div className="bg-teal text-white px-4 py-1 rounded-full text-sm font-semibold flex items-center">
            <Star className="h-4 w-4 mr-1" />
            Plus populaire
          </div>
        </div>
      )}

      <div className="p-8">
        {/* Plan Header */}
        <div className="text-center mb-8">
          <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-2">
            {plan.display_name}
          </h3>
          <p className="text-gray-600 dark:text-gray-300 mb-6">
            {plan.description}
          </p>
          
          {/* Pricing */}
          <div className="mb-6">
            {plan.plan_name === 'custom' ? (
              <div>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  Sur mesure
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  Tarification personnalisée
                </div>
              </div>
            ) : (
              <div>
                <div className="text-4xl font-bold text-gray-900 dark:text-white mb-2">
                  {plan?.price_per_credit?.toLocaleString()} FCFA
                  <span className="text-lg font-normal text-gray-600 dark:text-gray-300">/crédit</span>
                </div>
                <div className="text-gray-600 dark:text-gray-300">
                  Minimum {plan?.minimum_purchase} crédit{plan?.minimum_purchase && plan?.minimum_purchase > 1 ? 's' : ''}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Features */}
        <div className="mb-8">
          <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Fonctionnalités incluses :</h4>
          <ul className="space-y-3">
            {plan.benefits.map((benefit, index) => (
              <li key={index} className="flex items-start">
                <Check className="h-5 w-5 text-teal mr-3 mt-0.5 flex-shrink-0" />
                <span className="text-gray-700 dark:text-gray-300">{benefit}</span>
              </li>
            ))}
          </ul>

          {/* Chatbot Info */}
          {plan.chatbot_enabled && (
            <div className="mt-4 p-3 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
              <div className="flex items-center text-teal dark:text-teal">
                <MessageCircle className="h-4 w-4 mr-2" />
                <span className="text-sm font-medium">
                  Chatbot IA : {plan.chatbot_cost_per_message} crédit/message
                </span>
              </div>
            </div>
          )}
        </div>

        {/* Limitations */}
        {plan.limitations && plan.limitations.length > 0 && (
          <div className="mb-8">
            <h4 className="font-semibold text-gray-900 dark:text-white mb-4">Limitations :</h4>
            <ul className="space-y-2">
              {plan.limitations.map((limitation, index) => (
                <li key={index} className="flex items-start text-sm text-gray-600 dark:text-gray-400">
                  <span className="mr-2">•</span>
                  <span>{limitation}</span>
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Contact Info for Custom Plan */}
        {plan.plan_name === 'custom' && plan.contact_info && (
          <div className="mb-8 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg">
            <h4 className="font-semibold text-teal dark:text-teal mb-3">Contactez-nous :</h4>
            <div className="space-y-2">
              {plan.contact_info.email && (
                <div className="flex items-center text-tealdarker dark:text-teal">
                  <Mail className="h-4 w-4 mr-2" />
                  <span className="text-sm">{plan.contact_info.email}</span>
                </div>
              )}
              {plan.contact_info.phone && (
                <div className="flex items-center text-tealdarker dark:text-teal">
                  <Phone className="h-4 w-4 mr-2" />
                  <span className="text-sm">{plan.contact_info.phone}</span>
                </div>
              )}
            </div>
            {plan.contact_info.message && (
              <p className="text-sm text-tealdarker dark:text-teal mt-3">
                {plan.contact_info.message}
              </p>
            )}
          </div>
        )}

        {/* CTA Button */}
        <button
          onClick={handleSelectPlan}
          disabled={authLoading || loading}
          className={getButtonStyle()}
        >
          {loading ? (
            <Loader2 className="h-4 w-4 animate-spin" />
          ) : (
            <span className="flex items-center justify-center">
              {getButtonText()}
              <ArrowRight className="h-4 w-4 ml-2" />
            </span>
          )}
        </button>

        {/* Additional Info */}
        <div className="mt-4 text-center">
          <p className="text-xs text-gray-500">
            {plan.plan_name === 'custom' 
              ? 'Devis personnalisé sous 24h'
              : 'Commencez avec 5 crédits gratuits'
            }
          </p>
        </div>
      </div>
    </motion.div>
  );
}
