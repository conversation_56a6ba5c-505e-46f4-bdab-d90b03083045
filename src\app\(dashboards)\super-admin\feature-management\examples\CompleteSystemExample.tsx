"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Users, 
  Shield, 
  Settings, 
  Crown, 
  Zap, 
  Star,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Info
} from 'lucide-react';

// Composants du système de subscription
import FeatureGate from '@/app/components/subscription/FeatureGate';
import FeatureButton from '@/app/components/subscription/FeatureButton';
import SubscriptionStatus from '@/app/components/subscription/SubscriptionStatus';
import { SubscriptionModalProvider } from '@/app/components/subscription/SubscriptionModalProvider';

// Services
import { useSubscription } from '@/hooks/useSubscription';
import { useTranslation } from '@/hooks/useTranslation';

/**
 * Exemple complet d'utilisation du système de contrôle d'accès basé sur les abonnements
 * 
 * Cet exemple montre comment intégrer tous les composants du système :
 * - FeatureGate pour le contrôle d'accès conditionnel
 * - FeatureButton pour les actions avec gestion d'abonnement
 * - SubscriptionStatus pour l'affichage du statut
 * - Gestion automatique des modales d'upgrade
 */
const CompleteSystemExample: React.FC = () => {
  const { t } = useTranslation();
  const { subscription, loading } = useSubscription();
  const [selectedStudents, setSelectedStudents] = useState<string[]>([]);
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false);

  // Données d'exemple
  const students = [
    { id: '1', name: 'Alice Martin', class: '6ème A', status: 'active' },
    { id: '2', name: 'Bob Dupont', class: '5ème B', status: 'active' },
    { id: '3', name: 'Claire Durand', class: '4ème C', status: 'inactive' },
  ];

  const handleBulkAction = (action: string) => {
    console.log(`Action en lot: ${action} sur ${selectedStudents.length} étudiants`);
    // Ici, l'ApiInterceptorService gérera automatiquement les erreurs d'abonnement
  };

  const handleExportStudents = () => {
    console.log('Export des étudiants...');
    // Cette action sera interceptée si l'abonnement ne le permet pas
  };

  const handleAdvancedSearch = () => {
    console.log('Recherche avancée...');
    // Fonctionnalité premium
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <SubscriptionModalProvider>
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 p-6">
        <div className="max-w-7xl mx-auto space-y-6">
          {/* Header avec statut d'abonnement */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                  <Users className="w-8 h-8 mr-3 text-blue-600" />
                  Gestion des Étudiants
                </h1>
                <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                  Exemple complet du système de contrôle d'accès
                </p>
              </div>
              
              <SubscriptionStatus />
            </div>
          </div>

          {/* Fonctionnalités de base (toujours accessibles) */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
              <Star className="w-5 h-5 mr-2 text-gray-500" />
              Fonctionnalités de Base (Basic)
            </h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Users className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Voir les étudiants
                </div>
              </button>
              
              <button className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Settings className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Paramètres de base
                </div>
              </button>
              
              <button className="p-4 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
                <Info className="w-6 h-6 text-blue-600 mx-auto mb-2" />
                <div className="text-sm font-medium text-gray-900 dark:text-gray-100">
                  Informations
                </div>
              </button>
            </div>
          </div>

          {/* Fonctionnalités Standard */}
          <FeatureGate 
            featureId="student_bulk_actions" 
            fallback={
              <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-6">
                <div className="flex items-center space-x-3">
                  <Zap className="w-6 h-6 text-yellow-600" />
                  <div>
                    <h3 className="font-medium text-yellow-800 dark:text-yellow-400">
                      Fonctionnalités Standard
                    </h3>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300 mt-1">
                      Passez à un abonnement Standard pour accéder aux actions en lot et à l'export.
                    </p>
                  </div>
                </div>
              </div>
            }
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-blue-500" />
                Fonctionnalités Standard
              </h2>
              
              <div className="space-y-4">
                {/* Actions en lot */}
                <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Actions en lot
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {selectedStudents.length} étudiant(s) sélectionné(s)
                    </p>
                  </div>
                  
                  <div className="flex space-x-2">
                    <FeatureButton
                      featureId="student_bulk_actions"
                      onClick={() => handleBulkAction('activate')}
                      className="px-3 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors"
                      disabled={selectedStudents.length === 0}
                    >
                      <CheckCircle className="w-4 h-4 mr-1" />
                      Activer
                    </FeatureButton>
                    
                    <FeatureButton
                      featureId="student_bulk_actions"
                      onClick={() => handleBulkAction('deactivate')}
                      className="px-3 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors"
                      disabled={selectedStudents.length === 0}
                    >
                      <XCircle className="w-4 h-4 mr-1" />
                      Désactiver
                    </FeatureButton>
                  </div>
                </div>

                {/* Export */}
                <div className="flex items-center justify-between p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div>
                    <h3 className="font-medium text-gray-900 dark:text-gray-100">
                      Export des données
                    </h3>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      Exporter la liste des étudiants en CSV ou Excel
                    </p>
                  </div>
                  
                  <FeatureButton
                    featureId="student_export"
                    onClick={handleExportStudents}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
                  >
                    Export CSV
                  </FeatureButton>
                </div>
              </div>
            </div>
          </FeatureGate>

          {/* Fonctionnalités Premium */}
          <FeatureGate 
            featureId="student_advanced_analytics" 
            fallback={
              <div className="bg-purple-50 dark:bg-purple-900/20 border border-purple-200 dark:border-purple-800 rounded-lg p-6">
                <div className="flex items-center space-x-3">
                  <Crown className="w-6 h-6 text-purple-600" />
                  <div>
                    <h3 className="font-medium text-purple-800 dark:text-purple-400">
                      Fonctionnalités Premium
                    </h3>
                    <p className="text-sm text-purple-700 dark:text-purple-300 mt-1">
                      Débloquez les analytics avancés et la recherche intelligente avec Premium.
                    </p>
                  </div>
                </div>
              </div>
            }
          >
            <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
                <Crown className="w-5 h-5 mr-2 text-yellow-500" />
                Fonctionnalités Premium
              </h2>
              
              <div className="space-y-4">
                {/* Recherche avancée */}
                <div className="p-4 bg-gradient-to-r from-purple-50 to-pink-50 dark:from-purple-900/20 dark:to-pink-900/20 rounded-lg">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium text-gray-900 dark:text-gray-100">
                        Recherche Avancée
                      </h3>
                      <p className="text-sm text-gray-600 dark:text-gray-400">
                        Filtres intelligents et recherche sémantique
                      </p>
                    </div>
                    
                    <FeatureButton
                      featureId="student_advanced_search"
                      onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                      className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors"
                    >
                      {showAdvancedFilters ? 'Masquer' : 'Afficher'} Filtres
                    </FeatureButton>
                  </div>
                  
                  {showAdvancedFilters && (
                    <motion.div
                      initial={{ opacity: 0, height: 0 }}
                      animate={{ opacity: 1, height: 'auto' }}
                      className="mt-4 p-4 bg-white dark:bg-gray-800 rounded-lg border border-purple-200 dark:border-purple-700"
                    >
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        <select className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg">
                          <option>Niveau scolaire</option>
                          <option>6ème</option>
                          <option>5ème</option>
                          <option>4ème</option>
                        </select>
                        <select className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg">
                          <option>Statut</option>
                          <option>Actif</option>
                          <option>Inactif</option>
                        </select>
                        <input 
                          type="text" 
                          placeholder="Recherche sémantique..."
                          className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg"
                        />
                      </div>
                    </motion.div>
                  )}
                </div>

                {/* Analytics */}
                <div className="p-4 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-lg">
                  <h3 className="font-medium text-gray-900 dark:text-gray-100 mb-3">
                    Analytics Avancés
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">87%</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Taux de présence</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-green-600">92%</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Satisfaction</div>
                    </div>
                    <div className="text-center">
                      <div className="text-2xl font-bold text-purple-600">15.2</div>
                      <div className="text-sm text-gray-600 dark:text-gray-400">Moyenne générale</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </FeatureGate>

          {/* Liste des étudiants avec sélection */}
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6">
            <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">
              Liste des Étudiants
            </h2>
            
            <div className="space-y-2">
              {students.map((student) => (
                <div 
                  key={student.id}
                  className="flex items-center space-x-3 p-3 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  <input
                    type="checkbox"
                    checked={selectedStudents.includes(student.id)}
                    onChange={(e) => {
                      if (e.target.checked) {
                        setSelectedStudents(prev => [...prev, student.id]);
                      } else {
                        setSelectedStudents(prev => prev.filter(id => id !== student.id));
                      }
                    }}
                    className="w-4 h-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500"
                  />
                  <div className="flex-1">
                    <div className="font-medium text-gray-900 dark:text-gray-100">
                      {student.name}
                    </div>
                    <div className="text-sm text-gray-500 dark:text-gray-400">
                      {student.class}
                    </div>
                  </div>
                  <div className={`px-2 py-1 rounded-full text-xs ${
                    student.status === 'active' 
                      ? 'bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400'
                      : 'bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400'
                  }`}>
                    {student.status === 'active' ? 'Actif' : 'Inactif'}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Informations sur l'abonnement */}
          <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-6">
            <div className="flex items-start space-x-3">
              <Info className="w-6 h-6 text-blue-600 mt-1" />
              <div>
                <h3 className="font-medium text-blue-800 dark:text-blue-400">
                  Comment ça marche
                </h3>
                <div className="text-sm text-blue-700 dark:text-blue-300 mt-2 space-y-1">
                  <p>• Les fonctionnalités sont automatiquement masquées selon votre abonnement</p>
                  <p>• Les boutons déclenchent des modales d'upgrade si nécessaire</p>
                  <p>• Les appels API sont interceptés pour gérer les limites d'abonnement</p>
                  <p>• L'interface s'adapte dynamiquement à vos permissions</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </SubscriptionModalProvider>
  );
};

export default CompleteSystemExample;
