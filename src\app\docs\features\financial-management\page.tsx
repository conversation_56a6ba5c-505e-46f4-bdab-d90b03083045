"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  CreditCard,
  DollarSign,
  BarChart3,
  FileText,
  CheckCircle,
  ArrowRight,
  Receipt,
  Calculator,
  TrendingUp,
  Shield,
  Users,
  Calendar,
  Settings,
  Eye,
  Edit,
  Plus,
  Download
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function FinancialManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'fees', name: 'Fee Management', icon: Receipt },
    { id: 'payments', name: 'Payments', icon: CreditCard },
    { id: 'reports', name: 'Financial Reports', icon: BarChart3 },
    { id: 'invoices', name: 'Invoices', icon: FileText },
    { id: 'analytics', name: 'Analytics', icon: TrendingUp },
  ];

  const features = [
    {
      title: 'Fee Structure Management',
      description: 'Create and manage comprehensive fee structures with multiple categories and payment plans.',
      icon: Receipt,
      benefits: [
        'Multiple fee categories (tuition, transport, meals, etc.)',
        'Flexible payment schedules',
        'Discount and scholarship management',
        'Late fee configuration',
        'Fee structure templates',
        'Multi-currency support'
      ]
    },
    {
      title: 'Payment Processing',
      description: 'Streamlined payment collection with multiple payment methods and automated tracking.',
      icon: CreditCard,
      benefits: [
        'Multiple payment methods (cash, card, bank transfer)',
        'Online payment integration',
        'Payment plan management',
        'Automatic payment reminders',
        'Payment history tracking',
        'Receipt generation'
      ]
    },
    {
      title: 'Financial Reporting',
      description: 'Comprehensive financial reports and analytics for better decision making.',
      icon: BarChart3,
      benefits: [
        'Revenue and collection reports',
        'Outstanding balance tracking',
        'Payment trend analysis',
        'Custom report generation',
        'Export capabilities',
        'Real-time financial dashboards'
      ]
    },
    {
      title: 'Invoice Management',
      description: 'Automated invoice generation and management with professional templates.',
      icon: FileText,
      benefits: [
        'Automated invoice generation',
        'Professional invoice templates',
        'Bulk invoice processing',
        'Invoice status tracking',
        'Payment reminders',
        'Digital invoice delivery'
      ]
    },
    {
      title: 'Financial Analytics',
      description: 'Advanced analytics and insights for financial performance monitoring.',
      icon: TrendingUp,
      benefits: [
        'Revenue trend analysis',
        'Collection rate monitoring',
        'Financial forecasting',
        'Performance comparisons',
        'Cash flow analysis',
        'Budget tracking'
      ]
    },
    {
      title: 'Security & Compliance',
      description: 'Enterprise-grade security with financial data protection and compliance.',
      icon: Shield,
      benefits: [
        'Data encryption',
        'Audit trail logging',
        'Financial compliance',
        'Secure payment processing',
        'Access controls',
        'Data backup and recovery'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Create Fee Structure',
      description: 'Set up new fee categories and payment plans',
      icon: Plus,
      action: () => console.log('Create fee structure')
    },
    {
      title: 'Process Payment',
      description: 'Record and process student payments',
      icon: CreditCard,
      action: () => console.log('Process payment')
    },
    {
      title: 'Generate Invoice',
      description: 'Create invoices for outstanding balances',
      icon: FileText,
      action: () => console.log('Generate invoice')
    },
    {
      title: 'Financial Report',
      description: 'Generate comprehensive financial reports',
      icon: BarChart3,
      action: () => console.log('Generate report')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Features" />
      
      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-4 sm:mb-6">
              <div className="p-3 sm:p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <CreditCard className="w-8 h-8 sm:w-12 sm:h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
              Financial Management
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4">
              Complete financial tracking including fees, payments, and financial reporting. 
              Streamline your school's financial operations with powerful management tools.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-6 sm:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-8 sm:space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 sm:p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3 sm:mr-4">
                            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-1 sm:space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-teal-500 mr-2 flex-shrink-0" />
                              <span className="line-clamp-2">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-4 sm:p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-2 sm:mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-2 sm:mr-3">
                            <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 text-sm sm:text-base">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'fees' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Fee Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Fee Structure Setup
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Create fee categories (tuition, transport, meals)</li>
                      <li>• Set payment schedules and due dates</li>
                      <li>• Configure late fees and penalties</li>
                      <li>• Apply discounts and scholarships</li>
                      <li>• Multi-currency support</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Fee Collection
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Track fee collection progress</li>
                      <li>• Generate collection reports</li>
                      <li>• Monitor outstanding balances</li>
                      <li>• Send payment reminders</li>
                      <li>• Handle fee waivers</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'payments' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Payment Processing
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Payment Methods
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Cash payment recording</li>
                      <li>• Credit/debit card processing</li>
                      <li>• Bank transfer tracking</li>
                      <li>• Online payment integration</li>
                      <li>• Mobile payment support</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Payment Tracking
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Payment history logging</li>
                      <li>• Receipt generation</li>
                      <li>• Payment confirmation</li>
                      <li>• Refund processing</li>
                      <li>• Payment reconciliation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Financial Reports
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Standard Reports
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Revenue collection reports</li>
                      <li>• Outstanding balance reports</li>
                      <li>• Payment trend analysis</li>
                      <li>• Fee structure reports</li>
                      <li>• Collection efficiency reports</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Custom Reports
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Custom report builder</li>
                      <li>• Data export capabilities</li>
                      <li>• Scheduled report generation</li>
                      <li>• Interactive dashboards</li>
                      <li>• Real-time analytics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'invoices' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Invoice Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Invoice Generation
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Automated invoice creation</li>
                      <li>• Professional templates</li>
                      <li>• Bulk invoice processing</li>
                      <li>• Custom invoice numbering</li>
                      <li>• Digital invoice delivery</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Invoice Tracking
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Invoice status monitoring</li>
                      <li>• Payment reminders</li>
                      <li>• Overdue invoice alerts</li>
                      <li>• Invoice history</li>
                      <li>• Payment reconciliation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'analytics' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Financial Analytics
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Performance Analytics
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Revenue trend analysis</li>
                      <li>• Collection rate monitoring</li>
                      <li>• Payment pattern analysis</li>
                      <li>• Financial forecasting</li>
                      <li>• Performance comparisons</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Business Intelligence
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Cash flow analysis</li>
                      <li>• Budget tracking</li>
                      <li>• Financial dashboards</li>
                      <li>• KPI monitoring</li>
                      <li>• Predictive analytics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 dark:bg-teal-600 text-white py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6">
            Ready to Streamline Your Financial Management?
          </h2>
          <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-teal-100 dark:text-teal-100 max-w-3xl mx-auto">
            Start using these powerful financial management tools to improve your school's financial operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 dark:bg-white dark:text-teal-600 dark:hover:bg-gray-100 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-teal-600 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 