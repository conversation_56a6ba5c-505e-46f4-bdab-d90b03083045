const FeatureRegistry = require('../models/FeatureRegistry');
const SchoolSubscription = require('../models/SchoolSubscription');
const NodeCache = require('node-cache');

// Cache for feature data (TTL: 10 minutes)
const featureCache = new NodeCache({ stdTTL: 600, checkperiod: 120 });

/**
 * Service for managing feature registry operations
 */
class FeatureRegistryService {
  
  /**
   * Get all features available for a subscription level
   * @param {string} subscriptionLevel - Subscription level (basic, standard, premium, enterprise)
   * @param {boolean} useCache - Whether to use cache
   * @returns {Array} Array of features
   */
  static async getFeaturesBySubscriptionLevel(subscriptionLevel, useCache = true) {
    const cacheKey = `features_by_level_${subscriptionLevel}`;
    
    if (useCache) {
      const cached = featureCache.get(cacheKey);
      if (cached) return cached;
    }
    
    const features = await FeatureRegistry.getFeaturesBySubscriptionLevel(subscriptionLevel);
    
    if (useCache) {
      featureCache.set(cacheKey, features);
    }
    
    return features;
  }
  
  /**
   * Get features by module and subscription level
   * @param {string} module - Module name
   * @param {string} subscriptionLevel - Subscription level
   * @param {boolean} useCache - Whether to use cache
   * @returns {Array} Array of features
   */
  static async getFeaturesByModule(module, subscriptionLevel = 'basic', useCache = true) {
    const cacheKey = `features_${module}_${subscriptionLevel}`;
    
    if (useCache) {
      const cached = featureCache.get(cacheKey);
      if (cached) return cached;
    }
    
    const features = await FeatureRegistry.getFeaturesByModule(module, subscriptionLevel);
    
    if (useCache) {
      featureCache.set(cacheKey, features);
    }
    
    return features;
  }
  
  /**
   * Check if a school has access to a specific feature
   * @param {string} featureId - Feature identifier
   * @param {string} schoolId - School identifier
   * @param {boolean} useCache - Whether to use cache
   * @returns {Object} Access check result
   */
  static async checkSchoolFeatureAccess(featureId, schoolId, useCache = true) {
    const cacheKey = `school_feature_${schoolId}_${featureId}`;
    
    if (useCache) {
      const cached = featureCache.get(cacheKey);
      if (cached) return cached;
    }
    
    try {
      // Get school subscription
      const subscription = await SchoolSubscription.findOne({ school_id: schoolId });
      
      if (!subscription) {
        return {
          hasAccess: false,
          reason: 'no_subscription',
          subscription_level: 'none'
        };
      }
      
      // Check feature access
      const accessResult = await FeatureRegistry.checkFeatureAccess(featureId, subscription.plan_type);
      
      const result = {
        ...accessResult,
        subscription_level: subscription.plan_type,
        school_id: schoolId
      };
      
      if (useCache) {
        // Cache positive results longer than negative ones
        const ttl = result.hasAccess ? 600 : 60;
        featureCache.set(cacheKey, result, ttl);
      }
      
      return result;
      
    } catch (error) {
      console.error('Error checking school feature access:', error);
      return {
        hasAccess: false,
        reason: 'error',
        error: error.message
      };
    }
  }
  
  /**
   * Get feature details by ID
   * @param {string} featureId - Feature identifier
   * @param {boolean} useCache - Whether to use cache
   * @returns {Object|null} Feature object or null
   */
  static async getFeatureById(featureId, useCache = true) {
    const cacheKey = `feature_${featureId}`;
    
    if (useCache) {
      const cached = featureCache.get(cacheKey);
      if (cached) return cached;
    }
    
    const feature = await FeatureRegistry.findOne({ 
      feature_id: featureId,
      status: 'active'
    });
    
    if (useCache && feature) {
      featureCache.set(cacheKey, feature);
    }
    
    return feature;
  }
  
  /**
   * Get all features grouped by module
   * @param {string} subscriptionLevel - Subscription level filter
   * @param {boolean} useCache - Whether to use cache
   * @returns {Object} Features grouped by module
   */
  static async getFeaturesByModuleGrouped(subscriptionLevel = 'basic', useCache = true) {
    const cacheKey = `features_grouped_${subscriptionLevel}`;
    
    if (useCache) {
      const cached = featureCache.get(cacheKey);
      if (cached) return cached;
    }
    
    const features = await this.getFeaturesBySubscriptionLevel(subscriptionLevel, false);
    
    const grouped = features.reduce((acc, feature) => {
      if (!acc[feature.module]) {
        acc[feature.module] = [];
      }
      acc[feature.module].push(feature);
      return acc;
    }, {});
    
    if (useCache) {
      featureCache.set(cacheKey, grouped);
    }
    
    return grouped;
  }
  
  /**
   * Get feature statistics
   * @param {string} subscriptionLevel - Optional subscription level filter
   * @returns {Object} Feature statistics
   */
  static async getFeatureStatistics(subscriptionLevel = null) {
    const pipeline = [];
    
    // Match active features
    pipeline.push({
      $match: {
        status: 'active',
        is_enabled_globally: true
      }
    });
    
    // Filter by subscription level if provided
    if (subscriptionLevel) {
      const levelHierarchy = {
        'basic': ['basic'],
        'standard': ['basic', 'standard'],
        'premium': ['basic', 'standard', 'premium'],
        'enterprise': ['basic', 'standard', 'premium', 'enterprise']
      };
      
      pipeline.push({
        $match: {
          subscription_level: { $in: levelHierarchy[subscriptionLevel] || ['basic'] }
        }
      });
    }
    
    // Group by various dimensions
    pipeline.push({
      $facet: {
        byModule: [
          { $group: { _id: '$module', count: { $sum: 1 } } },
          { $sort: { _id: 1 } }
        ],
        bySubscriptionLevel: [
          { $group: { _id: '$subscription_level', count: { $sum: 1 } } },
          { $sort: { _id: 1 } }
        ],
        byCategory: [
          { $group: { _id: '$category', count: { $sum: 1 } } },
          { $sort: { _id: 1 } }
        ],
        total: [
          { $count: 'total' }
        ]
      }
    });
    
    const result = await FeatureRegistry.aggregate(pipeline);
    
    return {
      total: result[0].total[0]?.total || 0,
      by_module: result[0].byModule,
      by_subscription_level: result[0].bySubscriptionLevel,
      by_category: result[0].byCategory
    };
  }
  
  /**
   * Create or update a feature
   * @param {Object} featureData - Feature data
   * @returns {Object} Created/updated feature
   */
  static async createOrUpdateFeature(featureData) {
    const existingFeature = await FeatureRegistry.findOne({ 
      feature_id: featureData.feature_id 
    });
    
    if (existingFeature) {
      // Update existing feature
      Object.assign(existingFeature, featureData);
      existingFeature.last_modified_by = featureData.last_modified_by;
      await existingFeature.save();
      
      // Clear related cache
      this.clearFeatureCache(featureData.feature_id);
      
      return existingFeature;
    } else {
      // Create new feature
      const newFeature = new FeatureRegistry(featureData);
      await newFeature.save();
      
      // Clear module cache
      this.clearModuleCache(featureData.module);
      
      return newFeature;
    }
  }
  
  /**
   * Enable or disable a feature
   * @param {string} featureId - Feature identifier
   * @param {boolean} enabled - Whether to enable the feature
   * @param {string} userId - User making the change
   * @returns {Object} Updated feature
   */
  static async toggleFeature(featureId, enabled, userId) {
    const feature = await FeatureRegistry.findOne({ feature_id: featureId });
    
    if (!feature) {
      throw new Error('Feature not found');
    }
    
    feature.status = enabled ? 'active' : 'inactive';
    feature.last_modified_by = userId;
    await feature.save();
    
    // Clear all related cache
    this.clearFeatureCache(featureId);
    this.clearModuleCache(feature.module);
    
    return feature;
  }
  
  /**
   * Clear feature-specific cache
   * @param {string} featureId - Feature identifier
   */
  static clearFeatureCache(featureId) {
    const keys = featureCache.keys();
    const featureKeys = keys.filter(key => key.includes(featureId));
    featureKeys.forEach(key => featureCache.del(key));
  }
  
  /**
   * Clear module-specific cache
   * @param {string} module - Module name
   */
  static clearModuleCache(module) {
    const keys = featureCache.keys();
    const moduleKeys = keys.filter(key => key.includes(module) || key.includes('grouped'));
    moduleKeys.forEach(key => featureCache.del(key));
  }
  
  /**
   * Clear all feature cache
   */
  static clearAllCache() {
    featureCache.flushAll();
  }
  
  /**
   * Get cache statistics
   * @returns {Object} Cache statistics
   */
  static getCacheStats() {
    return featureCache.getStats();
  }
}

module.exports = FeatureRegistryService;
