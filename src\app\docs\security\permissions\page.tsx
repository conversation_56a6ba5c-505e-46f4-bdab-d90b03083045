"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Shield,
  Users,
  Key,
  FileText,
  CheckCircle,
  ArrowRight,
  Lock,
  Eye,
  Settings,
  UserCheck,
  Database,
  AlertTriangle,
  Plus,
  Trash2
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function SecurityPermissionsPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'roles', name: 'User Roles', icon: Users },
    { id: 'permissions', name: 'Permissions', icon: Key },
    { id: 'access-control', name: 'Access Control', icon: Lock },
    { id: 'audit', name: 'Audit Logs', icon: FileText },
    { id: 'compliance', name: 'Compliance', icon: Shield },
  ];

  const features = [
    {
      title: 'Role-Based Access Control',
      description: 'Comprehensive role management with granular permissions and access control.',
      icon: Users,
      benefits: [
        'Predefined user roles (Super Admin, School Admin, Teacher, Parent)',
        'Custom role creation and management',
        'Granular permission assignments',
        'Role hierarchy and inheritance',
        'Permission inheritance rules',
        'Role-based dashboard access'
      ]
    },
    {
      title: 'Permission Management',
      description: 'Fine-grained permission system for controlling access to platform features.',
      icon: Key,
      benefits: [
        'Module-level permissions',
        'Action-based permissions (create, read, update, delete)',
        'Data-level permissions',
        'Permission inheritance',
        'Permission validation',
        'Dynamic permission checking'
      ]
    },
    {
      title: 'Access Control',
      description: 'Advanced access control mechanisms for data and feature security.',
      icon: Lock,
      benefits: [
        'Data access restrictions',
        'Feature access control',
        'IP-based access restrictions',
        'Time-based access control',
        'Session management',
        'Multi-factor authentication'
      ]
    },
    {
      title: 'Audit Logging',
      description: 'Comprehensive audit trails for security monitoring and compliance.',
      icon: FileText,
      benefits: [
        'User action logging',
        'Permission change tracking',
        'Access attempt logging',
        'Data modification audit',
        'Audit report generation',
        'Compliance reporting'
      ]
    },
    {
      title: 'Security Monitoring',
      description: 'Real-time security monitoring and threat detection.',
      icon: Shield,
      benefits: [
        'Suspicious activity detection',
        'Failed login monitoring',
        'Permission violation alerts',
        'Security incident reporting',
        'Automated security responses',
        'Security dashboard'
      ]
    },
    {
      title: 'Compliance & Governance',
      description: 'Built-in compliance features for educational data protection.',
      icon: UserCheck,
      benefits: [
        'GDPR compliance tools',
        'FERPA compliance support',
        'Data retention policies',
        'Privacy controls',
        'Consent management',
        'Compliance reporting'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Manage Roles',
      description: 'Create and configure user roles',
      icon: Users,
      action: () => console.log('Manage roles')
    },
    {
      title: 'Set Permissions',
      description: 'Configure granular permissions',
      icon: Key,
      action: () => console.log('Set permissions')
    },
    {
      title: 'View Audit Logs',
      description: 'Monitor security activities',
      icon: FileText,
      action: () => console.log('View audit logs')
    },
    {
      title: 'Security Settings',
      description: 'Configure security parameters',
      icon: Settings,
      action: () => console.log('Security settings')
    }
  ];

  const userRoles = [
    {
      role: 'Super Admin',
      description: 'Full system access and management capabilities',
      permissions: [
        'Manage all schools and users',
        'System configuration',
        'Global settings management',
        'Security administration',
        'Compliance management',
        'System monitoring'
      ]
    },
    {
      role: 'School Admin',
      description: 'School-level administration and management',
      permissions: [
        'Manage school users and students',
        'Academic data management',
        'Financial management',
        'School settings',
        'Reports and analytics',
        'Communication management'
      ]
    },
    {
      role: 'Teacher',
      description: 'Class and student management capabilities',
      permissions: [
        'Manage assigned classes',
        'Grade management',
        'Attendance tracking',
        'Student communication',
        'Class reports',
        'Resource management'
      ]
    },
    {
      role: 'Parent',
      description: 'Access to child-specific information',
      permissions: [
        'View child information',
        'Communication with teachers',
        'Payment management',
        'Attendance tracking',
        'Grade monitoring',
        'School announcements'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Security" />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <Shield className="w-12 h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Security & Permissions
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Comprehensive security and permission management for protecting your school data. 
              Advanced role-based access control with granular permissions and audit capabilities.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-4">
                            <Icon className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-4 h-4 text-teal-500 mr-2 flex-shrink-0" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
                            <Icon className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'roles' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                User Roles
              </h3>
              <div className="space-y-8">
                {userRoles.map((role, index) => (
                  <div key={index} className="border border-gray-200 dark:border-gray-700 rounded-lg p-6">
                    <div className="flex items-center mb-4">
                      <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
                        <Users className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                      </div>
                      <div>
                        <h4 className="text-lg font-semibold text-gray-900 dark:text-white">
                          {role.role}
                        </h4>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {role.description}
                        </p>
                      </div>
                    </div>
                    <ul className="space-y-2">
                      {role.permissions.map((permission, idx) => (
                        <li key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                          <CheckCircle className="w-4 h-4 text-teal-500 mr-2 flex-shrink-0" />
                          {permission}
                        </li>
                      ))}
                    </ul>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Permission System
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Permission Types
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Module permissions (students, classes, grades)</li>
                      <li>• Action permissions (create, read, update, delete)</li>
                      <li>• Data permissions (own data, all data)</li>
                      <li>• Feature permissions (reports, analytics)</li>
                      <li>• System permissions (settings, security)</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Permission Management
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Granular permission assignment</li>
                      <li>• Permission inheritance</li>
                      <li>• Permission validation</li>
                      <li>• Dynamic permission checking</li>
                      <li>• Permission audit logging</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'access-control' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Access Control
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Data Access Control
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Row-level security</li>
                      <li>• Column-level permissions</li>
                      <li>• Data masking</li>
                      <li>• Encryption at rest</li>
                      <li>• Secure data transmission</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Feature Access Control
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Feature-level permissions</li>
                      <li>• UI element visibility</li>
                      <li>• Function access control</li>
                      <li>• API endpoint protection</li>
                      <li>• Menu item restrictions</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'audit' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Audit Logging
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Audit Events
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• User login/logout events</li>
                      <li>• Permission changes</li>
                      <li>• Data modifications</li>
                      <li>• System configuration changes</li>
                      <li>• Security events</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Audit Features
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Comprehensive event logging</li>
                      <li>• Audit trail preservation</li>
                      <li>• Search and filtering</li>
                      <li>• Export capabilities</li>
                      <li>• Compliance reporting</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Compliance & Governance
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Data Protection
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• GDPR compliance tools</li>
                      <li>• FERPA compliance support</li>
                      <li>• Data retention policies</li>
                      <li>• Privacy controls</li>
                      <li>• Consent management</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Compliance Features
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Compliance reporting</li>
                      <li>• Audit trail maintenance</li>
                      <li>• Data subject rights</li>
                      <li>• Breach notification</li>
                      <li>• Compliance monitoring</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Secure Your School Data?
          </h2>
          <p className="text-xl mb-8 text-teal-100">
            Implement comprehensive security and permission management to protect your school's sensitive information.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 