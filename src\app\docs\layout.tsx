"use client";

import { useRouter } from "next/navigation";
import { useState, useEffect, useMemo } from "react";
import {
    BookOpen,
    Users,
    Shield,
    Home,
    Search,
    Menu,
    X,
    ChevronDown,
    ChevronRight,
    ArrowLeft,
    Zap,
    MessageCircle,
    Settings,
    HelpCircle,
    GraduationCap,
    <PERSON>r<PERSON><PERSON><PERSON>,
    Filter,
    Star
} from "lucide-react";
import Logo from "@/components/widgets/Logo";
import ThemeToggle from "@/components/ThemeToggle";

interface NavigationItem {
    title: string;
    icon: React.ReactNode;
    href: string;
    description: string;
    children?: NavigationItem[];
    isExpanded?: boolean;
}

export default function DocsLayout({
    children,
}: {
    children: React.ReactNode;
}) {
    const router = useRouter();
    const [sidebarOpen, setSidebarOpen] = useState(false);
    const [mounted, setMounted] = useState(false);
    const [currentPath, setCurrentPath] = useState("");
    const [sidebarSearch, setSidebarSearch] = useState("");
    const [expandedSections, setExpandedSections] = useState<string[]>([]);

    useEffect(() => {
        setMounted(true);
        if (typeof window !== "undefined") {
            setCurrentPath(window.location.pathname);
        }
    }, []);

    const navigationItems: NavigationItem[] = [
        {
            title: "Getting Started",
            icon: <BookOpen className="w-5 h-5" />,
            href: "/docs/getting-started",
            description: "Setup and configuration guide",
            children: [
                {
                    title: "School Setup",
                    icon: <Settings className="w-4 h-4" />,
                    href: "/docs/getting-started/school-setup",
                    description: "Configure your school profile"
                },
                {
                    title: "User Invitation",
                    icon: <UserCheck className="w-4 h-4" />,
                    href: "/docs/getting-started/user-invitation",
                    description: "Invite teachers, parents, students"
                }
            ]
        },
        {
            title: "User Roles & Management",
            icon: <Users className="w-5 h-5" />,
            href: "/docs/user-roles",
            description: "Managing users, roles and permissions",
            children: [
                {
                    title: "School Administrator",
                    icon: <Users className="w-4 h-4" />,
                    href: "/docs/user-roles/school-admin",
                    description: "Complete admin guide"
                },
                {
                    title: "Teacher Guide",
                    icon: <GraduationCap className="w-4 h-4" />,
                    href: "/docs/user-roles/teacher",
                    description: "Class and grade management"
                },
                {
                    title: "Parent Guide",
                    icon: <UserCheck className="w-4 h-4" />,
                    href: "/docs/user-roles/parent",
                    description: "Monitor child progress"
                },
                {
                    title: "Student Guide",
                    icon: <BookOpen className="w-4 h-4" />,
                    href: "/docs/user-roles/student",
                    description: "Access academic records"
                }
            ]
        },
        {
            title: "Features",
            icon: <Zap className="w-5 h-5" />,
            href: "/docs/features",
            description: "Core platform features",
            children: [
                {
                    title: "Academic Management",
                    icon: <BookOpen className="w-4 h-4" />,
                    href: "/docs/features/academic-management",
                    description: "Grades, attendance, timetables"
                },
                {
                    title: "Financial Management",
                    icon: <Settings className="w-4 h-4" />,
                    href: "/docs/features/financial-management",
                    description: "Fees, payments, reporting"
                },
                {
                    title: "Communication Tools",
                    icon: <MessageCircle className="w-4 h-4" />,
                    href: "/docs/features/communication",
                    description: "Announcements and messaging"
                },
                {
                    title: "Reports & Analytics",
                    icon: <BookOpen className="w-4 h-4" />,
                    href: "/docs/features/reports-analytics",
                    description: "Data insights and reporting"
                }
            ]
        },
        {
            title: "Security & Privacy",
            icon: <Shield className="w-5 h-5" />,
            href: "/docs/security",
            description: "Security features and data protection",
            children: [
                {
                    title: "User Permissions",
                    icon: <Shield className="w-4 h-4" />,
                    href: "/docs/security/permissions",
                    description: "Role-based access control"
                },
                {
                    title: "Data Security",
                    icon: <Shield className="w-4 h-4" />,
                    href: "/docs/security/data-security",
                    description: "Encryption and compliance"
                }
            ]
        },
        {
            title: "Troubleshooting",
            icon: <HelpCircle className="w-5 h-5" />,
            href: "/docs/troubleshooting",
            description: "Common issues and solutions",
            children: [
                {
                    title: "Common Issues",
                    icon: <HelpCircle className="w-4 h-4" />,
                    href: "/docs/troubleshooting/common-issues",
                    description: "Frequently asked questions"
                },
                {
                    title: "Support",
                    icon: <MessageCircle className="w-4 h-4" />,
                    href: "/docs/troubleshooting/support",
                    description: "Get help and contact support"
                }
            ]
        }
    ];

    // Filter navigation items based on search
    const filteredNavigationItems = useMemo(() => {
        if (!sidebarSearch.trim()) return navigationItems;
        
        return navigationItems.filter(item => {
            const matchesSearch = 
                item.title.toLowerCase().includes(sidebarSearch.toLowerCase()) ||
                item.description.toLowerCase().includes(sidebarSearch.toLowerCase()) ||
                item.children?.some(child => 
                    child.title.toLowerCase().includes(sidebarSearch.toLowerCase()) ||
                    child.description.toLowerCase().includes(sidebarSearch.toLowerCase())
                );
            
            return matchesSearch;
        });
    }, [navigationItems, sidebarSearch]);

    const handleBackToHome = () => {
        router.push("/");
    };

    const getCurrentPageTitle = () => {
        if (currentPath.includes("/getting-started")) return "Getting Started";
        if (currentPath.includes("/user-roles")) return "User Roles & Management";
        if (currentPath.includes("/features")) return "Features";
        if (currentPath.includes("/security")) return "Security & Privacy";
        if (currentPath.includes("/troubleshooting")) return "Troubleshooting";
        return "Documentation";
    };

    const toggleSection = (sectionTitle: string) => {
        setExpandedSections(prev => 
            prev.includes(sectionTitle) 
                ? prev.filter(item => item !== sectionTitle)
                : [...prev, sectionTitle]
        );
    };

    const isSectionExpanded = (sectionTitle: string) => {
        return expandedSections.includes(sectionTitle);
    };

    const isActive = (href: string) => {
        return currentPath === href || currentPath.startsWith(href + "/");
    };

    if (!mounted) {
        return null;
    }

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <header className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 sticky top-0 z-40">
                <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                    <div className="flex justify-between items-center h-16">
                        {/* Logo and Navigation */}
                        <div className="flex items-center space-x-4">
                            <button
                                onClick={handleBackToHome}
                                className="flex items-center space-x-2 hover:opacity-80 transition-opacity"
                            >
                                <Logo />
                            </button>
                            <div className="hidden md:block">
                                <span className="text-gray-400 dark:text-gray-500">
                                    /
                                </span>
                                <span className="ml-2 text-lg font-semibold text-gray-900 dark:text-white">
                                    {getCurrentPageTitle()}
                                </span>
                            </div>
                        </div>

                        <div className="flex items-center space-x-4">
                            {/* Back to Home Button */}
                            <button
                                onClick={handleBackToHome}
                                className="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 hover:text-teal-600 dark:hover:text-teal-300 transition-colors"
                            >
                                <Home className="w-4 h-4" />
                                <span className="hidden sm:inline">
                                    Back to Home
                                </span>
                            </button>

                            <ThemeToggle />

                            {/* Mobile menu button */}
                            <button
                                onClick={() => setSidebarOpen(!sidebarOpen)}
                                className="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 dark:hover:bg-gray-700"
                            >
                                {sidebarOpen ? (
                                    <X className="w-6 h-6" />
                                ) : (
                                    <Menu className="w-6 h-6" />
                                )}
                            </button>
                        </div>
                    </div>
                </div>
            </header>

            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
                <div className="flex gap-8">
                    {/* Enhanced Sidebar */}
                    <aside
                        className={`${
                            sidebarOpen ? "block" : "hidden"
                        } md:block w-full md:w-80 flex-shrink-0`}
                    >
                        <div className="sticky top-24 max-h-[calc(100vh-8rem)] overflow-y-auto">
                            {/* Sidebar Search */}
                            <div className="mb-6">
                                <div className="relative">
                                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                                    <input
                                        type="text"
                                        placeholder="Search documentation..."
                                        value={sidebarSearch}
                                        onChange={(e) => setSidebarSearch(e.target.value)}
                                        className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-teal-500 focus:border-teal-500 dark:bg-gray-700 dark:text-white text-sm"
                                    />
                                </div>
                                {sidebarSearch && (
                                    <button
                                        onClick={() => setSidebarSearch("")}
                                        className="mt-2 text-xs text-teal-600 dark:text-teal-300 hover:text-teal-700 dark:hover:text-teal-200"
                                    >
                                        Clear search
                                    </button>
                                )}
                            </div>

                            {/* Navigation */}
                            <nav className="space-y-1">
                                {filteredNavigationItems.map((item) => {
                                    const hasChildren = item.children && item.children.length > 0;
                                    const isExpanded = isSectionExpanded(item.title);
                                    const isActiveItem = isActive(item.href);
                                    
                                    return (
                                        <div key={item.href} className="space-y-1">
                                            <button
                                                onClick={() => {
                                                    if (hasChildren) {
                                                        toggleSection(item.title);
                                                    } else {
                                                        router.push(item.href);
                                                        setSidebarOpen(false);
                                                    }
                                                }}
                                                className={`w-full flex items-center justify-between p-3 rounded-lg transition-colors group border ${
                                                    isActiveItem
                                                        ? "bg-teal-50 dark:bg-teal-900/20 border-teal-200 dark:border-teal-800 shadow-sm"
                                                        : "border-transparent hover:bg-white dark:hover:bg-gray-800 hover:border-gray-200 dark:hover:border-gray-700 hover:shadow-sm"
                                                }`}
                                            >
                                                <div className="flex items-center space-x-3">
                                                    <div
                                                        className={`flex-shrink-0 ${
                                                            isActiveItem
                                                                ? "text-teal-600 dark:text-teal-300"
                                                                : "text-gray-500 dark:text-gray-400 group-hover:text-teal-600 dark:group-hover:text-teal-300"
                                                        }`}
                                                    >
                                                        {item.icon}
                                                    </div>
                                                    <div className="text-left">
                                                        <div
                                                            className={`text-sm font-medium ${
                                                                isActiveItem
                                                                    ? "text-teal-600 dark:text-teal-300"
                                                                    : "text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-300"
                                                            }`}
                                                        >
                                                            {item.title}
                                                        </div>
                                                        <div
                                                            className={`text-xs mt-1 ${
                                                                isActiveItem
                                                                    ? "text-teal-500 dark:text-teal-400"
                                                                    : "text-gray-500 dark:text-gray-400"
                                                            }`}
                                                        >
                                                            {item.description}
                                                        </div>
                                                    </div>
                                                </div>
                                                {hasChildren && (
                                                    <ChevronDown 
                                                        className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                                                            isExpanded ? "rotate-180" : ""
                                                        }`}
                                                    />
                                                )}
                                            </button>
                                            
                                            {/* Children */}
                                            {hasChildren && isExpanded && (
                                                <div className="ml-6 space-y-1">
                                                    {item.children?.map((child) => {
                                                        const isChildActive = isActive(child.href);
                                                        return (
                                                            <button
                                                                key={child.href}
                                                                onClick={() => {
                                                                    router.push(child.href);
                                                                    setSidebarOpen(false);
                                                                }}
                                                                className={`w-full flex items-center space-x-3 p-2 rounded-md transition-colors group ${
                                                                    isChildActive
                                                                        ? "bg-teal-100 dark:bg-teal-900/30 text-teal-700 dark:text-teal-200"
                                                                        : "text-gray-600 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-700"
                                                                }`}
                                                            >
                                                                <div className="flex-shrink-0">
                                                                    {child.icon}
                                                                </div>
                                                                <div className="text-left">
                                                                    <div className="text-sm font-medium">
                                                                        {child.title}
                                                                    </div>
                                                                    <div className="text-xs mt-1 opacity-75">
                                                                        {child.description}
                                                                    </div>
                                                                </div>
                                                            </button>
                                                        );
                                                    })}
                                                </div>
                                            )}
                                        </div>
                                    );
                                })}
                            </nav>

                            {/* Quick Links */}
                            <div className="mt-8 p-4 bg-teal-50 dark:bg-teal-900/20 rounded-lg border border-teal-200 dark:border-teal-800">
                                <h3 className="text-sm font-semibold text-teal-900 dark:text-teal-100 mb-3 flex items-center">
                                    <Star className="w-4 h-4 mr-2" />
                                    Quick Links
                                </h3>
                                <div className="space-y-2 text-sm">
                                    <a
                                        href="/contact"
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                                    >
                                        Contact Support
                                    </a>
                                    <a
                                        href="/docs/troubleshooting/common-issues"
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                                    >
                                        Common Issues
                                    </a>
                                    <a
                                        href="/docs/getting-started"
                                        className="block text-teal-700 dark:text-teal-300 hover:text-teal-800 dark:hover:text-teal-200 transition-colors"
                                    >
                                        Getting Started
                                    </a>
                                </div>
                            </div>
                        </div>
                    </aside>

                    {/* Main Content */}
                    <main className="flex-1 min-w-0">
                        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
                            {children}
                        </div>
                    </main>
                </div>
            </div>

            {/* Mobile sidebar overlay */}
            {sidebarOpen && (
                <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-30 md:hidden"
                    onClick={() => setSidebarOpen(false)}
                />
            )}
        </div>
    );
}
