"use client";

import { useState } from 'react';
import { Users, GraduationCap, Settings, BarChart3, Shield, ArrowRight, CheckCircle, AlertCircle, BookOpen, School } from 'lucide-react';
import Link from 'next/link';

const capabilities = [
  {
    category: 'School Management',
    icon: School,
    description: 'Manage your school\'s profile, settings, and configuration',
    features: [
      'Update school information and branding',
      'Configure academic calendar and terms',
      'Manage school policies and guidelines',
      'Set up class levels and subjects',
      'Configure grading scales and assessment criteria'
    ]
  },
  {
    category: 'Staff Management',
    icon: Users,
    description: 'Manage teachers, administrators, and support staff',
    features: [
      'Create and manage teacher accounts',
      'Assign teachers to classes and subjects',
      'Set staff permissions and access levels',
      'Monitor staff performance and activity',
      'Manage staff schedules and assignments'
    ]
  },
  {
    category: 'Student Management',
    icon: GraduationCap,
    description: 'Manage student enrollment, records, and academic progress',
    features: [
      'Enroll new students and manage records',
      'Assign students to classes and sections',
      'Monitor academic performance and attendance',
      'Generate student reports and transcripts',
      'Manage student disciplinary actions'
    ]
  },
  {
    category: 'Academic Administration',
    icon: BookOpen,
    description: 'Oversee academic operations and curriculum management',
    features: [
      'Manage class schedules and timetables',
      'Configure grading systems and assessments',
      'Monitor academic performance metrics',
      'Manage exam schedules and supervision',
      'Oversee curriculum implementation'
    ]
  },
  {
    category: 'Financial Management',
    icon: BarChart3,
    description: 'Manage school finances, fees, and financial reporting',
    features: [
      'Configure fee structures and payment plans',
      'Monitor fee collections and outstanding balances',
      'Generate financial reports and statements',
      'Manage scholarships and financial aid',
      'Track school expenses and budgets'
    ]
  },
  {
    category: 'Communication & Reporting',
    icon: Shield,
    description: 'Facilitate communication and generate comprehensive reports',
    features: [
      'Send announcements to parents and staff',
      'Generate academic and attendance reports',
      'Monitor parent engagement and communication',
      'Create custom reports and analytics',
      'Manage school-wide notifications'
    ]
  }
];

const permissions = [
  {
    module: 'Students',
    permissions: [
      'view_all_students',
      'add_edit_delete_students',
      'generate_id_cards',
      'generate_report_cards',
      'manage_student_records'
    ]
  },
  {
    module: 'Academic Records',
    permissions: [
      'view_all_school_grades',
      'manage_terms',
      'manage_timetables',
      'manage_periods',
      'manage_subjects',
      'manage_classes',
      'manage_exam_types',
      'manage_discipline'
    ]
  },
  {
    module: 'Staff',
    permissions: [
      'view_staff_list',
      'add_edit_delete_staff',
      'manage_staff_permissions',
      'reset_staff_passwords',
      'manage_teacher_assignments'
    ]
  },
  {
    module: 'Financials',
    permissions: [
      'view_student_fee_balances',
      'record_fee_payments',
      'manage_school_credit_balance',
      'view_financial_reports',
      'manage_fee_types',
      'view_transactions'
    ]
  },
  {
    module: 'Classes',
    permissions: [
      'view_all_classes',
      'add_edit_delete_classes',
      'manage_class_schedules',
      'assign_teachers_to_classes'
    ]
  },
  {
    module: 'Announcements',
    permissions: [
      'view_announcements',
      'create_edit_announcements',
      'delete_announcements',
      'publish_announcements'
    ]
  },
  {
    module: 'Resources',
    permissions: [
      'view_resources',
      'add_edit_delete_resources',
      'manage_resource_categories'
    ]
  },
  {
    module: 'Reports',
    permissions: [
      'generate_student_reports',
      'view_attendance_reports',
      'generate_academic_reports',
      'export_data'
    ]
  }
];

const quickActions = [
  {
    title: 'Manage Students',
    description: 'Add, edit, and manage student records',
    href: '/school-admin/students',
    icon: GraduationCap
  },
  {
    title: 'Staff Management',
    description: 'Manage teachers and staff accounts',
    href: '/school-admin/staff',
    icon: Users
  },
  {
    title: 'Academic Setup',
    description: 'Configure classes, subjects, and schedules',
    href: '/school-admin/academics',
    icon: BookOpen
  },
  {
    title: 'Financial Reports',
    description: 'View financial reports and fee management',
    href: '/school-admin/finance',
    icon: BarChart3
  }
];

export default function SchoolAdminGuide() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <Users className="h-8 w-8 text-blue-600 mr-3" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">School Administrator Guide</h1>
              <p className="text-xl text-gray-600">Complete guide for managing your school and its operations</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Role Overview */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-blue-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Role Overview</h2>
              <p className="text-gray-700 mb-4">
                School Administrators have comprehensive control over their school's operations, including 
                staff management, student enrollment, academic administration, and financial oversight. 
                This role is typically assigned to principals, school directors, or administrative staff.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Access Level</div>
                  <div className="text-blue-600 font-medium">School-Wide Access</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Schools Managed</div>
                  <div className="text-blue-600 font-medium">Single School</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">User Management</div>
                  <div className="text-blue-600 font-medium">School Users Only</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'overview', name: 'Overview', icon: BookOpen },
              { id: 'capabilities', name: 'Capabilities', icon: Shield },
              { id: 'permissions', name: 'Permissions', icon: Settings },
              { id: 'quick-actions', name: 'Quick Actions', icon: ArrowRight }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200">
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">School Administrator Overview</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Responsibilities</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Manage school profile and configuration</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Oversee staff hiring and management</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Monitor student enrollment and academic progress</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Manage school finances and fee collection</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Generate reports and analytics</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Areas</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>School dashboard and analytics</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>All student and staff records</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Academic management tools</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Financial management and reporting</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Communication and announcement tools</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'capabilities' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">School Administrator Capabilities</h2>
              
              <div className="space-y-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon;
                  
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start mb-4">
                        <div className="p-2 bg-blue-50 rounded-lg mr-4">
                          <Icon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{capability.category}</h3>
                          <p className="text-gray-600">{capability.description}</p>
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {capability.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Permissions</h2>
              
              <div className="space-y-6">
                {permissions.map((module, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{module.module}</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {module.permissions.map((permission, permIndex) => (
                        <div key={permIndex} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 font-mono bg-gray-100 px-2 py-1 rounded">
                            {permission}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'quick-actions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  
                  return (
                    <Link
                      key={index}
                      href={action.href}
                      className="group border border-gray-200 rounded-lg p-6 hover:border-blue-300 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          <div className="p-2 bg-blue-50 rounded-lg mr-4">
                            <Icon className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 group-hover:text-blue-600 transition-colors">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-blue-600 transition-colors" />
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Best Practices */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Practices</h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Regularly review staff permissions and access levels</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Monitor student performance metrics and attendance patterns</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Generate monthly financial reports to track school revenue</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Use announcements to keep parents and staff informed</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Documentation */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Documentation</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <Link href="/docs/user-roles/teacher" className="text-blue-600 hover:text-blue-800 text-sm">
              → Teacher Guide
            </Link>
            <Link href="/docs/user-roles/parent" className="text-blue-600 hover:text-blue-800 text-sm">
              → Parent Guide
            </Link>
            <Link href="/docs/features/academic-management" className="text-blue-600 hover:text-blue-800 text-sm">
              → Academic Management
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 