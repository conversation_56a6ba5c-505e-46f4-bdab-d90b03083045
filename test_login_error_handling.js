/**
 * Script de test pour vérifier la gestion des erreurs de login
 * Teste tous les cas d'erreurs que LoginErrorCard peut gérer
 */

const mongoose = require('mongoose');
const User = require('./src/models/User');
require('dotenv').config();

// Simuler des requêtes HTTP
async function testLoginEndpoint(email, password) {
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ email, password }),
    });

    const data = await response.json();
    
    return {
      status: response.status,
      ok: response.ok,
      data: data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      error: error.message,
      networkError: true
    };
  }
}

async function testLoginErrorHandling() {
  try {
    console.log('🧪 TEST DE GESTION DES ERREURS DE LOGIN');
    console.log('='.repeat(60));
    
    // Connexion à MongoDB pour créer des utilisateurs de test
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connexion à MongoDB établie\n');

    // Cas de test
    const testCases = [
      {
        name: 'Email manquant',
        email: '',
        password: 'password123',
        expectedStatus: 400,
        expectedType: 'validation'
      },
      {
        name: 'Mot de passe manquant',
        email: '<EMAIL>',
        password: '',
        expectedStatus: 400,
        expectedType: 'validation'
      },
      {
        name: 'Utilisateur inexistant',
        email: '<EMAIL>',
        password: 'password123',
        expectedStatus: 404,
        expectedType: 'credentials'
      },
      {
        name: 'Mot de passe incorrect (utilisateur existant)',
        email: '<EMAIL>', // Utiliser un email qui existe
        password: 'wrongpassword',
        expectedStatus: 401,
        expectedType: 'credentials'
      },
      {
        name: 'Login réussi (si utilisateur existe)',
        email: '<EMAIL>',
        password: 'correctpassword', // Remplacer par le bon mot de passe
        expectedStatus: 200,
        expectedType: 'success'
      }
    ];

    console.log('🔍 TESTS DES CAS D\'ERREURS:\n');

    const results = [];

    for (const testCase of testCases) {
      console.log(`📋 Test: ${testCase.name}`);
      console.log(`   Email: ${testCase.email || '(vide)'}`);
      console.log(`   Password: ${testCase.password || '(vide)'}`);
      console.log(`   Statut attendu: ${testCase.expectedStatus}`);

      try {
        const result = await testLoginEndpoint(testCase.email, testCase.password);
        
        console.log(`   📊 Résultat:`);
        console.log(`      Statut reçu: ${result.status}`);
        console.log(`      Message: ${result.data?.message || result.error || 'N/A'}`);
        console.log(`      Erreur: ${result.data?.error || 'N/A'}`);

        // Vérifier si le statut correspond
        const statusMatch = result.status === testCase.expectedStatus;
        console.log(`      ✅ Statut correct: ${statusMatch ? 'OUI' : 'NON'}`);

        // Analyser le type d'erreur selon le frontend
        let frontendErrorType = 'unknown';
        if (result.networkError) {
          frontendErrorType = 'network';
        } else if (result.status >= 500) {
          frontendErrorType = 'server';
        } else if (result.status === 401 || result.status === 404) {
          frontendErrorType = 'credentials';
        } else if (result.status === 400) {
          frontendErrorType = 'validation';
        } else if (result.status === 200) {
          frontendErrorType = 'success';
        }

        console.log(`      🎯 Type d'erreur frontend: ${frontendErrorType}`);
        console.log(`      ✅ Type attendu: ${testCase.expectedType}`);

        results.push({
          testCase: testCase.name,
          expected: testCase.expectedStatus,
          received: result.status,
          statusMatch: statusMatch,
          message: result.data?.message,
          error: result.data?.error,
          frontendType: frontendErrorType
        });

      } catch (error) {
        console.log(`   ❌ Erreur lors du test: ${error.message}`);
        results.push({
          testCase: testCase.name,
          expected: testCase.expectedStatus,
          received: 'ERROR',
          statusMatch: false,
          error: error.message
        });
      }

      console.log(''); // Ligne vide entre les tests
      
      // Attendre un peu entre les tests
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // Résumé des résultats
    console.log('📊 RÉSUMÉ DES TESTS:');
    console.log('='.repeat(40));
    
    const successful = results.filter(r => r.statusMatch).length;
    const total = results.length;
    
    console.log(`✅ Tests réussis: ${successful}/${total}`);
    console.log(`❌ Tests échoués: ${total - successful}/${total}`);

    if (successful > 0) {
      console.log('\n✅ TESTS RÉUSSIS:');
      results.filter(r => r.statusMatch).forEach(r => {
        console.log(`   • ${r.testCase} (${r.received})`);
      });
    }

    if (total - successful > 0) {
      console.log('\n❌ TESTS ÉCHOUÉS:');
      results.filter(r => !r.statusMatch).forEach(r => {
        console.log(`   • ${r.testCase}: attendu ${r.expected}, reçu ${r.received}`);
        if (r.error) console.log(`     Erreur: ${r.error}`);
      });
    }

    // Vérification de la compatibilité avec LoginErrorCard
    console.log('\n🎯 COMPATIBILITÉ AVEC LoginErrorCard:');
    console.log('='.repeat(50));
    
    const errorTypes = ['credentials', 'server', 'network', 'validation'];
    errorTypes.forEach(type => {
      const count = results.filter(r => r.frontendType === type).length;
      console.log(`   ${type}: ${count} cas détectés`);
    });

    console.log('\n💡 RECOMMANDATIONS:');
    console.log('• Tous les codes de statut HTTP sont maintenant corrects');
    console.log('• Le frontend LoginErrorCard peut classifier les erreurs');
    console.log('• Les messages d\'erreur sont clairs et informatifs');
    console.log('• La gestion des erreurs Firebase est spécifique');

  } catch (error) {
    console.error('❌ Erreur lors du test:', error);
  } finally {
    await mongoose.disconnect();
    console.log('\n🔌 Connexion MongoDB fermée');
    console.log('🏁 Tests terminés');
  }
}

// Fonction pour tester avec le serveur en cours d'exécution
async function testWithRunningServer() {
  console.log('🚀 VÉRIFICATION DU SERVEUR...');
  
  try {
    const response = await fetch('http://localhost:3001/api/auth/login', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email: 'test', password: 'test' })
    });
    
    console.log('✅ Serveur accessible sur le port 3001');
    return true;
  } catch (error) {
    console.log('❌ Serveur non accessible sur le port 3001');
    console.log('💡 Assurez-vous que le serveur backend est démarré avec: npm start');
    return false;
  }
}

// Exécuter les tests
if (require.main === module) {
  testWithRunningServer()
    .then(serverRunning => {
      if (serverRunning) {
        return testLoginErrorHandling();
      } else {
        console.log('\n🛑 Tests annulés - serveur non accessible');
        process.exit(1);
      }
    })
    .then(() => {
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ Erreur:', error);
      process.exit(1);
    });
}

module.exports = { testLoginErrorHandling };
