"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Plus, 
  Search, 
  Filter, 
  BarChart3, 
  Shield, 
  Zap, 
  Crown,
  Star,
  Eye,
  Edit,
  Trash2,
  RefreshCw,
  Download,
  Upload
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import FeatureRegistryServices, { FeatureInfo, FeatureStatistics } from '@/app/services/FeatureRegistryServices';
import FeatureCard from './components/FeatureCard';
import FeatureModal from './components/FeatureModal';
import FeatureStatsDashboard from './components/FeatureStatsDashboard';
import BulkActionsPanel from './components/BulkActionsPanel';

interface FilterState {
  module: string;
  subscriptionLevel: string;
  status: string;
  category: string;
  searchTerm: string;
}

/**
 * Page de gestion des fonctionnalités pour les Super Admins
 */
const FeatureManagementPage: React.FC = () => {
  const { t } = useTranslation();
  
  // États principaux
  const [features, setFeatures] = useState<FeatureInfo[]>([]);
  const [statistics, setStatistics] = useState<FeatureStatistics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // États de l'interface
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [showModal, setShowModal] = useState(false);
  const [editingFeature, setEditingFeature] = useState<FeatureInfo | null>(null);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [showStats, setShowStats] = useState(false);
  
  // États de filtrage
  const [filters, setFilters] = useState<FilterState>({
    module: '',
    subscriptionLevel: '',
    status: '',
    category: '',
    searchTerm: ''
  });

  // Charger les données initiales
  useEffect(() => {
    loadFeatures();
    loadStatistics();
  }, []);

  const loadFeatures = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Charger toutes les fonctionnalités groupées par module
      const groupedFeatures = await FeatureRegistryServices.getFeaturesByModuleGrouped('basic', false);
      
      // Aplatir les fonctionnalités groupées
      const allFeatures = Object.values(groupedFeatures).flat();
      setFeatures(allFeatures);
    } catch (err) {
      console.error('Erreur lors du chargement des fonctionnalités:', err);
      setError('Impossible de charger les fonctionnalités');
    } finally {
      setLoading(false);
    }
  };

  const loadStatistics = async () => {
    try {
      const stats = await FeatureRegistryServices.getFeatureStatistics();
      setStatistics(stats);
    } catch (err) {
      console.error('Erreur lors du chargement des statistiques:', err);
    }
  };

  // Filtrer les fonctionnalités
  const filteredFeatures = features.filter(feature => {
    const matchesSearch = !filters.searchTerm || 
      feature.name.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      feature.description.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
      feature.feature_id.toLowerCase().includes(filters.searchTerm.toLowerCase());
    
    const matchesModule = !filters.module || feature.module === filters.module;
    const matchesLevel = !filters.subscriptionLevel || feature.subscription_level === filters.subscriptionLevel;
    const matchesStatus = !filters.status || feature.status === filters.status;
    const matchesCategory = !filters.category || feature.category === filters.category;
    
    return matchesSearch && matchesModule && matchesLevel && matchesStatus && matchesCategory;
  });

  // Gestionnaires d'événements
  const handleCreateFeature = () => {
    setEditingFeature(null);
    setShowModal(true);
  };

  const handleEditFeature = (feature: FeatureInfo) => {
    setEditingFeature(feature);
    setShowModal(true);
  };

  const handleDeleteFeature = async (featureId: string) => {
    if (!confirm('Êtes-vous sûr de vouloir supprimer cette fonctionnalité ?')) {
      return;
    }

    try {
      // Ici, vous ajouteriez la logique de suppression
      console.log('Suppression de la fonctionnalité:', featureId);
      await loadFeatures(); // Recharger après suppression
    } catch (err) {
      console.error('Erreur lors de la suppression:', err);
    }
  };

  const handleFeatureSaved = () => {
    setShowModal(false);
    setEditingFeature(null);
    loadFeatures();
    loadStatistics();
  };

  const handleBulkAction = async (action: string, featureIds: string[]) => {
    try {
      switch (action) {
        case 'enable':
          // Activer les fonctionnalités sélectionnées
          break;
        case 'disable':
          // Désactiver les fonctionnalités sélectionnées
          break;
        case 'delete':
          if (confirm(`Supprimer ${featureIds.length} fonctionnalité(s) ?`)) {
            // Supprimer les fonctionnalités sélectionnées
          }
          break;
      }
      
      setSelectedFeatures([]);
      await loadFeatures();
    } catch (err) {
      console.error('Erreur lors de l\'action en lot:', err);
    }
  };

  const handleClearCache = async () => {
    try {
      await FeatureRegistryServices.clearCache();
      alert('Cache vidé avec succès');
    } catch (err) {
      console.error('Erreur lors du vidage du cache:', err);
      alert('Erreur lors du vidage du cache');
    }
  };

  const getModules = () => {
    const modules = [...new Set(features.map(f => f.module))];
    return modules.sort();
  };

  const getSubscriptionLevels = () => {
    const levels = [...new Set(features.map(f => f.subscription_level))];
    return levels.sort();
  };

  const getCategories = () => {
    const categories = [...new Set(features.map(f => f.category))];
    return categories.sort();
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900 dark:text-gray-100 flex items-center">
                <Settings className="w-8 h-8 mr-3 text-blue-600" />
                {t('feature_management.title', 'Gestion des Fonctionnalités')}
              </h1>
              <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">
                {t('feature_management.subtitle', 'Configurez et gérez les fonctionnalités de l\'application')}
              </p>
            </div>
            
            <div className="flex items-center space-x-3">
              <button
                onClick={() => setShowStats(!showStats)}
                className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 transition-colors flex items-center"
              >
                <BarChart3 className="w-4 h-4 mr-2" />
                Statistiques
              </button>
              
              <button
                onClick={handleClearCache}
                className="px-4 py-2 bg-yellow-600 text-white rounded-lg hover:bg-yellow-700 transition-colors flex items-center"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Vider le cache
              </button>
              
              <button
                onClick={handleCreateFeature}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center"
              >
                <Plus className="w-4 h-4 mr-2" />
                Nouvelle fonctionnalité
              </button>
            </div>
          </div>
        </div>
      </div>

      {/* Statistiques */}
      {showStats && statistics && (
        <motion.div
          initial={{ opacity: 0, height: 0 }}
          animate={{ opacity: 1, height: 'auto' }}
          exit={{ opacity: 0, height: 0 }}
          className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700"
        >
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
            <FeatureStatsDashboard statistics={statistics} />
          </div>
        </motion.div>
      )}

      {/* Contenu principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Filtres et recherche */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
            {/* Recherche */}
            <div className="lg:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Rechercher une fonctionnalité..."
                  value={filters.searchTerm}
                  onChange={(e) => setFilters(prev => ({ ...prev, searchTerm: e.target.value }))}
                  className="w-full pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
                />
              </div>
            </div>

            {/* Filtres */}
            <select
              value={filters.module}
              onChange={(e) => setFilters(prev => ({ ...prev, module: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">Tous les modules</option>
              {getModules().map(module => (
                <option key={module} value={module}>{module}</option>
              ))}
            </select>

            <select
              value={filters.subscriptionLevel}
              onChange={(e) => setFilters(prev => ({ ...prev, subscriptionLevel: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">Tous les niveaux</option>
              {getSubscriptionLevels().map(level => (
                <option key={level} value={level}>{t(`subscription.plans.${level}`, level)}</option>
              ))}
            </select>

            <select
              value={filters.status}
              onChange={(e) => setFilters(prev => ({ ...prev, status: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">Tous les statuts</option>
              <option value="active">Actif</option>
              <option value="inactive">Inactif</option>
            </select>

            <select
              value={filters.category}
              onChange={(e) => setFilters(prev => ({ ...prev, category: e.target.value }))}
              className="px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent dark:bg-gray-700 dark:text-gray-100"
            >
              <option value="">Toutes les catégories</option>
              {getCategories().map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>
        </div>

        {/* Actions en lot */}
        {selectedFeatures.length > 0 && (
          <BulkActionsPanel
            selectedCount={selectedFeatures.length}
            onAction={handleBulkAction}
            selectedFeatures={selectedFeatures}
          />
        )}

        {/* Liste des fonctionnalités */}
        {error ? (
          <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-4 text-red-700 dark:text-red-400">
            {error}
          </div>
        ) : (
          <div className={viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4'}>
            {filteredFeatures.map(feature => (
              <FeatureCard
                key={feature.feature_id}
                feature={feature}
                isSelected={selectedFeatures.includes(feature.feature_id)}
                onSelect={(selected) => {
                  if (selected) {
                    setSelectedFeatures(prev => [...prev, feature.feature_id]);
                  } else {
                    setSelectedFeatures(prev => prev.filter(id => id !== feature.feature_id));
                  }
                }}
                onEdit={() => handleEditFeature(feature)}
                onDelete={() => handleDeleteFeature(feature.feature_id)}
                viewMode={viewMode}
              />
            ))}
          </div>
        )}

        {filteredFeatures.length === 0 && !loading && (
          <div className="text-center py-12">
            <Shield className="w-12 h-12 text-gray-400 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-gray-100 mb-2">
              Aucune fonctionnalité trouvée
            </h3>
            <p className="text-gray-500 dark:text-gray-400">
              Essayez de modifier vos filtres ou créez une nouvelle fonctionnalité.
            </p>
          </div>
        )}
      </div>

      {/* Modal de création/édition */}
      {showModal && (
        <FeatureModal
          feature={editingFeature}
          onSave={handleFeatureSaved}
          onClose={() => setShowModal(false)}
        />
      )}
    </div>
  );
};

export default FeatureManagementPage;
