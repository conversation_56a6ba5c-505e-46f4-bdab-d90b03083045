"use client";

import { useState } from 'react';
import { GraduationCap, BookOpen, FileCheck2, Percent, Clock, MessageSquare, ArrowRight, CheckCircle, AlertCircle, Users } from 'lucide-react';
import Link from 'next/link';

const capabilities = [
  {
    category: 'Class Management',
    icon: BookOpen,
    description: 'Manage your assigned classes and teaching schedule',
    features: [
      'View assigned classes and student lists',
      'Access class schedules and timetables',
      'Manage class materials and resources',
      'Track class attendance and participation',
      'Organize class activities and assignments'
    ]
  },
  {
    category: 'Student Assessment',
    icon: Percent,
    description: 'Grade students and track academic progress',
    features: [
      'Enter and update student grades',
      'Create and grade assignments',
      'Track student performance over time',
      'Generate progress reports',
      'Provide feedback on student work'
    ]
  },
  {
    category: 'Attendance Management',
    icon: FileCheck2,
    description: 'Monitor and record student attendance',
    features: [
      'Take daily attendance for your classes',
      'View attendance history and patterns',
      'Generate attendance reports',
      'Mark students as present, absent, or late',
      'Track attendance trends and issues'
    ]
  },
  {
    category: 'Academic Planning',
    icon: Clock,
    description: 'Plan lessons and manage academic content',
    features: [
      'Create lesson plans and schedules',
      'Upload and share educational resources',
      'Plan assignments and assessments',
      'Track curriculum coverage',
      'Organize teaching materials'
    ]
  },
  {
    category: 'Student Support',
    icon: Users,
    description: 'Support individual student needs and development',
    features: [
      'Monitor individual student progress',
      'Identify students needing additional support',
      'Provide personalized feedback',
      'Track student goals and achievements',
      'Collaborate with other teachers on student needs'
    ]
  }
];

const permissions = [
  {
    module: 'Classes',
    permissions: [
      'view_all_classes',
      'manage_class_schedules',
      'view_class_materials',
      'organize_class_activities'
    ]
  },
  {
    module: 'Students',
    permissions: [
      'view_all_students',
      'track_student_progress',
      'view_student_records',
      'provide_student_feedback'
    ]
  },
  {
    module: 'Academic Records',
    permissions: [
      'view_grades_assigned_classes',
      'enter_edit_grades_assigned_classes',
      'take_attendance_assigned_classes',
      'view_attendance_reports'
    ]
  },
  {
    module: 'Resources',
    permissions: [
      'view_resources',
      'upload_teaching_materials',
      'share_educational_content',
      'organize_lesson_plans'
    ]
  },
  {
    module: 'Reports',
    permissions: [
      'generate_student_reports',
      'view_class_performance',
      'track_attendance_metrics',
      'export_grade_data'
    ]
  }
];

const quickActions = [
  {
    title: 'My Classes',
    description: 'View and manage your assigned classes',
    href: '/teacher-dashboard/classes',
    icon: BookOpen
  },
  {
    title: 'Take Attendance',
    description: 'Record daily attendance for your classes',
    href: '/teacher-dashboard/attendance',
    icon: FileCheck2
  },
  {
    title: 'Enter Grades',
    description: 'Grade students and update academic records',
    href: '/teacher-dashboard/grades',
    icon: Percent
  },
  {
    title: 'View Schedule',
    description: 'Check your teaching schedule and timetables',
    href: '/teacher-dashboard/timetable',
    icon: Clock
  }
];

export default function TeacherGuide() {
  const [activeTab, setActiveTab] = useState('overview');

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white border-b border-gray-200">
        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="flex items-center mb-4">
            <GraduationCap className="h-8 w-8 text-green-600 mr-3" />
            <div>
              <h1 className="text-4xl font-bold text-gray-900">Teacher Guide</h1>
              <p className="text-xl text-gray-600">Complete guide for managing classes, grades, and student communication</p>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Role Overview */}
        <div className="bg-gradient-to-r from-green-50 to-emerald-50 rounded-xl p-6 mb-8">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-green-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Role Overview</h2>
              <p className="text-gray-700 mb-4">
                Teachers are responsible for managing their assigned classes, tracking student progress, 
                entering grades, taking attendance, and communicating with parents. This role focuses on 
                academic delivery and student development within the classroom environment.
              </p>
              <div className="grid md:grid-cols-3 gap-4 text-sm">
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Access Level</div>
                  <div className="text-green-600 font-medium">Class-Level Access</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Classes Managed</div>
                  <div className="text-green-600 font-medium">Assigned Classes Only</div>
                </div>
                <div className="bg-white rounded-lg p-3">
                  <div className="font-semibold text-gray-900">Student Access</div>
                  <div className="text-green-600 font-medium">Class Students Only</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Navigation Tabs */}
        <div className="mb-8">
          <nav className="flex space-x-8 border-b border-gray-200">
            {[
              { id: 'overview', name: 'Overview', icon: BookOpen },
              { id: 'capabilities', name: 'Capabilities', icon: GraduationCap },
              { id: 'permissions', name: 'Permissions', icon: FileCheck2 },
              { id: 'quick-actions', name: 'Quick Actions', icon: ArrowRight }
            ].map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center py-2 px-1 border-b-2 font-medium text-sm ${
                    activeTab === tab.id
                      ? 'border-green-500 text-green-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <Icon className="h-4 w-4 mr-2" />
                  {tab.name}
                </button>
              );
            })}
          </nav>
        </div>

        {/* Tab Content */}
        <div className="bg-white rounded-lg border border-gray-200">
          {activeTab === 'overview' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Teacher Overview</h2>
              
              <div className="grid md:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Responsibilities</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Manage assigned classes and student lists</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Take daily attendance for all classes</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Enter and update student grades regularly</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Create and share educational resources</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Track student progress and performance</span>
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Access Areas</h3>
                  <ul className="space-y-3">
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Personal teacher dashboard</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Assigned classes and student records</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Grade entry and assessment tools</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Attendance tracking and reporting</span>
                    </li>
                    <li className="flex items-start">
                      <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                      <span>Lesson planning and resource management</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'capabilities' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Teacher Capabilities</h2>
              
              <div className="space-y-6">
                {capabilities.map((capability, index) => {
                  const Icon = capability.icon;
                  
                  return (
                    <div key={index} className="border border-gray-200 rounded-lg p-6">
                      <div className="flex items-start mb-4">
                        <div className="p-2 bg-green-50 rounded-lg mr-4">
                          <Icon className="h-6 w-6 text-green-600" />
                        </div>
                        <div>
                          <h3 className="text-lg font-semibold text-gray-900">{capability.category}</h3>
                          <p className="text-gray-600">{capability.description}</p>
                        </div>
                      </div>
                      
                      <ul className="space-y-2">
                        {capability.features.map((feature, featureIndex) => (
                          <li key={featureIndex} className="flex items-start">
                            <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                            <span className="text-sm text-gray-700">{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  );
                })}
              </div>
            </div>
          )}

          {activeTab === 'permissions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Detailed Permissions</h2>
              
              <div className="space-y-6">
                {permissions.map((module, index) => (
                  <div key={index} className="border border-gray-200 rounded-lg p-6">
                    <h3 className="text-lg font-semibold text-gray-900 mb-4">{module.module}</h3>
                    <div className="grid md:grid-cols-2 gap-4">
                      {module.permissions.map((permission, permIndex) => (
                        <div key={permIndex} className="flex items-center">
                          <CheckCircle className="h-4 w-4 text-green-500 mr-2 flex-shrink-0" />
                          <span className="text-sm text-gray-700 font-mono bg-gray-100 px-2 py-1 rounded">
                            {permission}
                          </span>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {activeTab === 'quick-actions' && (
            <div className="p-6">
              <h2 className="text-2xl font-bold text-gray-900 mb-6">Quick Actions</h2>
              
              <div className="grid md:grid-cols-2 gap-6">
                {quickActions.map((action, index) => {
                  const Icon = action.icon;
                  
                  return (
                    <Link
                      key={index}
                      href={action.href}
                      className="group border border-gray-200 rounded-lg p-6 hover:border-green-300 hover:shadow-md transition-all duration-200"
                    >
                      <div className="flex items-start justify-between">
                        <div className="flex items-center">
                          <div className="p-2 bg-green-50 rounded-lg mr-4">
                            <Icon className="h-6 w-6 text-green-600" />
                          </div>
                          <div>
                            <h3 className="font-semibold text-gray-900 group-hover:text-green-600 transition-colors">
                              {action.title}
                            </h3>
                            <p className="text-sm text-gray-600 mt-1">{action.description}</p>
                          </div>
                        </div>
                        <ArrowRight className="h-5 w-5 text-gray-400 group-hover:text-green-600 transition-colors" />
                      </div>
                    </Link>
                  );
                })}
              </div>
            </div>
          )}
        </div>

        {/* Best Practices */}
        <div className="mt-8 bg-yellow-50 border border-yellow-200 rounded-xl p-6">
          <div className="flex items-start">
            <AlertCircle className="h-6 w-6 text-yellow-600 mr-3 mt-0.5 flex-shrink-0" />
            <div>
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Best Practices</h3>
              <ul className="space-y-2 text-sm text-gray-700">
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Take attendance daily and update grades regularly</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Communicate with parents proactively about student progress</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Use the platform to share resources and assignments</span>
                </li>
                <li className="flex items-start">
                  <CheckCircle className="h-4 w-4 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                  <span>Monitor attendance patterns and address issues promptly</span>
                </li>
              </ul>
            </div>
          </div>
        </div>

        {/* Related Documentation */}
        <div className="mt-8 bg-white rounded-lg border border-gray-200 p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Related Documentation</h3>
          <div className="grid md:grid-cols-3 gap-4">
            <Link href="/docs/user-roles/school-admin" className="text-blue-600 hover:text-blue-800 text-sm">
              → School Administrator Guide
            </Link>
            <Link href="/docs/user-roles/parent" className="text-blue-600 hover:text-blue-800 text-sm">
              → Parent Guide
            </Link>
            <Link href="/docs/features/academic-management" className="text-blue-600 hover:text-blue-800 text-sm">
              → Academic Management
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
} 