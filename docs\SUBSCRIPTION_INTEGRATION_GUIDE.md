# Guide d'Intégration Subscription-Aware

Ce guide explique comment intégrer les fonctionnalités subscription-aware dans l'application Scholarify.

## 🏗️ Architecture

### 1. SubscriptionContext
Le contexte central qui gère l'état de l'abonnement et fournit les méthodes d'accès.

```typescript
import { useSubscription } from '@/context/SubscriptionContext';

const { 
  subscription,     // Données de l'abonnement
  isLoading,       // État de chargement
  error,           // Erreurs éventuelles
  schoolId,        // ID de l'école
  setSchoolId,     // Définir l'ID de l'école
  upgradeSubscription // Mettre à niveau
} = useSubscription();
```

### 2. Hooks Utilitaires

#### useFeatureAccess
```typescript
import { useFeatureAccess } from '@/context/SubscriptionContext';

const { 
  hasAccess,      // Booléen d'accès
  canUse,         // Alias pour hasAccess
  reason,         // Raison du refus
  requiredPlan,   // Plan requis
  currentPlan,    // Plan actuel
  isLoading       // État de chargement
} = useFeatureAccess('advanced_reports');
```

#### useFeatureLimit
```typescript
import { useFeatureLimit } from '@/context/SubscriptionContext';

const {
  limit,          // Limite maximale
  usage,          // Utilisation actuelle
  remaining,      // Restant disponible
  percentage,     // Pourcentage d'utilisation
  isLimitReached, // Limite atteinte
  isNearLimit     // Proche de la limite (>80%)
} = useFeatureLimit('students');
```

## 🧩 Composants

### 1. FeatureGate
Contrôle l'accès aux fonctionnalités avec fallback automatique.

```typescript
import { FeatureGate } from '@/components/subscription/FeatureGate';

<FeatureGate
  featureId="advanced_reports"
  fallback={<UpgradePrompt />}
>
  <AdvancedReportsComponent />
</FeatureGate>
```

### 2. FeatureButton
Bouton avec contrôle d'accès intégré.

```typescript
import { FeatureButton } from '@/components/subscription/FeatureButton';

<FeatureButton
  featureId="bulk_operations"
  className="bg-purple-600 hover:bg-purple-700"
>
  <Upload className="w-4 h-4 mr-2" />
  Import en lot
</FeatureButton>
```

### 3. SubscriptionAwareWrapper
Wrapper générique pour rendre n'importe quel composant subscription-aware.

```typescript
import { SubscriptionAwareWrapper } from '@/components/school-admin/SubscriptionAwareWrapper';

<SubscriptionAwareWrapper
  featureId="api_access"
  fallbackComponent={<UpgradeCard />}
>
  <ApiAccessComponent />
</SubscriptionAwareWrapper>
```

### 4. FeatureCard
Card avec gestion automatique des fonctionnalités premium.

```typescript
import { FeatureCard } from '@/components/school-admin/SubscriptionAwareWrapper';

<FeatureCard
  title="Rapports avancés"
  description="Analyses détaillées avec statistiques"
  featureId="advanced_reports"
  icon={<BarChart3 className="w-5 h-5" />}
>
  <ReportsContent />
</FeatureCard>
```

### 5. UsageLimit
Affichage des limites avec actions d'upgrade.

```typescript
import { UsageLimit } from '@/components/school-admin/SubscriptionAwareWrapper';

<UsageLimit
  current={45}
  limit={50}
  label="Étudiants"
  featureId="student_limit"
/>
```

## 🔧 Configuration

### 1. Initialisation du Context
Le SubscriptionProvider doit être ajouté au niveau racine de l'application.

```typescript
// app/layout.tsx
import { SubscriptionProvider } from '@/context/SubscriptionContext';

export default function RootLayout({ children }) {
  return (
    <AuthProvider>
      <SubscriptionProvider>
        {children}
      </SubscriptionProvider>
    </AuthProvider>
  );
}
```

### 2. Définition du SchoolId
Le schoolId doit être défini après l'authentification.

```typescript
// Dans un composant après l'auth
const { setSchoolId } = useSubscription();
const { user } = useAuth();

useEffect(() => {
  if (user?.schoolId) {
    setSchoolId(user.schoolId);
  }
}, [user, setSchoolId]);
```

## 📋 Types de Plans

Le système utilise trois types de plans définis dans `SchoolSubscriptionModel`:

- **basic** (niveau 1) - Fonctionnalités de base
- **standard** (niveau 2) - Fonctionnalités avancées
- **custom** (niveau 3) - Fonctionnalités enterprise

## 🎯 Exemples d'Utilisation

### Page avec Contrôle d'Accès
```typescript
'use client';

import { useSubscription, useFeatureAccess } from '@/context/SubscriptionContext';
import { FeatureGate } from '@/components/subscription/FeatureGate';

export default function StudentsPage() {
  const { subscription } = useSubscription();
  const { hasAccess } = useFeatureAccess('bulk_operations');

  return (
    <div>
      {/* Contenu de base */}
      <StudentList />
      
      {/* Fonctionnalité conditionnelle */}
      {hasAccess && <BulkOperationsPanel />}
      
      {/* Fonctionnalité avec fallback */}
      <FeatureGate
        featureId="advanced_reports"
        fallback={<UpgradePrompt />}
      >
        <AdvancedReports />
      </FeatureGate>
    </div>
  );
}
```

### Bouton avec Upgrade Automatique
```typescript
<FeatureButton
  featureId="api_access"
  onClick={() => console.log('API access granted')}
  className="bg-blue-600 hover:bg-blue-700"
>
  Accéder à l'API
</FeatureButton>
```

### Vérification de Limites
```typescript
const studentLimit = useFeatureLimit('students');

if (studentLimit.isLimitReached) {
  return <LimitReachedMessage />;
}

// Continuer avec l'ajout d'étudiant
```

## 🚀 Bonnes Pratiques

1. **Toujours vérifier l'accès** avant d'afficher des fonctionnalités premium
2. **Utiliser les composants fournis** plutôt que de réimplémenter la logique
3. **Fournir des fallbacks appropriés** pour une UX fluide
4. **Définir le schoolId** dès que possible après l'authentification
5. **Utiliser les hooks de limites** pour prévenir les dépassements

## 🔍 Debugging

### Vérifier l'état du contexte
```typescript
const { subscription, isLoading, error } = useSubscription();

console.log('Subscription state:', {
  subscription,
  isLoading,
  error
});
```

### Tester l'accès aux fonctionnalités
```typescript
const access = useFeatureAccess('feature_id');
console.log('Feature access:', access);
```

## 📚 Ressources

- [SubscriptionContext.tsx](../src/context/SubscriptionContext.tsx)
- [FeatureGate.tsx](../src/components/subscription/FeatureGate.tsx)
- [SubscriptionAwareWrapper.tsx](../src/components/school-admin/SubscriptionAwareWrapper.tsx)
- [Exemple d'intégration](../src/components/school-admin/examples/SubscriptionIntegrationExample.tsx)
