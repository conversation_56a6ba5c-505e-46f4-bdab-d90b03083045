"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Lock, Crown, ArrowRight, Info } from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { useSubscription } from '@/hooks/useSubscription';

interface FeatureLockedModalProps {
  isOpen: boolean;
  onClose: () => void;
  featureId: string;
  requiredPlan: string;
  currentPlan: string;
}

interface FeatureLockedModalState {
  isOpen: boolean;
  props: Omit<FeatureLockedModalProps, 'isOpen' | 'onClose'>;
}

// État global pour la modale
let modalState: FeatureLockedModalState = {
  isOpen: false,
  props: {
    featureId: '',
    requiredPlan: 'standard',
    currentPlan: 'basic'
  }
};

let setModalState: React.Dispatch<React.SetStateAction<FeatureLockedModalState>> | null = null;

/**
 * Fonction pour afficher la modale de fonctionnalité verrouillée
 */
export const showFeatureLockedModal = (props: Omit<FeatureLockedModalProps, 'isOpen' | 'onClose'>) => {
  if (setModalState) {
    setModalState({
      isOpen: true,
      props
    });
  }
};

/**
 * Fonction pour fermer la modale de fonctionnalité verrouillée
 */
export const hideFeatureLockedModal = () => {
  if (setModalState) {
    setModalState(prev => ({
      ...prev,
      isOpen: false
    }));
  }
};

/**
 * Composant de modale pour les fonctionnalités verrouillées
 */
const FeatureLockedModal: React.FC<FeatureLockedModalProps> = ({
  isOpen,
  onClose,
  featureId,
  requiredPlan,
  currentPlan
}) => {
  const { t } = useTranslation();
  const { upgradeSubscription } = useSubscription();
  const [isUpgrading, setIsUpgrading] = useState(false);

  const getPlanColor = (plan: string) => {
    switch (plan) {
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      case 'enterprise':
        return 'from-purple-500 to-pink-500';
      case 'standard':
        return 'from-blue-500 to-cyan-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const handleUpgrade = async () => {
    setIsUpgrading(true);
    try {
      await upgradeSubscription(requiredPlan);
      onClose();
    } catch (error) {
      console.error('Erreur lors de la mise à niveau:', error);
    } finally {
      setIsUpgrading(false);
    }
  };

  const featureName = t(`subscription.features.${featureId}`, featureId);
  const currentPlanName = t(`subscription.plans.${currentPlan}`);
  const requiredPlanName = t(`subscription.plans.${requiredPlan}`);

  return (
    <AnimatePresence>
      {isOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center">
          {/* Overlay */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className="absolute inset-0 bg-black bg-opacity-50"
            onClick={onClose}
          />

          {/* Modal */}
          <motion.div
            initial={{ opacity: 0, scale: 0.9, y: 20 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.9, y: 20 }}
            className="relative bg-white dark:bg-gray-800 rounded-2xl shadow-2xl max-w-md w-full mx-4 overflow-hidden"
          >
            {/* Header */}
            <div className="p-6 bg-gradient-to-r from-red-500 to-pink-500 text-white">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="p-2 bg-white bg-opacity-20 rounded-full">
                    <Lock className="w-6 h-6" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold">
                      {t('subscription.feature_locked')}
                    </h3>
                    <p className="text-white/80 text-sm">
                      {t('subscription.access_denied')}
                    </p>
                  </div>
                </div>
                <button
                  onClick={onClose}
                  className="text-white/80 hover:text-white transition-colors"
                >
                  <X className="w-6 h-6" />
                </button>
              </div>
            </div>

            {/* Content */}
            <div className="p-6">
              {/* Feature info */}
              <div className="mb-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
                <div className="flex items-start space-x-3">
                  <Info className="w-5 h-5 text-blue-500 mt-0.5 flex-shrink-0" />
                  <div>
                    <h4 className="font-medium text-gray-900 dark:text-gray-100 mb-1">
                      {featureName}
                    </h4>
                    <p className="text-sm text-gray-600 dark:text-gray-400">
                      {t('subscription.feature_requires_upgrade', { plan: requiredPlanName })}
                    </p>
                  </div>
                </div>
              </div>

              {/* Current vs Required */}
              <div className="mb-6">
                <div className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-700 rounded-lg">
                  <div className="text-center">
                    <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      {t('subscription.current_plan')}
                    </p>
                    <p className="font-semibold text-gray-900 dark:text-gray-100">
                      {currentPlanName}
                    </p>
                  </div>
                  
                  <ArrowRight className="w-5 h-5 text-gray-400" />
                  
                  <div className="text-center">
                    <p className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                      Requis
                    </p>
                    <p className={`font-semibold bg-gradient-to-r ${getPlanColor(requiredPlan)} bg-clip-text text-transparent`}>
                      {requiredPlanName}
                    </p>
                  </div>
                </div>
              </div>

              {/* Benefits preview */}
              <div className="mb-6">
                <h4 className="font-semibold text-gray-900 dark:text-gray-100 mb-3">
                  Avec {requiredPlanName}, vous débloquez :
                </h4>
                <ul className="space-y-2 text-sm text-gray-600 dark:text-gray-400">
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>{featureName}</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Fonctionnalités avancées</span>
                  </li>
                  <li className="flex items-center space-x-2">
                    <div className="w-1.5 h-1.5 bg-blue-500 rounded-full"></div>
                    <span>Support prioritaire</span>
                  </li>
                </ul>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                <button
                  onClick={onClose}
                  className="flex-1 px-4 py-2 border border-gray-300 dark:border-gray-600 rounded-lg text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
                >
                  {t('common.cancel')}
                </button>
                <button
                  onClick={handleUpgrade}
                  disabled={isUpgrading}
                  className={`flex-1 px-4 py-2 bg-gradient-to-r ${getPlanColor(requiredPlan)} text-white rounded-lg hover:opacity-90 transition-opacity flex items-center justify-center space-x-2 disabled:opacity-50`}
                >
                  {isUpgrading ? (
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <>
                      <Crown className="w-4 h-4" />
                      <span>{t('subscription.upgrade_now')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

/**
 * Provider pour la modale de fonctionnalité verrouillée
 */
export const FeatureLockedModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, setState] = useState<FeatureLockedModalState>(modalState);

  // Enregistrer la fonction setState globalement
  React.useEffect(() => {
    setModalState = setState;
    return () => {
      setModalState = null;
    };
  }, []);

  return (
    <>
      {children}
      <FeatureLockedModal
        isOpen={state.isOpen}
        onClose={hideFeatureLockedModal}
        {...state.props}
      />
    </>
  );
};

export default FeatureLockedModal;
