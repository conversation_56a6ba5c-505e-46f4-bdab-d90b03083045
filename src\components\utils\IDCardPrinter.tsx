import { CreditCard } from 'lucide-react';
import React, { useState } from 'react';

// Define the types for your props to ensure type safety
interface Student {
    _id: string;
    first_name: string;
    last_name: string;
    avatar?: string;
    student_id?: string;
    gender?: string;
    nationality?: string;
    class_level?: string;
    date_of_birth?: Date | string;
    qrCode?: string; // If you have pre-generated QR codes
}

interface School {
    name?: string;
    logo?: string;
}

interface IDCardPrinterProps {
    students: Student[];
    school?: School;
    acadamicYear: string;
    getClassLevelNames: (levels: string[]) => string;
}

const IDCardPrinter: React.FC<IDCardPrinterProps> = ({
    students,
    school,
    acadamicYear,
    getClassLevelNames,
}) => {
    const [showOrientationModal, setShowOrientationModal] = useState(false);

    const generateIDCardHTML = (orientation: 'portrait' | 'landscape') => {
        const logoURL = school?.logo || `${window.location.origin}/assets/images/school-logo.jpg`;
        let htmlContent = '';

        if (orientation === 'landscape') {
            htmlContent = `
        <html>
          <head>
            <title>Student ID Cards - Landscape</title>
           <style>
          /*
           * GLOBAL STYLES (Apply to both screen and print by default)
           */
          body {
            margin: 0;
            font-family: 'Segoe UI', sans-serif;
            background: #f5f5f5;
            padding: 15px; /* Keep padding for screen view */
          }

          .page {
            display: grid;
            /* Update minmax to card width */
            grid-template-columns: repeat(auto-fit, minmax(85.6mm, 1fr));
            gap: 5mm; /* Reduced gap between cards for more density on the page */
            justify-content: center;
            align-items: start; /* Align grid items to the top within their cells */
            width: 100%;
            height: auto;
            padding: 5mm; /* Reduced page padding to fit more cards */
            box-sizing: border-box;
          }

          .card {
            width: 85.6mm; /* Standard CR80 width */
            height: 54mm;  /* Standard CR80 height */
            background: #ffffff;
            border-radius: 2mm; /* Reduced border-radius */
            box-shadow: 0 0.5mm 2mm rgba(0, 0, 0, 0.1); /* Reduced shadow */
            padding: 3mm; /* Reduced internal padding of the card */
            display: flex;
            flex-direction: column;
            align-items: center; /* Center horizontally within card */
            overflow: hidden;
            page-break-inside: avoid;
          }

          .text-center {
              width: 100%;
              text-align: center;
          }

          .card-flex {
            display: flex;
            justify-content: center;
            align-items: center; /* Vertically center photo and info details */
            width: 100%;
            flex-grow: 1; /* Allow to take available vertical space */
            gap: 2mm; /* Reduced gap between photo and info */
            box-sizing: border-box;
          }

          .photo {
            width: 30mm; /* Significantly reduced photo size */
            height: 30mm; /* Significantly reduced photo size */
            border-radius: 1mm; /* Smaller border radius for photo */
            flex-shrink: 0;
            object-fit: cover;
          }

          .photo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 1mm;
          }

          .card-right {
            display: flex;
            flex-direction: row;
            flex-grow: 1;
            justify-content: space-between;
            align-items: flex-end; /* Align to bottom for QR/Logo */
            height: 100%; /* Take full height of its parent (card-flex) */
          }

          .info-details {
            flex-grow: 1;
            padding-right: 1mm; /* Reduced padding */
            display: flex;
            flex-direction: column;
            justify-content: center; /* Center content vertically within its space */
            height: 100%;
          }

          .republic {
            font-size: 2.5mm; /* Smaller font size */
            font-weight: bold;
            color: #1E3D59;
            margin-bottom: 0;
            line-height: 1.1;
          }

          .motto {
            font-size: 2mm; /* Smaller font size */
            font-style: italic;
            color: #1E3D59;
            margin-top: 0;
            margin-bottom: 0.5mm;
            line-height: 1.1;
          }

          .school-header {
            margin-bottom: 0.5mm;
          }

          .school-name {
            font-size: 3.5mm; /* Smaller font size */
            font-weight: bold;
            color: #1E3D59;
            line-height: 1.1;
          }

          .card-title {
            font-size: 3mm; /* Smaller font size */
            font-weight: bold;
            color: #000;
            margin-top: 1mm;
            line-height: 1.1;
          }

          .info-item {
            font-size: 2.5mm; /* Smaller info text font size */
            margin: 0.2mm 0; /* Very small vertical margin for compactness */
            line-height: 1.2; /* Tighter line height */
            word-wrap: break-word;
          }

          .info-item strong {
            font-weight: bold;
            color: #1E3D59;
            margin-right: 1mm; /* Reduced margin */
          }

          .qr-logo-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1mm; /* Reduced gap */
            flex-shrink: 0;
            justify-content: flex-end; /* Align QR/logo to bottom */
          }

          .qr {
            width: 15mm; /* Reduced QR size */
            height: 15mm; /* Reduced QR size */
          }

          .school-logo {
            width: 15mm; /* Reduced logo size */
            height: 15mm; /* Reduced logo size */
            object-fit: contain;
          }

          /*
           * PRINT-SPECIFIC STYLES (Only apply when printing)
           */
          @media print {
            body {
              margin: 0;
              -webkit-print-color-adjust: exact;
              print-color-adjust: exact;
              background: none;
              padding: 0;
            }
            @page {
              size: landscape;
              margin: 5mm; /* Adjusted page margin for printing to be tighter */
            }
            .page {
              display: grid;
              grid-template-columns: repeat(auto-fit, minmax(85.6mm, 1fr));
              gap: 5mm;
              justify-content: center;
              align-items: start;
              padding: 5mm; /* Consistent with global */
            }
          }
        </style>
          </head>
          <body>
            <div class="page">
                ${students.map(student => {
                const dob = new Date(student.date_of_birth ?? "");
                const formattedDOB = dob.toLocaleDateString('en-GB');
                const baseVerifyUrl = "https://google.com/verify-student"; // Replace with your actual URL
                const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
                    `${baseVerifyUrl}/${student._id}`
                )}&size=50x50`;
                return `
              <div class="card">
                <div class="text-center">
                  <div class="republic">REPUBLIC OF CAMEROON</div>
                  <div class="motto">Peace - Work - Fatherland</div>
                  <div class="school-header">
                    <div class="school-name">${school?.name || "SCHOOL NAME"}</div>
                  </div>
                  <div class="card-title">STUDENT ID CARD</div>
                </div>

                <div class="card-flex">
                  <div class="photo">
                    <img src="${student.avatar || `${window.location.origin}/assets/images/student.jpg`}" alt="Student Photo" />
                  </div>
                  <div class="card-right">
                    <div class="info-details">
                      <p class="info-item"><strong>Name:</strong> ${student.first_name} ${student.last_name}</p>
                      <p class="info-item"><strong>Student ID:</strong> ${student.student_id || "N/A"}</p>
                      <p class="info-item"><strong>Class:</strong> ${getClassLevelNames([student.class_level ?? ""])}</p>
                      <p class="info-item"><strong>Date Of Birth:</strong> ${formattedDOB}</p>
                      <p class="info-item"><strong>Gender:</strong> ${student.gender || "N/A"}</p>
                      <p class="info-item"><strong>Nationality:</strong> ${student.nationality || "N/A"}</p>
                      <p class="info-item"><strong>Academic Year:</strong> ${acadamicYear || "N/A"}</p>
                    </div>
                    <div class="qr-logo-container">
                      <img src="${student.qrCode || qrCodeURL}" alt="QR Code" class="qr" />
                      <img src="${logoURL}" alt="School Logo" class="school-logo" />
                    </div>
                  </div>
                </div>
              </div>`;
            }).join('')}
            </div>
            <script>
              window.onload = function () {
                window.print();
                window.onafterprint = function () {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `;
        } else {
            // Original Portrait Orientation Code
            htmlContent = `
        <html>
          <head>
            <title>Student ID Cards</title>
            <style>
              @media print {
                body {
                  margin: 0;
                  -webkit-print-color-adjust: exact;
                  print-color-adjust: exact;
                }
                .page {
                  page-break-after: always;
                }
              }

              body {
                margin: 0;
                font-family: 'Segoe UI', sans-serif;
                background: #f5f5f5;
                padding: 20px;
                display: flex;
                flex-direction: column;
                align-items: center;
              }

              .page {
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 20px;
                margin-bottom: 40px;
              }

              .card {
                width: 280px;
                height: 440px;
                background: #ffffff;
                border-radius: 12px;
                box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
                padding: 12px;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: space-between;
              }

              .republic {
                text-align: center;
                font-size: 10px;
                font-weight: bold;
                color: #1E3D59;
              }

              .motto {
                text-align: center;
                font-size: 8px;
                font-style: italic;
                color: #1E3D59;
                margin-bottom: 4px;
              }

              .school-header {
                text-align: center;
                margin-bottom: 4px;
              }

              .school-name {
                font-size: 14px;
                font-weight: bold;
                color: #1E3D59;
              }

              .card-title {
                font-size: 11px;
                font-weight: bold;
                margin: 4px 0 6px;
                color: #000;
              }

              .photo {
                width: 180px;
                height: 180px;
                border-radius: 20px;
                margin-bottom: 10px;
              }

              .photo img {
                width: 100%;
                height: 100%;
                object-fit: cover;
              }

              .info-grid {
                display: flex;
                justify-content: space-between;
                width: 100%;
                font-size: 10px;
                gap: 8px;
              }

              .info-column {
                width: 30%;
                line-height: 1.5;
              }

              .info-column p {
                margin: 4px 0;
              }

              .info-column strong {
                display: block;
                font-weight: bold;
                color: #1E3D59;
              }

              .qr-logo-column {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                gap: 8px;
                width: 40%;
              }

              .qr {
                width: 60px;
                height: 60px;
              }

              .school-logo {
                width: 60px;
                height: 60px;
                object-fit: contain;
              }
            </style>
          </head>
          <body>
            <div class="page">
              ${students.map(student => {
                const dob = new Date(student.date_of_birth ?? "");
                const formattedDOB = dob.toLocaleDateString('en-GB');
                const baseVerifyUrl = "https://google.com/verify-student"; // Replace with your actual URL
                const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
                    `${baseVerifyUrl}/${student._id}`
                )}&size=60x60`;
                return `
                  <div class="card">
                    <div class="republic">REPUBLIC OF CAMEROON</div>
                    <div class="motto">Peace - Work - Fatherland</div>
                    <div class="school-header">
                      <div class="school-name">${school?.name || "SCHOOL NAME"}</div>
                    </div>
                    <div class="card-title">STUDENT ID CARD</div>
                    <div class="photo">
                      <img src="${student.avatar || `${window.location.origin}/assets/images/student.jpg`}" alt="Student Photo" />
                    </div>
                    <div class="info-grid">
                      <div class="info-column">
                        <p><strong>Name</strong> <em>${student.first_name} ${student.last_name}</em></p>
                        <p><strong>School</strong> <em>${school?.name || "N/A"}</em></p>
                        <p><strong>Student ID</strong> <em>${student.student_id || "N/A"}</em></p>
                      </div>
                      <div class="info-column">
                        <p><strong>Gender</strong> <em>${student.gender || "N/A"}</em></p>
                        <p><strong>Nationality</strong> <em>${student.nationality || "N/A"}</em></p>
                        <p><strong>Class</strong> <em>${getClassLevelNames([student.class_level ?? ""])}</em></p>
                        <p><strong>Date Of Birth</strong> <em>${formattedDOB}</em></p>
                        <p><strong>Academic Year</strong> <em>${acadamicYear || "N/A"}</em></p>
                      </div>
                      <div class="qr-logo-column">
                        <img src="${student.qrCode || qrCodeURL}" alt="QR Code" class="qr" />
                        <img src="${logoURL}" alt="School Logo" class="school-logo" />
                      </div>
                    </div>
                  </div>`;
            }).join('')}
            </div>
            <script>
              window.onload = function () {
                window.print();
                window.onafterprint = function () {
                  window.close();
                };
              };
            </script>
          </body>
        </html>
      `;
        }
        return htmlContent;
    };

    const handlePrint = (orientation: 'portrait' | 'landscape') => {
        const printWindow = window.open('', '_blank');

        if (!printWindow) {
            alert('Pop-up blocked! Please allow pop-ups for this site to print ID cards.');
            return;
        }

        const html = generateIDCardHTML(orientation);
        printWindow.document.open();
        printWindow.document.write(html);
        printWindow.document.close();
        setShowOrientationModal(false); // Close the modal after printing
    };

    return (
        <>

            <button
                onClick={() => setShowOrientationModal(true)}
                className="flex items-center gap-2 px-4 py-2 rounded-md bg-orange-500 text-white hover:bg-orange-600 text-sm"
            >
                <CreditCard size={18} /> Print ID Cards
            </button>
            {showOrientationModal && (
                <div style={{
                    position: 'fixed',
                    top: 0,
                    left: 0,
                    right: 0,
                    bottom: 0,
                    backgroundColor: 'rgba(0,0,0,0.5)',
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    zIndex: 1000,
                }}>
                    <div style={{
                        backgroundColor: 'white',
                        padding: '25px',
                        borderRadius: '10px',
                        boxShadow: '0 4px 15px rgba(0,0,0,0.2)',
                        textAlign: 'center',
                        minWidth: '300px',
                        maxWidth: '90%',
                    }}>
                        <h3 style={{ marginBottom: '20px', color: '#333' }}>Choose Print Orientation</h3>
                        <div style={{ display: 'flex', justifyContent: 'space-around', gap: '15px' }}>
                            <button
                                onClick={() => handlePrint('portrait')}
                                style={{
                                    padding: '12px 25px',
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    backgroundColor: '#4CAF50',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    transition: 'background-color 0.3s ease',
                                }}
                            >
                                Portrait
                            </button>
                            <button
                                onClick={() => handlePrint('landscape')}
                                style={{
                                    padding: '12px 25px',
                                    fontSize: '16px',
                                    fontWeight: 'bold',
                                    backgroundColor: '#008CBA',
                                    color: 'white',
                                    border: 'none',
                                    borderRadius: '8px',
                                    cursor: 'pointer',
                                    transition: 'background-color 0.3s ease',
                                }}
                            >
                                Landscape
                            </button>
                        </div>
                        <button
                            onClick={() => setShowOrientationModal(false)}
                            style={{
                                marginTop: '20px',
                                padding: '8px 15px',
                                fontSize: '14px',
                                backgroundColor: '#f44336',
                                color: 'white',
                                border: 'none',
                                borderRadius: '5px',
                                cursor: 'pointer',
                            }}
                        >
                            Cancel
                        </button>
                    </div>
                </div>
            )}
        </>
    );
};

export default IDCardPrinter;