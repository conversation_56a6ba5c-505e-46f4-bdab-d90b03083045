/**
 * Tests d'intégration pour le système de contrôle d'accès basé sur les abonnements
 */

import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import React from 'react';

// Composants à tester
import FeatureGate from '@/app/components/subscription/FeatureGate';
import FeatureButton from '@/app/components/subscription/FeatureButton';
import SubscriptionStatus from '@/app/components/subscription/SubscriptionStatus';
import { SubscriptionModalProvider } from '@/app/components/subscription/SubscriptionModalProvider';

// Services
import ApiInterceptorService, { SubscriptionRequiredError, FeatureAccessDeniedError } from '@/app/services/ApiInterceptorService';
import FeatureRegistryServices from '@/app/services/FeatureRegistryServices';

// Hooks
import { useSubscription } from '@/hooks/useSubscription';

// Mock des services
jest.mock('@/app/services/ApiInterceptorService');
jest.mock('@/app/services/FeatureRegistryServices');
jest.mock('@/hooks/useSubscription');

const mockApiInterceptorService = ApiInterceptorService as jest.Mocked<typeof ApiInterceptorService>;
const mockFeatureRegistryServices = FeatureRegistryServices as jest.Mocked<typeof FeatureRegistryServices>;
const mockUseSubscription = useSubscription as jest.MockedFunction<typeof useSubscription>;

describe('Système de Contrôle d\'Accès par Abonnement', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock par défaut pour useSubscription
    mockUseSubscription.mockReturnValue({
      subscription: {
        plan_name: 'basic',
        credits_remaining: 100,
        is_active: true,
        expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 jours
      },
      loading: false,
      error: null,
      refreshSubscription: jest.fn(),
    });

    // Mock par défaut pour FeatureRegistryServices
    mockFeatureRegistryServices.checkFeatureAccess.mockResolvedValue({
      hasAccess: true,
      reason: null,
      subscriptionRequired: null,
    });
  });

  describe('FeatureGate Component', () => {
    it('affiche le contenu quand l\'utilisateur a accès à la fonctionnalité', async () => {
      render(
        <SubscriptionModalProvider>
          <FeatureGate featureId="student_management">
            <div>Contenu protégé</div>
          </FeatureGate>
        </SubscriptionModalProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('Contenu protégé')).toBeInTheDocument();
      });
    });

    it('affiche le fallback quand l\'utilisateur n\'a pas accès', async () => {
      mockFeatureRegistryServices.checkFeatureAccess.mockResolvedValue({
        hasAccess: false,
        reason: 'subscription_required',
        subscriptionRequired: 'premium',
      });

      render(
        <SubscriptionModalProvider>
          <FeatureGate 
            featureId="advanced_analytics"
            fallback={<div>Mise à niveau requise</div>}
          >
            <div>Contenu protégé</div>
          </FeatureGate>
        </SubscriptionModalProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('Mise à niveau requise')).toBeInTheDocument();
        expect(screen.queryByText('Contenu protégé')).not.toBeInTheDocument();
      });
    });
  });

  describe('FeatureButton Component', () => {
    it('exécute l\'action quand l\'utilisateur a accès', async () => {
      const mockOnClick = jest.fn();

      render(
        <SubscriptionModalProvider>
          <FeatureButton featureId="student_export" onClick={mockOnClick}>
            Exporter
          </FeatureButton>
        </SubscriptionModalProvider>
      );

      const button = screen.getByText('Exporter');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockOnClick).toHaveBeenCalled();
      });
    });

    it('ouvre la modale d\'upgrade quand l\'accès est refusé', async () => {
      mockFeatureRegistryServices.checkFeatureAccess.mockResolvedValue({
        hasAccess: false,
        reason: 'subscription_required',
        subscriptionRequired: 'premium',
      });

      const mockOnClick = jest.fn();

      render(
        <SubscriptionModalProvider>
          <FeatureButton featureId="advanced_export" onClick={mockOnClick}>
            Export Avancé
          </FeatureButton>
        </SubscriptionModalProvider>
      );

      const button = screen.getByText('Export Avancé');
      fireEvent.click(button);

      await waitFor(() => {
        expect(mockOnClick).not.toHaveBeenCalled();
        // Vérifier que la modale d'upgrade s'ouvre
        expect(screen.getByText(/mise à niveau/i)).toBeInTheDocument();
      });
    });
  });

  describe('ApiInterceptorService', () => {
    it('gère correctement les erreurs SubscriptionRequiredError', async () => {
      const subscriptionError = new SubscriptionRequiredError({
        error: 'subscription_required',
        subscription_required: 'premium',
        current_subscription: 'basic',
        message: 'Cette fonctionnalité nécessite un abonnement Premium'
      });

      mockApiInterceptorService.get.mockRejectedValue(subscriptionError);

      try {
        await ApiInterceptorService.get('/test-endpoint');
        fail('L\'erreur aurait dû être lancée');
      } catch (error) {
        expect(error).toBeInstanceOf(SubscriptionRequiredError);
        expect((error as SubscriptionRequiredError).subscriptionError.subscription_required).toBe('premium');
      }
    });

    it('gère correctement les erreurs FeatureAccessDeniedError', async () => {
      const accessError = new FeatureAccessDeniedError({
        error: 'feature_access_denied',
        subscription_required: 'enterprise',
        current_subscription: 'standard',
        message: 'Accès refusé à cette fonctionnalité'
      });

      mockApiInterceptorService.post.mockRejectedValue(accessError);

      try {
        await ApiInterceptorService.post('/test-endpoint', {});
        fail('L\'erreur aurait dû être lancée');
      } catch (error) {
        expect(error).toBeInstanceOf(FeatureAccessDeniedError);
        expect((error as FeatureAccessDeniedError).subscriptionError.subscription_required).toBe('enterprise');
      }
    });
  });

  describe('SubscriptionStatus Component', () => {
    it('affiche le statut d\'abonnement correctement', () => {
      render(
        <SubscriptionModalProvider>
          <SubscriptionStatus />
        </SubscriptionModalProvider>
      );

      expect(screen.getByText(/basic/i)).toBeInTheDocument();
      expect(screen.getByText(/100/)).toBeInTheDocument(); // crédits restants
    });

    it('affiche un avertissement quand les crédits sont faibles', () => {
      mockUseSubscription.mockReturnValue({
        subscription: {
          plan_name: 'standard',
          credits_remaining: 5, // Faible nombre de crédits
          is_active: true,
          expires_at: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
        },
        loading: false,
        error: null,
        refreshSubscription: jest.fn(),
      });

      render(
        <SubscriptionModalProvider>
          <SubscriptionStatus />
        </SubscriptionModalProvider>
      );

      expect(screen.getByText(/crédits faibles/i)).toBeInTheDocument();
    });
  });

  describe('Intégration Complète', () => {
    it('fonctionne de bout en bout avec tous les composants', async () => {
      const TestComponent = () => (
        <SubscriptionModalProvider>
          <div>
            <SubscriptionStatus />
            <FeatureGate featureId="student_management">
              <FeatureButton 
                featureId="student_export" 
                onClick={() => console.log('Export déclenché')}
              >
                Exporter les étudiants
              </FeatureButton>
            </FeatureGate>
            <FeatureGate 
              featureId="advanced_analytics"
              fallback={<div>Analytics non disponible</div>}
            >
              <div>Dashboard Analytics</div>
            </FeatureGate>
          </div>
        </SubscriptionModalProvider>
      );

      render(<TestComponent />);

      // Vérifier que le statut s'affiche
      expect(screen.getByText(/basic/i)).toBeInTheDocument();

      // Vérifier que la fonctionnalité de base est accessible
      await waitFor(() => {
        expect(screen.getByText('Exporter les étudiants')).toBeInTheDocument();
      });

      // Simuler l'accès refusé pour les analytics avancés
      mockFeatureRegistryServices.checkFeatureAccess.mockImplementation((featureId) => {
        if (featureId === 'advanced_analytics') {
          return Promise.resolve({
            hasAccess: false,
            reason: 'subscription_required',
            subscriptionRequired: 'premium',
          });
        }
        return Promise.resolve({
          hasAccess: true,
          reason: null,
          subscriptionRequired: null,
        });
      });

      // Re-render pour déclencher la vérification
      render(<TestComponent />);

      await waitFor(() => {
        expect(screen.getByText('Analytics non disponible')).toBeInTheDocument();
      });
    });
  });

  describe('Gestion des Erreurs de Service', () => {
    it('gère les erreurs de réseau gracieusement', async () => {
      mockApiInterceptorService.get.mockRejectedValue(new Error('Network error'));

      try {
        await ApiInterceptorService.get('/test-endpoint');
        fail('L\'erreur aurait dû être lancée');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Network error');
      }
    });

    it('gère les timeouts correctement', async () => {
      mockApiInterceptorService.get.mockRejectedValue(new Error('Request timeout'));

      try {
        await ApiInterceptorService.get('/slow-endpoint');
        fail('L\'erreur aurait dû être lancée');
      } catch (error) {
        expect(error).toBeInstanceOf(Error);
        expect((error as Error).message).toBe('Request timeout');
      }
    });
  });
});

// Tests de performance
describe('Performance du Système', () => {
  it('cache les vérifications de fonctionnalités', async () => {
    const featureId = 'test_feature';
    
    // Premier appel
    await FeatureRegistryServices.checkFeatureAccess(featureId);
    
    // Deuxième appel (devrait utiliser le cache)
    await FeatureRegistryServices.checkFeatureAccess(featureId);
    
    // Vérifier que l'API n'a été appelée qu'une fois
    expect(mockFeatureRegistryServices.checkFeatureAccess).toHaveBeenCalledTimes(2);
  });

  it('invalide le cache après expiration', async () => {
    const featureId = 'test_feature_expire';
    
    // Simuler l'expiration du cache
    jest.useFakeTimers();
    
    await FeatureRegistryServices.checkFeatureAccess(featureId);
    
    // Avancer le temps de 6 minutes (cache expire après 5 minutes)
    jest.advanceTimersByTime(6 * 60 * 1000);
    
    await FeatureRegistryServices.checkFeatureAccess(featureId);
    
    // Vérifier que l'API a été appelée deux fois
    expect(mockFeatureRegistryServices.checkFeatureAccess).toHaveBeenCalledTimes(2);
    
    jest.useRealTimers();
  });
});
