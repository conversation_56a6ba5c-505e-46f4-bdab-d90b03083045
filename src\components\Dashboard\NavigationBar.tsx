"use client";
import React, { useEffect, useState } from "react";
import SearchBox from "../widgets/SearchBox";
import { Bell, Settings, Home, Menu, X } from "lucide-react";
import ThemeToggle from "../ThemeToggle";
import AvatarImage from "../widgets/AvatarImage";
import Breadcrumbs from './BreadCrums'
import UserMenuModal from "../widgets/UserMenuModal";
import NotificationCenter from "../widgets/NotificationCenter";
import SchoolPoints from "../widgets/SchoolPoints";
import useAuth from "@/app/hooks/useAuth";
import { useAcademicYearContext } from "@/context/AcademicYearContext";
import ActiveAcademicYear from "../ActiveAcademicYear";
import LanguageSelector from "../LanguageSelector";
import { useTheme } from "@/contexts/ThemeContext";

interface NavigationBarProps {
  icon: any;
  baseHref: string;
  title: string;
  isSidebarOpen?: boolean;
  toggleSidebar?: () => void; // Optional function to toggle sidebar
  onLogout: () => void
}
export default function NavigationBar({ icon: Icon, baseHref, title, toggleSidebar, isSidebarOpen, onLogout }: NavigationBarProps) {
  const { user, logout } = useAuth();
  const { theme, toggleTheme } = useTheme();


  return (
    <div className="w-full flex items-center justify-between p-4 bg-glassy shadow-md border md:border-none md:shadow-none border-gray-300 darK:border dark:border-gray-800">
      {/* Mobile Sidebar Toggle and School Points */}
      <button
        id="mobile-sidebar-toggle"
        className="block lg:hidden p-2 text-foreground rounded-lg top-4 left-4 z-30"
        onClick={() => toggleSidebar && toggleSidebar()}
      >
        {isSidebarOpen ? <X size={24} /> : <Menu size={24} />}
      </button>

      <div className="hidden lg:flex flex-col gap-2">
        <Breadcrumbs baseHref={baseHref} icon={Icon} />
        <p className="text-2xl font-semibold text-foreground">{title}</p>
      </div>

      {/* Right: Actions (Search, Notifications, School Points, Language, Dark Mode, Settings, Profile) */}
      <div className="flex items-center gap-2">
        {/* <SearchBox /> */}
        <ActiveAcademicYear/>
        <SchoolPoints className="hidden md:flex" />
        <LanguageSelector variant="compact" className="hidden sm:block" />
        <NotificationCenter className="hidden lg:block" />
        <ThemeToggle />
        <UserMenuModal
          avatarUrl={user?.avatar || "https://img.freepik.com/free-psd/3d-illustration-person-with-sunglasses_23-2149436188.jpg"}
          userName={user?.name || ""}
          onToggleTheme={toggleTheme}
          isDarkMode={theme === 'dark'}
          role={user?.role || ""}
        />
      </div>
    </div>
  );
}
