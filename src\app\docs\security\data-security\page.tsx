"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  Shield,
  Lock,
  Database,
  FileText,
  CheckCircle,
  ArrowRight,
  Eye,
  Settings,
  AlertTriangle,
  Key,
  Globe,
  Server,
  Plus,
  Download
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function DataSecurityPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'encryption', name: 'Encryption', icon: Lock },
    { id: 'storage', name: 'Data Storage', icon: Database },
    { id: 'transmission', name: 'Data Transmission', icon: Globe },
    { id: 'backup', name: 'Backup & Recovery', icon: Server },
    { id: 'compliance', name: 'Compliance', icon: Shield },
  ];

  const features = [
    {
      title: 'Data Encryption',
      description: 'Comprehensive encryption for data at rest and in transit.',
      icon: Lock,
      benefits: [
        'AES-256 encryption at rest',
        'TLS 1.3 encryption in transit',
        'End-to-end encryption',
        'Key management system',
        'Encryption key rotation',
        'Hardware security modules'
      ]
    },
    {
      title: 'Secure Data Storage',
      description: 'Enterprise-grade data storage with advanced security measures.',
      icon: Database,
      benefits: [
        'Redundant data centers',
        'Geographic data distribution',
        'Automated backups',
        'Data integrity checks',
        'Disaster recovery',
        'Secure access controls'
      ]
    },
    {
      title: 'Data Transmission Security',
      description: 'Secure data transmission with multiple layers of protection.',
      icon: Globe,
      benefits: [
        'HTTPS/TLS encryption',
        'API security measures',
        'Secure file transfers',
        'VPN access options',
        'Network security',
        'DDoS protection'
      ]
    },
    {
      title: 'Access Control',
      description: 'Multi-layered access control for data protection.',
      icon: Key,
      benefits: [
        'Role-based access control',
        'Multi-factor authentication',
        'Session management',
        'IP whitelisting',
        'Time-based access',
        'Audit logging'
      ]
    },
    {
      title: 'Backup & Recovery',
      description: 'Comprehensive backup and disaster recovery solutions.',
      icon: Server,
      benefits: [
        'Automated daily backups',
        'Point-in-time recovery',
        'Cross-region replication',
        'Backup encryption',
        'Recovery testing',
        'Business continuity'
      ]
    },
    {
      title: 'Compliance & Governance',
      description: 'Built-in compliance features for educational data protection.',
      icon: Shield,
      benefits: [
        'GDPR compliance',
        'FERPA compliance',
        'Data retention policies',
        'Privacy controls',
        'Audit trails',
        'Compliance reporting'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Security Settings',
      description: 'Configure security parameters',
      icon: Settings,
      action: () => console.log('Security settings')
    },
    {
      title: 'View Audit Logs',
      description: 'Monitor security activities',
      icon: FileText,
      action: () => console.log('View audit logs')
    },
    {
      title: 'Backup Status',
      description: 'Check backup and recovery status',
      icon: Server,
      action: () => console.log('Backup status')
    },
    {
      title: 'Compliance Report',
      description: 'Generate compliance reports',
      icon: Shield,
      action: () => console.log('Compliance report')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Security" />
      
      {/* Hero Section */}
      <section className="pt-24 pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-6">
              <div className="p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <Shield className="w-12 h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-4xl font-bold text-gray-900 dark:text-white mb-6">
              Data Security
            </h1>
            <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
              Enterprise-grade data security with comprehensive encryption, secure storage, 
              and compliance features to protect your school's sensitive information.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700'
                  }`}
                >
                  <Icon className="w-5 h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-4">
                            <Icon className="w-6 h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-4 h-4 text-teal-500 mr-2 flex-shrink-0" />
                              {benefit}
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
                <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3">
                            <Icon className="w-5 h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'encryption' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Data Encryption
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Encryption at Rest
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• AES-256 encryption for all stored data</li>
                      <li>• Database-level encryption</li>
                      <li>• File system encryption</li>
                      <li>• Backup encryption</li>
                      <li>• Key management system</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Encryption in Transit
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• TLS 1.3 for all communications</li>
                      <li>• HTTPS for web access</li>
                      <li>• API encryption</li>
                      <li>• Secure file transfers</li>
                      <li>• Certificate management</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'storage' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Data Storage Security
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Storage Infrastructure
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Enterprise-grade data centers</li>
                      <li>• Geographic redundancy</li>
                      <li>• Automated failover</li>
                      <li>• Data integrity monitoring</li>
                      <li>• Physical security measures</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Access Controls
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Role-based access control</li>
                      <li>• Multi-factor authentication</li>
                      <li>• IP-based restrictions</li>
                      <li>• Session management</li>
                      <li>• Audit logging</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'transmission' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Data Transmission Security
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Network Security
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• DDoS protection</li>
                      <li>• Web application firewall</li>
                      <li>• Intrusion detection</li>
                      <li>• Network monitoring</li>
                      <li>• Traffic analysis</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      API Security
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• API key authentication</li>
                      <li>• Rate limiting</li>
                      <li>• Request validation</li>
                      <li>• CORS protection</li>
                      <li>• API monitoring</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'backup' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Backup & Recovery
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Backup Strategy
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Daily automated backups</li>
                      <li>• Incremental backup system</li>
                      <li>• Cross-region replication</li>
                      <li>• Backup encryption</li>
                      <li>• Backup verification</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Disaster Recovery
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Point-in-time recovery</li>
                      <li>• Automated failover</li>
                      <li>• Recovery testing</li>
                      <li>• Business continuity</li>
                      <li>• Recovery time objectives</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'compliance' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-8">
              <h3 className="text-2xl font-bold text-gray-900 dark:text-white mb-6">
                Compliance & Governance
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Data Protection
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• GDPR compliance tools</li>
                      <li>• FERPA compliance support</li>
                      <li>• Data retention policies</li>
                      <li>• Privacy controls</li>
                      <li>• Consent management</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Compliance Features
                    </h4>
                    <ul className="space-y-2 text-gray-600 dark:text-gray-400">
                      <li>• Compliance reporting</li>
                      <li>• Audit trail maintenance</li>
                      <li>• Data subject rights</li>
                      <li>• Breach notification</li>
                      <li>• Compliance monitoring</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 text-white py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-3xl font-bold mb-4">
            Ready to Secure Your Data?
          </h2>
          <p className="text-xl mb-8 text-teal-100">
            Implement enterprise-grade data security to protect your school's sensitive information.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 