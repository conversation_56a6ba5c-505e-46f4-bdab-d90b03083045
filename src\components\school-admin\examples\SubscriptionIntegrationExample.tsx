'use client';

import React from 'react';
import { 
  SubscriptionAwareWrapper, 
  FeatureCard, 
  SubscriptionBanner, 
  UsageLimit 
} from '@/components/school-admin/SubscriptionAwareWrapper';
import  FeatureButton  from '@/components/subscription/FeatureButton';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/Button';
import { 
  Users, 
  FileText, 
  BarChart3, 
  Download, 
  Upload,
  Crown,
  AlertTriangle
} from 'lucide-react';

/**
 * Exemple d'intégration des composants subscription-aware dans une page school-admin
 * Ce composant montre comment utiliser les différents wrappers et composants
 */
export default function SubscriptionIntegrationExample() {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-bold text-gray-900">
        Exemple d'Intégration Subscription-Aware
      </h1>
      
      {/* Banner d'information sur l'abonnement */}
      <SubscriptionBanner
        currentPlan="free"
        requiredPlan="premium"
        featureName="Rapports avancés"
        onUpgrade={() => console.log('Upgrade clicked')}
      />

      {/* Grille de fonctionnalités avec différents niveaux d'accès */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        
        {/* Fonctionnalité gratuite - toujours accessible */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="w-5 h-5 mr-2" />
              Gestion des étudiants
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-gray-600 mb-4">
              Fonctionnalité de base incluse dans tous les plans
            </p>
            <Button className="w-full">
              Gérer les étudiants
            </Button>
          </CardContent>
        </Card>

        {/* Fonctionnalité Premium avec FeatureCard */}
        <FeatureCard
          title="Rapports avancés"
          description="Générez des rapports détaillés avec analyses statistiques"
          featureId="advanced_reports"
          requiredPlan="premium"
          icon={<BarChart3 className="w-5 h-5" />}
        >
          <div className="space-y-3">
            <Button className="w-full">
              Générer rapport de performance
            </Button>
            <Button variant="outline" className="w-full">
              Exporter données
            </Button>
          </div>
        </FeatureCard>

        {/* Fonctionnalité Enterprise avec wrapper personnalisé */}
        <SubscriptionAwareWrapper
          featureId="bulk_operations"
          requiredPlan="enterprise"
          fallbackComponent={
            <Card className="relative border-dashed border-gray-300">
              <div className="absolute inset-0 bg-gray-50 bg-opacity-80 flex items-center justify-center z-10">
                <div className="text-center p-4">
                  <Crown className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                  <p className="text-sm text-gray-600 mb-3">
                    Fonctionnalité Enterprise
                  </p>
                  <FeatureButton
                    featureId="bulk_operations"
                    size="sm"
                  >
                    Mettre à niveau
                  </FeatureButton>
                </div>
              </div>
              <CardHeader className="opacity-50">
                <CardTitle className="flex items-center">
                  <Upload className="w-5 h-5 mr-2" />
                  Opérations en lot
                </CardTitle>
              </CardHeader>
              <CardContent className="opacity-50">
                <p className="text-gray-600 mb-4">
                  Importez et exportez des données en masse
                </p>
                <div className="space-y-2">
                  <div className="h-8 bg-gray-100 rounded"></div>
                  <div className="h-8 bg-gray-100 rounded"></div>
                </div>
              </CardContent>
            </Card>
          }
        >
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Upload className="w-5 h-5 mr-2" />
                Opérations en lot
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-600 mb-4">
                Importez et exportez des données en masse
              </p>
              <div className="space-y-2">
                <Button className="w-full">
                  <Upload className="w-4 h-4 mr-2" />
                  Importer étudiants
                </Button>
                <Button variant="outline" className="w-full">
                  <Download className="w-4 h-4 mr-2" />
                  Exporter données
                </Button>
              </div>
            </CardContent>
          </Card>
        </SubscriptionAwareWrapper>
      </div>

      {/* Section des limites d'utilisation */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">
          Limites d'utilisation
        </h2>
        
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <UsageLimit
            current={45}
            limit={50}
            label="Étudiants"
            featureId="student_limit"
            className="border-yellow-200"
          />
          
          <UsageLimit
            current={8}
            limit={10}
            label="Classes"
            featureId="class_limit"
            className="border-green-200"
          />
          
          <UsageLimit
            current={100}
            limit={100}
            label="Rapports mensuels"
            featureId="report_limit"
            className="border-red-200"
          />
        </div>
      </div>

      {/* Section des boutons d'action avec gestion des permissions */}
      <div className="space-y-4">
        <h2 className="text-xl font-semibold text-gray-900">
          Actions avec contrôle d'accès
        </h2>
        
        <div className="flex flex-wrap gap-3">
          {/* Bouton toujours disponible */}
          <Button>
            Action de base
          </Button>
          
          {/* Bouton avec contrôle d'accès Premium */}
          <FeatureButton
            featureId="advanced_analytics"
            className="bg-purple-600 hover:bg-purple-700"
          >
            <BarChart3 className="w-4 h-4 mr-2" />
            Analyses avancées
          </FeatureButton>

          {/* Bouton avec contrôle d'accès Enterprise */}
          <FeatureButton
            featureId="api_access"
            variant="outline"
            className="border-gold-300 text-gold-700 hover:bg-gold-50"
          >
            <FileText className="w-4 h-4 mr-2" />
            Accès API
          </FeatureButton>
        </div>
      </div>

      {/* Section d'information sur l'intégration */}
      <Card className="border-blue-200 bg-blue-50">
        <CardHeader>
          <CardTitle className="flex items-center text-blue-800">
            <AlertTriangle className="w-5 h-5 mr-2" />
            Guide d'intégration
          </CardTitle>
        </CardHeader>
        <CardContent className="text-blue-700">
          <div className="space-y-2 text-sm">
            <p><strong>SubscriptionAwareWrapper:</strong> Wrapper générique pour contrôler l'accès</p>
            <p><strong>FeatureCard:</strong> Card avec gestion automatique des fonctionnalités premium</p>
            <p><strong>FeatureButton:</strong> Bouton avec contrôle d'accès intégré</p>
            <p><strong>UsageLimit:</strong> Affichage des limites avec actions d'upgrade</p>
            <p><strong>SubscriptionBanner:</strong> Banner d'information sur les mises à niveau</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
