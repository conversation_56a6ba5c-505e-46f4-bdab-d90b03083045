"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Crown, 
  Zap, 
  Star, 
  CheckCircle, 
  AlertTriangle, 
  Calendar,
  CreditCard,
  ArrowRight,
  Settings,
  TrendingUp,
  Users,
  BarChart3
} from 'lucide-react';
import { useSubscription } from '@/hooks/useSubscription';
import { useTranslation } from '@/hooks/useTranslation';

interface SubscriptionStatusProps {
  showDetails?: boolean;
  showUpgradeButton?: boolean;
  className?: string;
  compact?: boolean;
}

/**
 * SubscriptionStatus Component
 * Displays current subscription information and status
 */
const SubscriptionStatus: React.FC<SubscriptionStatusProps> = ({
  showDetails = true,
  showUpgradeButton = true,
  className = '',
  compact = false
}) => {
  const { t } = useTranslation();
  const { subscription, loading, error, upgradeSubscription } = useSubscription();
  const [showFeatures, setShowFeatures] = useState(false);

  const getSubscriptionIcon = (planType: string) => {
    switch (planType) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-6 h-6 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-6 h-6 text-blue-500" />;
      default:
        return <Star className="w-6 h-6 text-gray-500" />;
    }
  };

  const getSubscriptionColor = (planType: string) => {
    switch (planType) {
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      case 'enterprise':
        return 'from-purple-500 to-pink-500';
      case 'standard':
        return 'from-blue-500 to-cyan-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
        return 'text-green-600 bg-green-100';
      case 'suspended':
        return 'text-yellow-600 bg-yellow-100';
      case 'inactive':
        return 'text-red-600 bg-red-100';
      default:
        return 'text-gray-600 bg-gray-100';
    }
  };

  const getPlanFeatures = (planType: string) => {
    const features = {
      basic: [
        'Student & Staff Management',
        'Basic Academic Records',
        'Class Management',
        'Basic Announcements'
      ],
      standard: [
        'All Basic Features',
        'Advanced Analytics',
        'Bulk Operations',
        'Financial Tracking',
        'Timetable Management'
      ],
      premium: [
        'All Standard Features',
        'ID Card Generation',
        'Report Cards',
        'Advanced Reporting',
        'Data Export',
        'Priority Support'
      ],
      enterprise: [
        'All Premium Features',
        'Custom Features',
        'API Access',
        'Dedicated Support',
        'Custom Integrations'
      ]
    };
    return features[planType as keyof typeof features] || features.basic;
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg h-24 w-full"></div>
      </div>
    );
  }

  if (error || !subscription) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-red-500" />
          <span className="text-red-700">
            {error || t('subscription.not_found')}
          </span>
        </div>
      </div>
    );
  }

  if (compact) {
    return (
      <div className={`flex items-center space-x-3 p-3 bg-white rounded-lg border ${className}`}>
        {getSubscriptionIcon(subscription.plan_type)}
        <div className="flex-1">
          <div className="flex items-center space-x-2">
            <span className="font-medium capitalize">{subscription.plan_type}</span>
            <span className={`px-2 py-1 text-xs rounded-full ${getStatusColor(subscription.status)}`}>
              {subscription.status}
            </span>
          </div>
          <div className="text-sm text-gray-500">
            {subscription.credits_balance} credits
          </div>
        </div>
        {showUpgradeButton && subscription.plan_type !== 'enterprise' && (
          <button
            onClick={() => upgradeSubscription('premium')}
            className="text-blue-600 hover:text-blue-700 text-sm font-medium"
          >
            Upgrade
          </button>
        )}
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-xl shadow-sm border border-gray-200 overflow-hidden ${className}`}>
      {/* Header */}
      <div className={`p-6 bg-gradient-to-r ${getSubscriptionColor(subscription.plan_type)}`}>
        <div className="flex items-center justify-between text-white">
          <div className="flex items-center space-x-3">
            {getSubscriptionIcon(subscription.plan_type)}
            <div>
              <h3 className="text-xl font-bold capitalize">
                {subscription.plan_type} Plan
              </h3>
              <div className="flex items-center space-x-2 mt-1">
                <span className={`px-2 py-1 text-xs rounded-full bg-white bg-opacity-20`}>
                  {subscription.status}
                </span>
                {subscription.status === 'active' && (
                  <CheckCircle className="w-4 h-4" />
                )}
              </div>
            </div>
          </div>
          
          {showUpgradeButton && subscription.plan_type !== 'enterprise' && (
            <motion.button
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={() => upgradeSubscription('premium')}
              className="bg-white bg-opacity-20 hover:bg-opacity-30 px-4 py-2 rounded-lg font-medium transition-all duration-200"
            >
              Upgrade
            </motion.button>
          )}
        </div>
      </div>

      {showDetails && (
        <div className="p-6">
          {/* Stats */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <CreditCard className="w-5 h-5 text-blue-500" />
              <div>
                <div className="text-sm text-gray-600">Credits Balance</div>
                <div className="font-semibold">{subscription.credits_balance.toLocaleString()}</div>
              </div>
            </div>
            
            <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
              <Users className="w-5 h-5 text-green-500" />
              <div>
                <div className="text-sm text-gray-600">Features</div>
                <div className="font-semibold">{subscription.features.length}</div>
              </div>
            </div>
            
            {subscription.expires_at && (
              <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                <Calendar className="w-5 h-5 text-purple-500" />
                <div>
                  <div className="text-sm text-gray-600">Expires</div>
                  <div className="font-semibold">
                    {new Date(subscription.expires_at).toLocaleDateString()}
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Features */}
          <div className="mb-4">
            <button
              onClick={() => setShowFeatures(!showFeatures)}
              className="flex items-center justify-between w-full p-3 bg-gray-50 rounded-lg hover:bg-gray-100 transition-colors"
            >
              <span className="font-medium">Plan Features</span>
              <motion.div
                animate={{ rotate: showFeatures ? 180 : 0 }}
                transition={{ duration: 0.2 }}
              >
                <ArrowRight className="w-4 h-4" />
              </motion.div>
            </button>
            
            <AnimatePresence>
              {showFeatures && (
                <motion.div
                  initial={{ opacity: 0, height: 0 }}
                  animate={{ opacity: 1, height: 'auto' }}
                  exit={{ opacity: 0, height: 0 }}
                  className="mt-3"
                >
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                    {getPlanFeatures(subscription.plan_type).map((feature, index) => (
                      <div key={index} className="flex items-center space-x-2 p-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0" />
                        <span className="text-sm text-gray-700">{feature}</span>
                      </div>
                    ))}
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>

          {/* Actions */}
          <div className="flex flex-wrap gap-3">
            <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
              <Settings className="w-4 h-4" />
              <span>Manage Plan</span>
            </button>
            
            <button className="flex items-center space-x-2 px-4 py-2 bg-gray-100 hover:bg-gray-200 rounded-lg transition-colors">
              <BarChart3 className="w-4 h-4" />
              <span>Usage Analytics</span>
            </button>
            
            {subscription.plan_type !== 'enterprise' && (
              <button
                onClick={() => upgradeSubscription('premium')}
                className="flex items-center space-x-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                <TrendingUp className="w-4 h-4" />
                <span>Upgrade Plan</span>
              </button>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SubscriptionStatus;
