Stack trace:
Frame         Function      Args
0007FFFF9DC0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8CC0) msys-2.0.dll+0x1FEBA
0007FFFF9DC0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA098) msys-2.0.dll+0x67F9
0007FFFF9DC0  000210046832 (000210285FF9, 0007FFFF9C78, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DC0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DC0  0002100690B4 (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0A0  00021006A49D (0007FFFF9DD0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFC65100000 ntdll.dll
7FFC63CD0000 KERNEL32.DLL
7FFC62250000 KERNELBASE.dll
7FFC64EF0000 USER32.dll
7FFC62B00000 win32u.dll
7FFC64C10000 GDI32.dll
000210040000 msys-2.0.dll
7FFC627C0000 gdi32full.dll
7FFC62A50000 msvcp_win.dll
7FFC62BD0000 ucrtbase.dll
7FFC62F70000 advapi32.dll
7FFC63030000 msvcrt.dll
7FFC63930000 sechost.dll
7FFC64CD0000 RPCRT4.dll
7FFC61760000 CRYPTBASE.DLL
7FFC62B30000 bcryptPrimitives.dll
7FFC63AE0000 IMM32.DLL
