const mongoose = require('mongoose');
const FeatureRegistry = require('../models/FeatureRegistry');
require('dotenv').config();

// Connect to MongoDB
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/scholarify');
    console.log('✅ Connected to MongoDB');
  } catch (error) {
    console.error('❌ MongoDB connection error:', error);
    process.exit(1);
  }
};

// Feature definitions based on Phase 1 audit
const features = [
  // Dashboard Features
  {
    feature_id: 'dashboard_stats_overview',
    name: 'Dashboard Statistics Overview',
    description: 'Display key metrics including teacher count, credit balance, and student count',
    module: 'dashboard',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/school/:id/stats', description: 'Get school statistics' }
    ],
    ui_components: [
      { component_name: 'StatsOverview', component_path: 'components/widgets/StatsOverview' }
    ]
  },
  {
    feature_id: 'dashboard_top_classes_chart',
    name: 'Top Classes Analytics Chart',
    description: 'Visual representation of class performance and enrollment analytics',
    module: 'dashboard',
    category: 'analytics',
    subscription_level: 'standard',
    ui_components: [
      { component_name: 'TopClassesChart', component_path: 'components/utils/TopClassesChart' }
    ]
  },
  {
    feature_id: 'dashboard_recent_announcements',
    name: 'Recent Announcements Widget',
    description: 'Display latest school announcements on dashboard',
    module: 'dashboard',
    category: 'core',
    subscription_level: 'basic',
    ui_components: [
      { component_name: 'RecentAnnouncements', component_path: 'components/widgets/RecentAnnouncements' }
    ]
  },

  // Student Management Features
  {
    feature_id: 'students_crud_operations',
    name: 'Student CRUD Operations',
    description: 'Create, read, update, and delete student records',
    module: 'students',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/student/get-students-by-school/:schoolId', description: 'Get students by school' },
      { method: 'POST', path: '/api/student/register-students', description: 'Register new student' },
      { method: 'PUT', path: '/api/student/update-student/:id', description: 'Update student' },
      { method: 'DELETE', path: '/api/student/delete-student/:id', description: 'Delete student' }
    ],
    permissions_required: [
      { module: 'students', permission: 'view_all_students' },
      { module: 'students', permission: 'add_edit_delete_students' }
    ]
  },
  {
    feature_id: 'students_bulk_operations',
    name: 'Student Bulk Operations',
    description: 'Import students via CSV and bulk delete operations',
    module: 'students',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'POST', path: '/api/student/import-csv-students/:schoolId', description: 'Import students from CSV' },
      { method: 'DELETE', path: '/api/student/delete-multiple-students', description: 'Delete multiple students' }
    ]
  },
  {
    feature_id: 'students_id_card_generation',
    name: 'Student ID Card Generation',
    description: 'Generate and print student ID cards with QR codes',
    module: 'students',
    category: 'advanced',
    subscription_level: 'premium',
    ui_components: [
      { component_name: 'IDCardPrinter', component_path: 'components/utils/IDCardPrinter' }
    ],
    permissions_required: [
      { module: 'students', permission: 'generate_id_cards' }
    ]
  },
  {
    feature_id: 'students_report_card_generation',
    name: 'Student Report Card Generation',
    description: 'Generate comprehensive student report cards',
    module: 'students',
    category: 'advanced',
    subscription_level: 'premium',
    permissions_required: [
      { module: 'students', permission: 'generate_report_cards' }
    ]
  },

  // Staff Management Features
  {
    feature_id: 'staff_crud_operations',
    name: 'Staff CRUD Operations',
    description: 'Create, read, update, and delete staff accounts',
    module: 'staff',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/staff/get-staff-by-school/:schoolId', description: 'Get staff by school' },
      { method: 'POST', path: '/api/staff/create-staff', description: 'Create staff account' },
      { method: 'PUT', path: '/api/staff/update-staff/:id', description: 'Update staff' },
      { method: 'DELETE', path: '/api/staff/delete-staff/:id', description: 'Delete staff' }
    ],
    permissions_required: [
      { module: 'staff', permission: 'view_staff_list' },
      { module: 'staff', permission: 'add_edit_delete_staff' }
    ]
  },
  {
    feature_id: 'staff_permission_management',
    name: 'Staff Permission Management',
    description: 'Assign and manage granular staff permissions',
    module: 'staff',
    category: 'enhanced',
    subscription_level: 'standard',
    permissions_required: [
      { module: 'staff', permission: 'manage_staff_permissions' }
    ]
  },
  {
    feature_id: 'staff_password_management',
    name: 'Staff Password Management',
    description: 'Reset staff passwords and generate access codes',
    module: 'staff',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'POST', path: '/api/staff/reset-password/:id', description: 'Reset staff password' },
      { method: 'POST', path: '/api/staff/generate-access-code/:id', description: 'Generate access code' }
    ],
    permissions_required: [
      { module: 'staff', permission: 'reset_staff_passwords' }
    ]
  },

  // Class Management Features
  {
    feature_id: 'classes_crud_operations',
    name: 'Class CRUD Operations',
    description: 'Create, read, update, and delete class records',
    module: 'classes',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/class/get-classes-by-school/:schoolId', description: 'Get classes by school' },
      { method: 'POST', path: '/api/class/create-class', description: 'Create class' },
      { method: 'PUT', path: '/api/class/update-class/:id', description: 'Update class' },
      { method: 'DELETE', path: '/api/class/delete-class/:id', description: 'Delete class' }
    ],
    permissions_required: [
      { module: 'classes', permission: 'view_all_classes' },
      { module: 'classes', permission: 'add_edit_delete_classes' }
    ]
  },
  {
    feature_id: 'class_level_management',
    name: 'Class Level Management',
    description: 'Create and manage class levels and hierarchical organization',
    module: 'classes',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/class-level/get-class-levels-by-school/:schoolId', description: 'Get class levels' },
      { method: 'POST', path: '/api/class-level/create-class-level', description: 'Create class level' }
    ]
  },

  // Academic Records Features
  {
    feature_id: 'grades_entry_management',
    name: 'Grade Entry and Management',
    description: 'Enter, update, and manage student grades by subject and term',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/grades/get-grades', description: 'Get grade records' },
      { method: 'POST', path: '/api/grades/create-grade', description: 'Create grade' },
      { method: 'PUT', path: '/api/grades/update-grade/:id', description: 'Update grade' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'view_all_school_grades' },
      { module: 'academic_records', permission: 'enter_edit_grades_assigned_classes' }
    ]
  },
  {
    feature_id: 'grades_export',
    name: 'Grade Export Capabilities',
    description: 'Export grades to PDF and Excel formats',
    module: 'academic_records',
    category: 'advanced',
    subscription_level: 'premium',
    api_endpoints: [
      { method: 'GET', path: '/api/grades/school/:school_id/export/pdf', description: 'Export grades to PDF' },
      { method: 'GET', path: '/api/grades/school/:school_id/export/excel', description: 'Export grades to Excel' }
    ]
  },
  {
    feature_id: 'attendance_recording',
    name: 'Attendance Recording',
    description: 'Mark and manage student attendance with multiple status options',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'POST', path: '/api/attendance/create-attendance', description: 'Create attendance record' },
      { method: 'POST', path: '/api/attendance/create-or-update-attendance', description: 'Batch attendance update' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'take_attendance_assigned_classes' },
      { module: 'academic_records', permission: 'view_all_attendance' }
    ]
  },
  {
    feature_id: 'attendance_analytics',
    name: 'Attendance Analytics',
    description: 'Attendance statistics, rates, and trend analysis',
    module: 'academic_records',
    category: 'analytics',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'GET', path: '/api/attendance/school/:school_id/stats', description: 'Get attendance statistics' }
    ]
  },

  // Financial Management Features
  {
    feature_id: 'fee_structure_management',
    name: 'Fee Structure Management',
    description: 'Create and manage school fee types and amounts',
    module: 'financial',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'GET', path: '/api/fees/get-fees-by-school/:schoolId', description: 'Get school fees' },
      { method: 'POST', path: '/api/fees/create-fee', description: 'Create fee type' }
    ],
    permissions_required: [
      { module: 'financials', permission: 'manage_fee_types' }
    ]
  },
  {
    feature_id: 'fee_payment_processing',
    name: 'Fee Payment Processing',
    description: 'Record and process student fee payments with installment support',
    module: 'financial',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'POST', path: '/api/fee-payment/create-fee-payment', description: 'Process fee payment' }
    ],
    permissions_required: [
      { module: 'financials', permission: 'record_fee_payments' }
    ]
  },
  {
    feature_id: 'credit_purchase_system',
    name: 'Credit Purchase System',
    description: 'Purchase school credits through integrated payment gateway',
    module: 'financial',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'POST', path: '/api/credit-purchase/initiate-purchase', description: 'Initiate credit purchase' }
    ]
  },

  // Communication Features
  {
    feature_id: 'announcements_crud',
    name: 'Announcement CRUD Operations',
    description: 'Create, read, update, and delete school announcements',
    module: 'communication',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/announcement/get-announcements-by-school/:schoolId', description: 'Get announcements' },
      { method: 'POST', path: '/api/announcement/create-announcement', description: 'Create announcement' },
      { method: 'PUT', path: '/api/announcement/update-announcement/:id', description: 'Update announcement' },
      { method: 'DELETE', path: '/api/announcement/delete-announcement/:id', description: 'Delete announcement' }
    ],
    permissions_required: [
      { module: 'communications', permission: 'view_announcements' },
      { module: 'communications', permission: 'create_edit_announcements' }
    ]
  },
  {
    feature_id: 'announcements_targeted_messaging',
    name: 'Targeted Announcement Messaging',
    description: 'Send announcements to specific audiences with priority levels',
    module: 'communication',
    category: 'enhanced',
    subscription_level: 'standard',
    permissions_required: [
      { module: 'communications', permission: 'publish_announcements' }
    ]
  },
  {
    feature_id: 'announcements_notification_system',
    name: 'Announcement Notification System',
    description: 'Automated notifications with delivery tracking',
    module: 'communication',
    category: 'advanced',
    subscription_level: 'premium'
  },

  // Timetable and Scheduling Features
  {
    feature_id: 'timetable_management',
    name: 'Timetable Management',
    description: 'Create and manage class schedules with teacher assignments',
    module: 'academic_records',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'POST', path: '/api/timetable/create-schedule-entry/:school_id', description: 'Create schedule entry' },
      { method: 'GET', path: '/api/timetable/get-schedule/:school_id', description: 'Get school schedule' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_timetables' }
    ]
  },
  {
    feature_id: 'period_management',
    name: 'Period Management',
    description: 'Define class periods with start and end times',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/periods/get-periods-by-school/:schoolId', description: 'Get periods' },
      { method: 'POST', path: '/api/periods/create-period', description: 'Create period' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_periods' }
    ]
  },

  // Reports and Analytics Features
  {
    feature_id: 'usage_analytics',
    name: 'Usage Analytics Dashboard',
    description: 'Credit usage charts and subscription analytics',
    module: 'reports',
    category: 'analytics',
    subscription_level: 'premium',
    ui_components: [
      { component_name: 'UsageChart', component_path: 'components/analytics/UsageChart' },
      { component_name: 'SubscriptionOverview', component_path: 'components/analytics/SubscriptionOverview' }
    ]
  },
  {
    feature_id: 'advanced_reporting',
    name: 'Advanced Reporting System',
    description: 'Comprehensive reports with PDF and Excel export',
    module: 'reports',
    category: 'advanced',
    subscription_level: 'premium',
    permissions_required: [
      { module: 'reports', permission: 'generate_student_reports' },
      { module: 'reports', permission: 'export_data' }
    ]
  },

  // Administrative Features
  {
    feature_id: 'school_profile_management',
    name: 'School Profile Management',
    description: 'Edit school information and configuration',
    module: 'administration',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/school/get-school/:id', description: 'Get school information' },
      { method: 'PUT', path: '/api/school/update-school/:id', description: 'Update school information' }
    ],
    permissions_required: [
      { module: 'school_management', permission: 'view_school_info' },
      { module: 'school_management', permission: 'edit_school_info' }
    ]
  },
  {
    feature_id: 'settings_management',
    name: 'Settings Management',
    description: 'User preferences and system configuration',
    module: 'administration',
    category: 'core',
    subscription_level: 'basic',
    ui_components: [
      { component_name: 'ProfileSettings', component_path: 'components/settings/ProfileSettings' },
      { component_name: 'GeneralSettings', component_path: 'components/settings/GeneralSettings' }
    ]
  },
  {
    feature_id: 'parent_management',
    name: 'Parent Management System',
    description: 'Register and manage parent accounts with student associations',
    module: 'administration',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/user/get-parents', description: 'Get parent accounts' },
      { method: 'POST', path: '/api/user/register-parent', description: 'Register parent account' }
    ]
  },
  {
    feature_id: 'justification_system',
    name: 'Absence Justification System',
    description: 'Manage absence justifications with review workflow',
    module: 'administration',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'POST', path: '/api/justification/create-justification', description: 'Create justification' },
      { method: 'PUT', path: '/api/justification/review-justification/:id', description: 'Review justification' }
    ]
  },

  // Subject and Term Management
  {
    feature_id: 'subject_management',
    name: 'Subject Management',
    description: 'Create and manage academic subjects',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/subject/get-subjects-by-school/:schoolId', description: 'Get subjects' },
      { method: 'POST', path: '/api/subject/create-subject', description: 'Create subject' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_subjects' }
    ]
  },
  {
    feature_id: 'term_management',
    name: 'Academic Term Management',
    description: 'Create and manage academic terms with sequences',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/terms/get-terms-by-school/:schoolId', description: 'Get terms' },
      { method: 'POST', path: '/api/terms/create-term', description: 'Create term' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_terms' }
    ]
  },
  {
    feature_id: 'exam_type_management',
    name: 'Exam Type Management',
    description: 'Define and manage examination types and categories',
    module: 'academic_records',
    category: 'core',
    subscription_level: 'basic',
    api_endpoints: [
      { method: 'GET', path: '/api/exam/get-exam-types-by-school/:schoolId', description: 'Get exam types' },
      { method: 'POST', path: '/api/exam/create-exam-type', description: 'Create exam type' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_exam_types' }
    ]
  },
  {
    feature_id: 'discipline_management',
    name: 'Disciplinary Management',
    description: 'Record and track disciplinary actions',
    module: 'academic_records',
    category: 'enhanced',
    subscription_level: 'standard',
    api_endpoints: [
      { method: 'GET', path: '/api/discipline/get-discipline-records/:schoolId', description: 'Get discipline records' },
      { method: 'POST', path: '/api/discipline/create-discipline-record', description: 'Create discipline record' }
    ],
    permissions_required: [
      { module: 'academic_records', permission: 'manage_discipline' }
    ]
  }
];

// Seed the feature registry
const seedFeatureRegistry = async () => {
  try {
    console.log('🌱 Starting feature registry seeding...');
    
    // Clear existing features (optional - remove in production)
    await FeatureRegistry.deleteMany({});
    console.log('🗑️  Cleared existing features');
    
    // Insert new features
    const insertedFeatures = await FeatureRegistry.insertMany(features);
    console.log(`✅ Successfully seeded ${insertedFeatures.length} features`);
    
    // Display summary
    const summary = await FeatureRegistry.aggregate([
      {
        $group: {
          _id: { module: '$module', subscription_level: '$subscription_level' },
          count: { $sum: 1 }
        }
      },
      {
        $sort: { '_id.module': 1, '_id.subscription_level': 1 }
      }
    ]);
    
    console.log('\n📊 Feature Summary:');
    summary.forEach(item => {
      console.log(`  ${item._id.module} (${item._id.subscription_level}): ${item.count} features`);
    });
    
  } catch (error) {
    console.error('❌ Error seeding feature registry:', error);
  } finally {
    await mongoose.connection.close();
    console.log('🔌 Database connection closed');
  }
};

// Run the seeding script
if (require.main === module) {
  connectDB().then(seedFeatureRegistry);
}

module.exports = { seedFeatureRegistry, features };
