const express = require('express');
const featureRegistryController = require('../controllers/featureRegistryController');
const { authenticate, authorize } = require('../middleware/middleware');
const SubscriptionMiddleware = require('../middleware/subscriptionMiddleware');

const router = express.Router();

/**
 * Feature Registry Routes
 * Provides endpoints for managing and accessing the feature registry
 */

// Public routes (with authentication)
/**
 * @route GET /api/feature-registry/subscription/:level
 * @desc Get features available for a specific subscription level
 * @access Private (All authenticated users)
 */
router.get('/subscription/:level', 
  authenticate, 
  featureRegistryController.getFeaturesBySubscriptionLevel
);

/**
 * @route GET /api/feature-registry/grouped
 * @desc Get features grouped by module for a subscription level
 * @access Private (All authenticated users)
 */
router.get('/grouped', 
  authenticate, 
  featureRegistryController.getFeaturesByModuleGrouped
);

/**
 * @route GET /api/feature-registry/check/:featureId/school/:schoolId
 * @desc Check if a school has access to a specific feature
 * @access Private (School admins, Super admins)
 */
router.get('/check/:featureId/school/:schoolId', 
  authenticate, 
  authorize(['super', 'admin', 'school_admin', 'dean_of_studies', 'bursar']),
  featureRegistryController.checkSchoolFeatureAccess
);

/**
 * @route GET /api/feature-registry/:featureId
 * @desc Get detailed information about a specific feature
 * @access Private (All authenticated users)
 */
router.get('/:featureId', 
  authenticate, 
  featureRegistryController.getFeatureById
);

/**
 * @route GET /api/feature-registry/statistics
 * @desc Get feature registry statistics
 * @access Private (Super admins, School admins)
 */
router.get('/statistics', 
  authenticate, 
  authorize(['super', 'admin', 'school_admin']),
  featureRegistryController.getFeatureStatistics
);

// Super Admin only routes
/**
 * @route POST /api/feature-registry
 * @desc Create a new feature in the registry
 * @access Private (Super admins only)
 */
router.post('/', 
  authenticate, 
  authorize(['super']),
  featureRegistryController.createOrUpdateFeature
);

/**
 * @route PUT /api/feature-registry/:featureId
 * @desc Update an existing feature in the registry
 * @access Private (Super admins only)
 */
router.put('/:featureId', 
  authenticate, 
  authorize(['super']),
  featureRegistryController.createOrUpdateFeature
);

/**
 * @route PATCH /api/feature-registry/:featureId/toggle
 * @desc Enable or disable a feature
 * @access Private (Super admins only)
 */
router.patch('/:featureId/toggle', 
  authenticate, 
  authorize(['super']),
  featureRegistryController.toggleFeature
);

// Cache management routes (Super Admin only)
/**
 * @route DELETE /api/feature-registry/cache
 * @desc Clear feature registry cache
 * @access Private (Super admins only)
 */
router.delete('/cache', 
  authenticate, 
  authorize(['super']),
  featureRegistryController.clearCache
);

/**
 * @route GET /api/feature-registry/cache/stats
 * @desc Get cache statistics
 * @access Private (Super admins only)
 */
router.get('/cache/stats', 
  authenticate, 
  authorize(['super']),
  featureRegistryController.getCacheStats
);

// Feature-specific middleware examples
/**
 * Example route showing how to protect an endpoint with feature-level access control
 * @route GET /api/feature-registry/example/protected
 * @desc Example of feature-protected endpoint
 * @access Private (Requires specific feature access)
 */
router.get('/example/protected', 
  authenticate,
  SubscriptionMiddleware.checkFeatureAccess('students_id_card_generation'),
  (req, res) => {
    const ResponseFormatter = require('../utils/responseFormatter');
    return ResponseFormatter.success(res, {
      message: 'You have access to this premium feature!',
      feature: req.feature,
      subscription: req.subscription
    }, 'Feature access granted');
  }
);

/**
 * Example route showing how to check multiple features (user needs access to at least one)
 * @route GET /api/feature-registry/example/any-feature
 * @desc Example of multiple feature check
 * @access Private (Requires access to any of the specified features)
 */
router.get('/example/any-feature', 
  authenticate,
  SubscriptionMiddleware.checkAnyFeatureAccess([
    'students_bulk_operations', 
    'staff_permission_management',
    'announcements_targeted_messaging'
  ]),
  (req, res) => {
    const ResponseFormatter = require('../utils/responseFormatter');
    return ResponseFormatter.success(res, {
      message: 'You have access to at least one of the required features!',
      accessible_features: req.accessibleFeatures
    }, 'Feature access granted');
  }
);

module.exports = router;
