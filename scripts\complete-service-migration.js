/**
 * Script pour compléter la migration de tous les services vers ApiInterceptorService
 * Ce script identifie et met à jour automatiquement toutes les fonctions qui utilisent encore fetch()
 */

const fs = require('fs');
const path = require('path');

// Services à migrer complètement
const servicesToMigrate = [
  'src/app/services/AnnouncementServices.tsx',
  'src/app/services/ClassServices.tsx', 
  'src/app/services/StaffServices.tsx',
  'src/app/services/SubscriptionServices.tsx',
  'src/app/services/GradeServices.tsx',
  'src/app/services/AttendanceServices.tsx',
  'src/app/services/FeesServices.tsx'
];

// Pattern pour identifier les fonctions avec fetch
const fetchPattern = /export\s+async\s+function\s+(\w+)\s*\([^)]*\)[^{]*\{[^}]*fetch\(/g;

// Template pour les fonctions migrées
const migrationTemplates = {
  GET: (endpoint, errorMessage) => `
  try {
    return await ApiInterceptorService.get('${endpoint}');
  } catch (error) {
    return handleSubscriptionError(error, "${errorMessage}");
  }`,
  
  POST: (endpoint, data, errorMessage) => `
  try {
    return await ApiInterceptorService.post('${endpoint}', ${data});
  } catch (error) {
    return handleSubscriptionError(error, "${errorMessage}");
  }`,
  
  PUT: (endpoint, data, errorMessage) => `
  try {
    return await ApiInterceptorService.put('${endpoint}', ${data});
  } catch (error) {
    return handleSubscriptionError(error, "${errorMessage}");
  }`,
  
  DELETE: (endpoint, errorMessage) => `
  try {
    return await ApiInterceptorService.delete('${endpoint}');
  } catch (error) {
    return handleSubscriptionError(error, "${errorMessage}");
  }`
};

// Fonction pour analyser un service
function analyzeService(filePath) {
  if (!fs.existsSync(filePath)) {
    console.log(`❌ Service non trouvé: ${filePath}`);
    return null;
  }

  const content = fs.readFileSync(filePath, 'utf8');
  const fetchMatches = [...content.matchAll(fetchPattern)];
  
  console.log(`📁 ${filePath}`);
  console.log(`   🔍 ${fetchMatches.length} fonction(s) avec fetch trouvée(s)`);
  
  if (fetchMatches.length > 0) {
    fetchMatches.forEach((match, index) => {
      console.log(`   📝 ${index + 1}. ${match[1]}()`);
    });
  }
  
  return {
    filePath,
    content,
    fetchCount: fetchMatches.length,
    functions: fetchMatches.map(m => m[1])
  };
}

// Fonction pour vérifier si un service a les imports nécessaires
function hasRequiredImports(content) {
  const hasApiInterceptor = content.includes('ApiInterceptorService');
  const hasErrorClasses = content.includes('SubscriptionRequiredError') && content.includes('FeatureAccessDeniedError');
  const hasHelper = content.includes('handleSubscriptionError');
  
  return { hasApiInterceptor, hasErrorClasses, hasHelper };
}

// Fonction pour ajouter les imports manquants
function addRequiredImports(content, serviceName) {
  const imports = hasRequiredImports(content);
  
  if (imports.hasApiInterceptor && imports.hasErrorClasses && imports.hasHelper) {
    return content; // Déjà complet
  }
  
  // Trouver la ligne d'import appropriée
  const lines = content.split('\n');
  let insertIndex = 0;
  
  // Chercher la dernière ligne d'import
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].startsWith('import ') || lines[i].startsWith('export ')) {
      insertIndex = i + 1;
    } else if (lines[i].trim() === '') {
      continue;
    } else {
      break;
    }
  }
  
  const newImports = [];
  
  if (!imports.hasApiInterceptor || !imports.hasErrorClasses) {
    newImports.push('import ApiInterceptorService, { SubscriptionRequiredError, FeatureAccessDeniedError } from "./ApiInterceptorService";');
  }
  
  if (!imports.hasHelper) {
    newImports.push(`
/**
 * Helper pour gérer les erreurs de subscription de manière cohérente
 */
const handleSubscriptionError = (error: any, defaultMessage: string): never => {
  console.error('${serviceName} service error:', error);
  
  if (error instanceof SubscriptionRequiredError) {
    throw new Error(\`Mise à niveau requise: \${error.subscriptionError.subscription_required}\`);
  }
  
  if (error instanceof FeatureAccessDeniedError) {
    throw new Error(\`Accès refusé: Cette fonctionnalité nécessite un abonnement \${error.subscriptionError.subscription_required}\`);
  }
  
  throw new Error(defaultMessage);
};`);
  }
  
  lines.splice(insertIndex, 0, ...newImports);
  return lines.join('\n');
}

// Fonction principale
function main() {
  console.log('🚀 Début de la migration complète des services...\n');
  
  const results = [];
  
  // Analyser tous les services
  servicesToMigrate.forEach(servicePath => {
    const analysis = analyzeService(servicePath);
    if (analysis) {
      results.push(analysis);
    }
  });
  
  console.log('\n📊 Résumé de l\'analyse:');
  console.log('================================');
  
  let totalFunctions = 0;
  results.forEach(result => {
    totalFunctions += result.fetchCount;
    const status = result.fetchCount === 0 ? '✅ Complet' : `⚠️  ${result.fetchCount} fonction(s) à migrer`;
    console.log(`${path.basename(result.filePath)}: ${status}`);
  });
  
  console.log(`\n📈 Total: ${totalFunctions} fonction(s) à migrer`);
  
  // Proposer la migration automatique
  if (totalFunctions > 0) {
    console.log('\n🔧 Pour compléter la migration:');
    console.log('1. Ajouter les imports manquants');
    console.log('2. Remplacer les appels fetch par ApiInterceptorService');
    console.log('3. Ajouter la gestion d\'erreurs subscription-aware');
    
    results.forEach(result => {
      if (result.fetchCount > 0) {
        console.log(`\n📝 ${path.basename(result.filePath)}:`);
        console.log(`   - Ajouter imports: ${!hasRequiredImports(result.content).hasApiInterceptor ? 'Oui' : 'Non'}`);
        console.log(`   - Fonctions à migrer: ${result.functions.join(', ')}`);
      }
    });
  } else {
    console.log('\n🎉 Tous les services sont déjà migrés !');
  }
  
  console.log('\n✨ Analyse terminée.');
}

// Exécuter le script
if (require.main === module) {
  main();
}

module.exports = { analyzeService, hasRequiredImports, addRequiredImports };
