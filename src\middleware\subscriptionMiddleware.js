const FeatureRegistry = require('../models/FeatureRegistry');
const SchoolSubscription = require('../models/SchoolSubscription');
const User = require('../models/User');
const ResponseFormatter = require('../utils/responseFormatter');
const NodeCache = require('node-cache');

// Cache for subscription data (TTL: 5 minutes)
const subscriptionCache = new NodeCache({ stdTTL: 300, checkperiod: 60 });

/**
 * Enhanced subscription middleware for feature-level access control
 */
class SubscriptionMiddleware {
  
  /**
   * Check if user has access to a specific feature
   * @param {string} featureId - Feature identifier to check
   * @param {Object} options - Additional options
   */
  static checkFeatureAccess(featureId, options = {}) {
    return async (req, res, next) => {
      try {
        // Extract user information
        const user = await User.findOne({ firebaseUid: req.user.uid });
        if (!user) {
          return ResponseFormatter.unauthorized(res, 'User not found');
        }
        
        // Skip check for super admins unless explicitly required
        if (user.role === 'super' && !options.enforceForSuperAdmin) {
          return next();
        }
        
        // Get school subscription information
        const schoolId = this.extractSchoolId(user, req);
        if (!schoolId) {
          return ResponseFormatter.error(res, 'School information not found', 'SCHOOL_NOT_FOUND', 400);
        }
        
        // Check feature access
        const accessResult = await this.validateFeatureAccess(featureId, schoolId, user);
        
        if (!accessResult.hasAccess) {
          return this.handleAccessDenied(res, accessResult, featureId);
        }
        
        // Add feature and subscription info to request
        req.feature = accessResult.feature;
        req.subscription = accessResult.subscription;
        req.schoolId = schoolId;
        
        next();
      } catch (error) {
        console.error('Feature access check error:', error);
        return ResponseFormatter.error(res, 'Feature access validation failed', 'FEATURE_CHECK_ERROR', 500);
      }
    };
  }
  
  /**
   * Middleware to check multiple features (user must have access to at least one)
   * @param {Array} featureIds - Array of feature identifiers
   * @param {Object} options - Additional options
   */
  static checkAnyFeatureAccess(featureIds, options = {}) {
    return async (req, res, next) => {
      try {
        const user = await User.findOne({ firebaseUid: req.user.uid });
        if (!user) {
          return ResponseFormatter.unauthorized(res, 'User not found');
        }
        
        if (user.role === 'super' && !options.enforceForSuperAdmin) {
          return next();
        }
        
        const schoolId = this.extractSchoolId(user, req);
        if (!schoolId) {
          return ResponseFormatter.error(res, 'School information not found', 'SCHOOL_NOT_FOUND', 400);
        }
        
        // Check access to any of the features
        let hasAnyAccess = false;
        let accessibleFeatures = [];
        
        for (const featureId of featureIds) {
          const accessResult = await this.validateFeatureAccess(featureId, schoolId, user);
          if (accessResult.hasAccess) {
            hasAnyAccess = true;
            accessibleFeatures.push(accessResult.feature);
          }
        }
        
        if (!hasAnyAccess) {
          return ResponseFormatter.subscriptionRequired(
            res,
            featureIds.join(', '),
            'standard', // Default required level
            'basic' // Default current level
          );
        }
        
        req.accessibleFeatures = accessibleFeatures;
        req.schoolId = schoolId;
        
        next();
      } catch (error) {
        console.error('Multiple feature access check error:', error);
        return ResponseFormatter.error(res, 'Feature access validation failed', 'FEATURE_CHECK_ERROR', 500);
      }
    };
  }
  
  /**
   * Validate feature access for a specific school and user
   * @param {string} featureId - Feature identifier
   * @param {string} schoolId - School identifier
   * @param {Object} user - User object
   * @returns {Object} Access validation result
   */
  static async validateFeatureAccess(featureId, schoolId, user) {
    try {
      // Check cache first
      const cacheKey = `feature_access_${featureId}_${schoolId}`;
      const cachedResult = subscriptionCache.get(cacheKey);
      
      if (cachedResult) {
        return cachedResult;
      }
      
      // Get feature information
      const feature = await FeatureRegistry.findOne({
        feature_id: featureId,
        status: 'active',
        is_enabled_globally: true
      });
      
      if (!feature) {
        return { hasAccess: false, reason: 'feature_not_found', feature: null };
      }
      
      // Get school subscription
      const subscription = await SchoolSubscription.findOne({ school_id: schoolId });
      
      if (!subscription) {
        return { 
          hasAccess: false, 
          reason: 'no_subscription', 
          feature,
          subscription: null 
        };
      }
      
      // Check subscription level access
      const hasSubscriptionAccess = this.checkSubscriptionLevel(
        feature.subscription_level,
        subscription.plan_type
      );
      
      if (!hasSubscriptionAccess) {
        const result = {
          hasAccess: false,
          reason: 'insufficient_subscription',
          feature,
          subscription,
          required_level: feature.subscription_level,
          current_level: subscription.plan_type
        };
        
        // Cache negative result for shorter time
        subscriptionCache.set(cacheKey, result, 60);
        return result;
      }
      
      // Check feature-specific limits
      const limitsCheck = await this.checkFeatureLimits(feature, subscription, schoolId);
      
      if (!limitsCheck.allowed) {
        return {
          hasAccess: false,
          reason: 'feature_limit_exceeded',
          feature,
          subscription,
          limit_details: limitsCheck
        };
      }
      
      // Check rollout configuration
      const rolloutCheck = this.checkRolloutAccess(feature, schoolId);
      
      if (!rolloutCheck) {
        return {
          hasAccess: false,
          reason: 'feature_not_rolled_out',
          feature,
          subscription
        };
      }
      
      const result = {
        hasAccess: true,
        feature,
        subscription,
        limits: limitsCheck
      };
      
      // Cache positive result
      subscriptionCache.set(cacheKey, result, 300);
      return result;
      
    } catch (error) {
      console.error('Feature validation error:', error);
      return { hasAccess: false, reason: 'validation_error', error };
    }
  }
  
  /**
   * Check if subscription level has access to feature
   * @param {string} requiredLevel - Required subscription level
   * @param {string} currentLevel - Current subscription level
   * @returns {boolean} Has access
   */
  static checkSubscriptionLevel(requiredLevel, currentLevel) {
    const levelHierarchy = {
      'basic': 1,
      'standard': 2,
      'premium': 3,
      'enterprise': 4
    };
    
    const required = levelHierarchy[requiredLevel] || 1;
    const current = levelHierarchy[currentLevel] || 1;
    
    return current >= required;
  }
  
  /**
   * Check feature usage limits
   * @param {Object} feature - Feature object
   * @param {Object} subscription - Subscription object
   * @param {string} schoolId - School identifier
   * @returns {Object} Limits check result
   */
  static async checkFeatureLimits(feature, subscription, schoolId) {
    // Implementation for checking daily/monthly limits
    // This would typically involve checking usage statistics
    
    return {
      allowed: true,
      daily_usage: 0,
      daily_limit: feature.limits?.daily_usage || null,
      monthly_usage: 0,
      monthly_limit: feature.limits?.monthly_usage || null
    };
  }
  
  /**
   * Check rollout access for feature
   * @param {Object} feature - Feature object
   * @param {string} schoolId - School identifier
   * @returns {boolean} Has rollout access
   */
  static checkRolloutAccess(feature, schoolId) {
    if (!feature.rollout) return true;
    
    // Check if school is excluded
    if (feature.rollout.exclude_schools?.includes(schoolId)) {
      return false;
    }
    
    // Check if school is specifically targeted
    if (feature.rollout.target_schools?.length > 0) {
      return feature.rollout.target_schools.includes(schoolId);
    }
    
    // Check rollout percentage (simplified - in production, use consistent hashing)
    const rolloutPercentage = feature.rollout.percentage || 100;
    return rolloutPercentage >= 100;
  }
  
  /**
   * Extract school ID from user or request
   * @param {Object} user - User object
   * @param {Object} req - Request object
   * @returns {string|null} School ID
   */
  static extractSchoolId(user, req) {
    // Try to get from request parameters first
    if (req.params.school_id) return req.params.school_id;
    if (req.body.school_id) return req.body.school_id;
    if (req.query.school_id) return req.query.school_id;
    
    // Get from user's school associations
    if (user.school_ids && user.school_ids.length > 0) {
      return user.school_ids[0].toString();
    }
    
    return null;
  }
  
  /**
   * Handle access denied scenarios
   * @param {Object} res - Response object
   * @param {Object} accessResult - Access validation result
   * @param {string} featureId - Feature identifier
   */
  static handleAccessDenied(res, accessResult, featureId) {
    switch (accessResult.reason) {
      case 'feature_not_found':
        return ResponseFormatter.featureNotFound(res, featureId);
        
      case 'no_subscription':
        return ResponseFormatter.subscriptionRequired(res, featureId, 'basic', 'none');
        
      case 'insufficient_subscription':
        return ResponseFormatter.subscriptionRequired(
          res,
          featureId,
          accessResult.required_level,
          accessResult.current_level
        );
        
      case 'feature_limit_exceeded':
        return ResponseFormatter.error(
          res,
          'Feature usage limit exceeded',
          'FEATURE_LIMIT_EXCEEDED',
          429,
          accessResult.limit_details
        );
        
      case 'feature_not_rolled_out':
        return ResponseFormatter.featureUnavailable(res, featureId, 'rollout');
        
      default:
        return ResponseFormatter.error(res, 'Feature access denied', 'ACCESS_DENIED', 403);
    }
  }
  
  /**
   * Clear subscription cache for a specific school
   * @param {string} schoolId - School identifier
   */
  static clearSchoolCache(schoolId) {
    const keys = subscriptionCache.keys();
    const schoolKeys = keys.filter(key => key.includes(schoolId));
    schoolKeys.forEach(key => subscriptionCache.del(key));
  }
}

module.exports = SubscriptionMiddleware;
