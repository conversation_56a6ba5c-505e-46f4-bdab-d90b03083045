/**
 * Standardized API Response Formatter
 * Provides consistent response structure across all endpoints
 */

class ResponseFormatter {
  
  /**
   * Success response format
   * @param {Object} res - Express response object
   * @param {*} data - Response data
   * @param {string} message - Success message
   * @param {number} statusCode - HTTP status code
   * @param {Object} meta - Additional metadata
   */
  static success(res, data = null, message = 'Operation successful', statusCode = 200, meta = {}) {
    const response = {
      success: true,
      message,
      data,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null,
        ...meta
      }
    };
    
    return res.status(statusCode).json(response);
  }
  
  /**
   * Error response format
   * @param {Object} res - Express response object
   * @param {string} message - Error message
   * @param {string} errorCode - Specific error identifier
   * @param {number} statusCode - HTTP status code
   * @param {Object} details - Additional error details
   */
  static error(res, message = 'An error occurred', errorCode = 'GENERAL_ERROR', statusCode = 500, details = {}) {
    const response = {
      success: false,
      message,
      error_code: errorCode,
      details,
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(statusCode).json(response);
  }
  
  /**
   * Subscription access denied response
   * @param {Object} res - Express response object
   * @param {string} featureId - Feature that was blocked
   * @param {string} requiredLevel - Required subscription level
   * @param {string} currentLevel - Current subscription level
   * @param {string} upgradeUrl - URL for subscription upgrade
   */
  static subscriptionRequired(res, featureId, requiredLevel, currentLevel = 'basic', upgradeUrl = null) {
    const response = {
      success: false,
      message: `This feature requires a ${requiredLevel} subscription`,
      error_code: 'SUBSCRIPTION_REQUIRED',
      subscription_required: requiredLevel,
      current_subscription: currentLevel,
      feature_id: featureId,
      upgrade_url: upgradeUrl || `/subscription/upgrade?plan=${requiredLevel}&feature=${featureId}`,
      details: {
        blocked_feature: featureId,
        required_subscription_level: requiredLevel,
        current_subscription_level: currentLevel,
        upgrade_benefits: this.getUpgradeBenefits(currentLevel, requiredLevel)
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(403).json(response);
  }
  
  /**
   * Feature not found response
   * @param {Object} res - Express response object
   * @param {string} featureId - Feature that was not found
   */
  static featureNotFound(res, featureId) {
    const response = {
      success: false,
      message: 'The requested feature is not available',
      error_code: 'FEATURE_NOT_FOUND',
      details: {
        feature_id: featureId,
        suggestion: 'Please check the feature identifier or contact support'
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(404).json(response);
  }
  
  /**
   * Feature temporarily unavailable response
   * @param {Object} res - Express response object
   * @param {string} featureId - Feature that is unavailable
   * @param {string} reason - Reason for unavailability
   */
  static featureUnavailable(res, featureId, reason = 'maintenance') {
    const response = {
      success: false,
      message: 'This feature is temporarily unavailable',
      error_code: 'FEATURE_UNAVAILABLE',
      details: {
        feature_id: featureId,
        reason,
        estimated_availability: this.getEstimatedAvailability(reason)
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(503).json(response);
  }
  
  /**
   * Validation error response
   * @param {Object} res - Express response object
   * @param {Array|Object} validationErrors - Validation error details
   * @param {string} message - Custom validation message
   */
  static validationError(res, validationErrors, message = 'Validation failed') {
    const response = {
      success: false,
      message,
      error_code: 'VALIDATION_ERROR',
      validation_errors: Array.isArray(validationErrors) ? validationErrors : [validationErrors],
      details: {
        error_count: Array.isArray(validationErrors) ? validationErrors.length : 1
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(400).json(response);
  }
  
  /**
   * Unauthorized access response
   * @param {Object} res - Express response object
   * @param {string} message - Custom unauthorized message
   */
  static unauthorized(res, message = 'Authentication required') {
    const response = {
      success: false,
      message,
      error_code: 'UNAUTHORIZED',
      details: {
        suggestion: 'Please provide valid authentication credentials'
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(401).json(response);
  }
  
  /**
   * Forbidden access response
   * @param {Object} res - Express response object
   * @param {string} message - Custom forbidden message
   * @param {Array} requiredPermissions - Required permissions for access
   */
  static forbidden(res, message = 'Insufficient permissions', requiredPermissions = []) {
    const response = {
      success: false,
      message,
      error_code: 'FORBIDDEN',
      details: {
        required_permissions: requiredPermissions,
        suggestion: 'Contact your administrator for access'
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(403).json(response);
  }
  
  /**
   * Paginated response format
   * @param {Object} res - Express response object
   * @param {Array} data - Response data array
   * @param {Object} pagination - Pagination details
   * @param {string} message - Success message
   */
  static paginated(res, data, pagination, message = 'Data retrieved successfully') {
    const response = {
      success: true,
      message,
      data,
      pagination: {
        current_page: pagination.page || 1,
        per_page: pagination.limit || 10,
        total_items: pagination.total || 0,
        total_pages: Math.ceil((pagination.total || 0) / (pagination.limit || 10)),
        has_next_page: pagination.hasNextPage || false,
        has_prev_page: pagination.hasPrevPage || false
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: res.locals.requestId || null
      }
    };
    
    return res.status(200).json(response);
  }
  
  /**
   * Get upgrade benefits for subscription levels
   * @param {string} currentLevel - Current subscription level
   * @param {string} targetLevel - Target subscription level
   * @returns {Array} Array of benefits
   */
  static getUpgradeBenefits(currentLevel, targetLevel) {
    const benefits = {
      'basic_to_standard': [
        'Advanced analytics and reporting',
        'Bulk import/export operations',
        'Enhanced permission management',
        'Financial tracking and reporting'
      ],
      'basic_to_premium': [
        'All Standard features',
        'ID card and report card generation',
        'Advanced data export capabilities',
        'Priority customer support'
      ],
      'standard_to_premium': [
        'Document generation features',
        'Advanced analytics dashboard',
        'Premium export formats',
        'Priority support'
      ]
    };
    
    const key = `${currentLevel}_to_${targetLevel}`;
    return benefits[key] || [`Upgrade to ${targetLevel} for enhanced features`];
  }
  
  /**
   * Get estimated availability time
   * @param {string} reason - Reason for unavailability
   * @returns {string} Estimated availability
   */
  static getEstimatedAvailability(reason) {
    const estimates = {
      'maintenance': 'Within 2 hours',
      'upgrade': 'Within 24 hours',
      'rollout': 'Gradual rollout in progress',
      'beta': 'Available in beta testing'
    };
    
    return estimates[reason] || 'Please check back later';
  }
}

module.exports = ResponseFormatter;
