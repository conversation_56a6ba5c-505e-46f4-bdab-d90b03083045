"use client";

import { Shield, Lock, Eye, Database, Users, Globe, Mail, Phone } from "lucide-react";
import SharedNavigation from "@/components/layout/SharedNavigation";
import SharedFooter from "@/components/layout/SharedFooter";
import { useRouter } from "next/navigation";

export default function PrivacyPolicyPage() {
    const router = useRouter();

    const sections = [
        {
            id: "overview",
            title: "Overview",
            icon: <Shield className="w-6 h-6" />,
            content: [
                "Scholarify is committed to protecting the privacy and security of your personal information. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you use our school management platform.",
                "This policy applies to all users of Scholarify, including students, parents, teachers, and administrators. By using our services, you agree to the collection and use of information in accordance with this policy."
            ]
        },
        {
            id: "information-collection",
            title: "Information We Collect",
            icon: <Database className="w-6 h-6" />,
            content: [
                "We collect information you provide directly to us, such as when you create an account, update your profile, or communicate with us. This may include:",
                "• Personal identification information (name, email address, phone number)",
                "• School and academic information (student records, grades, attendance)",
                "• User-generated content (messages, assignments, feedback)",
                "• Technical information (IP address, browser type, device information)",
                "• Usage data (how you interact with our platform)"
            ]
        },
        {
            id: "how-we-use",
            title: "How We Use Your Information",
            icon: <Eye className="w-6 h-6" />,
            content: [
                "We use the information we collect to:",
                "• Provide and maintain our school management services",
                "• Process and manage student records and academic data",
                "• Enable communication between teachers, students, and parents",
                "• Generate reports and analytics for educational purposes",
                "• Improve our platform and develop new features",
                "• Ensure security and prevent fraud",
                "• Comply with legal obligations"
            ]
        },
        {
            id: "information-sharing",
            title: "Information Sharing and Disclosure",
            icon: <Users className="w-6 h-6" />,
            content: [
                "We do not sell, trade, or rent your personal information to third parties. We may share your information in the following circumstances:",
                "• With your consent or at your direction",
                "• With school administrators and authorized personnel",
                "• With service providers who assist in operating our platform",
                "• To comply with legal requirements or protect our rights",
                "• In connection with a business transfer or merger"
            ]
        },
        {
            id: "data-security",
            title: "Data Security",
            icon: <Lock className="w-6 h-6" />,
            content: [
                "We implement appropriate technical and organizational security measures to protect your personal information, including:",
                "• Encryption of data in transit and at rest",
                "• Regular security assessments and updates",
                "• Access controls and authentication measures",
                "• Secure data centers and infrastructure",
                "• Employee training on data protection",
                "• Incident response procedures"
            ]
        },
        {
            id: "data-retention",
            title: "Data Retention",
            icon: <Globe className="w-6 h-6" />,
            content: [
                "We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy. Specific retention periods include:",
                "• Active user accounts: Until account deletion",
                "• Student records: As required by educational regulations",
                "• System logs: Up to 12 months",
                "• Marketing communications: Until opt-out",
                "Upon request, we will delete or anonymize your personal information, subject to legal requirements."
            ]
        },
        {
            id: "your-rights",
            title: "Your Rights and Choices",
            icon: <Mail className="w-6 h-6" />,
            content: [
                "You have the following rights regarding your personal information:",
                "• Access and review your personal information",
                "• Correct inaccurate or incomplete information",
                "• Request deletion of your personal information",
                "• Opt-out of marketing communications",
                "• Control your privacy settings",
                "• Export your data in a portable format",
                "To exercise these rights, contact us using the information provided below."
            ]
        },
        {
            id: "children-privacy",
            title: "Children's Privacy",
            icon: <Users className="w-6 h-6" />,
            content: [
                "Scholarify is designed for use by educational institutions and complies with the Children's Online Privacy Protection Act (COPPA) and Family Educational Rights and Privacy Act (FERPA).",
                "We collect student information only with proper consent from parents or educational institutions. Student data is used solely for educational purposes and is protected by strict security measures.",
                "Parents have the right to review, correct, or delete their child's personal information at any time."
            ]
        },
        {
            id: "cookies",
            title: "Cookies and Tracking",
            icon: <Eye className="w-6 h-6" />,
            content: [
                "We use cookies and similar technologies to enhance your experience on our platform:",
                "• Essential cookies for platform functionality",
                "• Analytics cookies to understand usage patterns",
                "• Security cookies to protect against fraud",
                "• Preference cookies to remember your settings",
                "You can control cookie settings through your browser preferences, though this may affect platform functionality."
            ]
        },
        {
            id: "international",
            title: "International Data Transfers",
            icon: <Globe className="w-6 h-6" />,
            content: [
                "Scholarify operates globally and may transfer your information to countries other than your own. We ensure appropriate safeguards are in place for international data transfers, including:",
                "• Standard contractual clauses",
                "• Adequacy decisions",
                "• Other appropriate safeguards as required by law",
                "We comply with applicable data protection laws in all jurisdictions where we operate."
            ]
        },
        {
            id: "changes",
            title: "Changes to This Policy",
            icon: <Shield className="w-6 h-6" />,
            content: [
                "We may update this Privacy Policy from time to time to reflect changes in our practices or applicable laws. We will notify you of any material changes by:",
                "• Posting the updated policy on our platform",
                "• Sending email notifications to registered users",
                "• Displaying prominent notices on our website",
                "Your continued use of our services after such changes constitutes acceptance of the updated policy."
            ]
        },
        {
            id: "contact",
            title: "Contact Us",
            icon: <Phone className="w-6 h-6" />,
            content: [
                "If you have any questions about this Privacy Policy or our data practices, please contact us:",
                "Email: <EMAIL>",
                "Phone: +****************",
                "Address: 123 Education Street, Tech City, TC 12345",
                "We will respond to your inquiry within 30 days."
            ]
        }
    ];

    return (
        <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
            {/* Header */}
            <SharedNavigation showBackButton={true} />

            {/* Hero Section */}
            <section className="py-16 bg-gradient-to-r from-teal-600 to-teal-700 dark:from-teal-800 dark:to-teal-900">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h1 className="text-4xl md:text-5xl font-bold text-white mb-6">
                        Privacy Policy
                    </h1>
                    <p className="text-xl text-teal-100 mb-4">
                        How we protect and handle your personal information
                    </p>
                    <p className="text-teal-200">
                        Last updated: December 2024
                    </p>
                </div>
            </section>

            {/* Content */}
            <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-8">
                    <div className="space-y-12">
                        {sections.map((section, index) => (
                            <section key={section.id} className="border-b border-gray-200 dark:border-gray-700 pb-8 last:border-b-0">
                                <div className="flex items-center mb-6">
                                    <div className="text-teal-600 dark:text-teal-400 mr-3">
                                        {section.icon}
                                    </div>
                                    <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
                                        {section.title}
                                    </h2>
                                </div>
                                
                                <div className="space-y-4 text-gray-700 dark:text-gray-300 leading-relaxed">
                                    {section.content.map((paragraph, pIndex) => (
                                        <p key={pIndex} className={paragraph.startsWith('•') ? 'ml-4' : ''}>
                                            {paragraph}
                                        </p>
                                    ))}
                                </div>
                            </section>
                        ))}
                    </div>

                    {/* Additional Information */}
                    <div className="mt-12 p-6 bg-gray-50 dark:bg-gray-700 rounded-lg">
                        <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
                            Additional Resources
                        </h3>
                        <div className="grid md:grid-cols-2 gap-4">
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Related Policies
                                </h4>
                                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                                    <li>• Terms of Service</li>
                                    <li>• Cookie Policy</li>
                                    <li>• Data Processing Agreement</li>
                                    <li>• Security Policy</li>
                                </ul>
                            </div>
                            <div>
                                <h4 className="font-medium text-gray-900 dark:text-white mb-2">
                                    Compliance
                                </h4>
                                <ul className="space-y-1 text-sm text-gray-600 dark:text-gray-300">
                                    <li>• GDPR Compliance</li>
                                    <li>• FERPA Compliance</li>
                                    <li>• COPPA Compliance</li>
                                    <li>• SOC 2 Type II Certified</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            {/* Footer CTA */}
            <section className="bg-gray-100 dark:bg-gray-800 py-16">
                <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
                    <h2 className="text-3xl font-bold text-gray-900 dark:text-white mb-4">
                        Questions About Privacy?
                    </h2>
                    <p className="text-lg text-gray-600 dark:text-gray-300 mb-8">
                        Our privacy team is here to help you understand how we protect your data.
                    </p>
                    <div className="flex flex-col sm:flex-row gap-4 justify-center">
                        <button
                            onClick={() => router.push("/contact")}
                            className="bg-teal-600 hover:bg-teal-700 text-white px-8 py-3 rounded-lg font-medium transition-colors"
                        >
                            Contact Us
                        </button>
                        <button
                            onClick={() => router.push("/help-center")}
                            className="border border-teal-600 text-teal-600 dark:text-teal-400 hover:bg-teal-50 dark:hover:bg-teal-900/20 px-8 py-3 rounded-lg font-medium transition-colors"
                        >
                            Help Center
                        </button>
                    </div>
                </div>
            </section>

            {/* Footer */}
            <SharedFooter />
        </div>
    );
} 