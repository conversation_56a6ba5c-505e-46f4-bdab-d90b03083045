"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { 
  BarChart3, 
  TrendingUp, 
  Shield, 
  Zap, 
  Crown, 
  Star,
  Users,
  Settings,
  Code,
  PieChart
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';
import { FeatureStatistics } from '@/app/services/FeatureRegistryServices';

interface FeatureStatsDashboardProps {
  statistics: FeatureStatistics;
}

/**
 * Dashboard des statistiques des fonctionnalités
 */
const FeatureStatsDashboard: React.FC<FeatureStatsDashboardProps> = ({ statistics }) => {
  const { t } = useTranslation();

  const getModuleIcon = (module: string) => {
    switch (module) {
      case 'students':
        return <Users className="w-5 h-5 text-blue-500" />;
      case 'staff':
        return <Shield className="w-5 h-5 text-green-500" />;
      case 'classes':
        return <Settings className="w-5 h-5 text-purple-500" />;
      case 'announcements':
        return <TrendingUp className="w-5 h-5 text-orange-500" />;
      default:
        return <Code className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSubscriptionIcon = (level: string) => {
    switch (level) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-5 h-5 text-blue-500" />;
      default:
        return <Star className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSubscriptionColor = (level: string) => {
    switch (level) {
      case 'premium':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400';
      case 'enterprise':
        return 'bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400';
      case 'standard':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900/20 dark:text-gray-400';
    }
  };

  const calculatePercentage = (value: number, total: number) => {
    return total > 0 ? Math.round((value / total) * 100) : 0;
  };

  return (
    <div className="space-y-6">
      {/* Vue d'ensemble */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-gradient-to-r from-blue-500 to-blue-600 rounded-lg p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-blue-100 text-sm">Total des fonctionnalités</p>
              <p className="text-3xl font-bold">{statistics.total}</p>
            </div>
            <BarChart3 className="w-8 h-8 text-blue-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-gradient-to-r from-green-500 to-green-600 rounded-lg p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-green-100 text-sm">Modules</p>
              <p className="text-3xl font-bold">{statistics.by_module.length}</p>
            </div>
            <Settings className="w-8 h-8 text-green-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-gradient-to-r from-purple-500 to-purple-600 rounded-lg p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-purple-100 text-sm">Niveaux d'abonnement</p>
              <p className="text-3xl font-bold">{statistics.by_subscription_level.length}</p>
            </div>
            <Crown className="w-8 h-8 text-purple-200" />
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-gradient-to-r from-orange-500 to-orange-600 rounded-lg p-6 text-white"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-orange-100 text-sm">Catégories</p>
              <p className="text-3xl font-bold">{statistics.by_category.length}</p>
            </div>
            <PieChart className="w-8 h-8 text-orange-200" />
          </div>
        </motion.div>
      </div>

      {/* Répartition détaillée */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Par module */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <Settings className="w-5 h-5 mr-2 text-blue-500" />
            Répartition par module
          </h3>
          
          <div className="space-y-3">
            {statistics.by_module.map((item, index) => {
              const percentage = calculatePercentage(item.count, statistics.total);
              return (
                <div key={item._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getModuleIcon(item._id)}
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                      {item._id}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-8 text-right">
                      {item.count}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </motion.div>

        {/* Par niveau d'abonnement */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <Crown className="w-5 h-5 mr-2 text-yellow-500" />
            Par niveau d'abonnement
          </h3>
          
          <div className="space-y-3">
            {statistics.by_subscription_level.map((item, index) => {
              const percentage = calculatePercentage(item.count, statistics.total);
              return (
                <div key={item._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    {getSubscriptionIcon(item._id)}
                    <span className={`px-2 py-1 text-xs rounded-full ${getSubscriptionColor(item._id)}`}>
                      {t(`subscription.plans.${item._id}`, item._id)}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-yellow-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-8 text-right">
                      {item.count}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </motion.div>

        {/* Par catégorie */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
            <PieChart className="w-5 h-5 mr-2 text-purple-500" />
            Par catégorie
          </h3>
          
          <div className="space-y-3">
            {statistics.by_category.map((item, index) => {
              const percentage = calculatePercentage(item.count, statistics.total);
              return (
                <div key={item._id} className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 capitalize">
                      {item._id}
                    </span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="w-20 bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                        style={{ width: `${percentage}%` }}
                      />
                    </div>
                    <span className="text-sm text-gray-600 dark:text-gray-400 w-8 text-right">
                      {item.count}
                    </span>
                  </div>
                </div>
              );
            })}
          </div>
        </motion.div>
      </div>

      {/* Graphiques en barres */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
      >
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4 flex items-center">
          <BarChart3 className="w-5 h-5 mr-2 text-blue-500" />
          Distribution des fonctionnalités
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Graphique modules */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Fonctionnalités par module
            </h4>
            <div className="space-y-2">
              {statistics.by_module.map((item, index) => {
                const percentage = calculatePercentage(item.count, statistics.total);
                return (
                  <div key={item._id} className="flex items-center space-x-3">
                    <div className="w-16 text-xs text-gray-600 dark:text-gray-400 capitalize">
                      {item._id}
                    </div>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-4 relative">
                      <div
                        className="bg-gradient-to-r from-blue-500 to-blue-600 h-4 rounded-full transition-all duration-500 flex items-center justify-center"
                        style={{ width: `${percentage}%` }}
                      >
                        {percentage > 15 && (
                          <span className="text-xs text-white font-medium">
                            {item.count}
                          </span>
                        )}
                      </div>
                      {percentage <= 15 && (
                        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 dark:text-gray-400">
                          {item.count}
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Graphique niveaux */}
          <div>
            <h4 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Fonctionnalités par niveau
            </h4>
            <div className="space-y-2">
              {statistics.by_subscription_level.map((item, index) => {
                const percentage = calculatePercentage(item.count, statistics.total);
                return (
                  <div key={item._id} className="flex items-center space-x-3">
                    <div className="w-16 text-xs text-gray-600 dark:text-gray-400 capitalize">
                      {t(`subscription.plans.${item._id}`, item._id)}
                    </div>
                    <div className="flex-1 bg-gray-200 dark:bg-gray-700 rounded-full h-4 relative">
                      <div
                        className="bg-gradient-to-r from-yellow-500 to-yellow-600 h-4 rounded-full transition-all duration-500 flex items-center justify-center"
                        style={{ width: `${percentage}%` }}
                      >
                        {percentage > 15 && (
                          <span className="text-xs text-white font-medium">
                            {item.count}
                          </span>
                        )}
                      </div>
                      {percentage <= 15 && (
                        <span className="absolute right-2 top-1/2 transform -translate-y-1/2 text-xs text-gray-600 dark:text-gray-400">
                          {item.count}
                        </span>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default FeatureStatsDashboard;
