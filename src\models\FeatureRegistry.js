const mongoose = require('mongoose');

// Feature Registry Schema - Central registry for all system features
const FeatureRegistrySchema = new mongoose.Schema({
  feature_id: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  
  // Feature identification
  name: {
    type: String,
    required: true,
    trim: true
  },
  
  description: {
    type: String,
    required: true,
    trim: true
  },
  
  // Feature categorization
  module: {
    type: String,
    required: true,
    enum: [
      'dashboard',
      'students',
      'staff',
      'classes',
      'academic_records',
      'financial',
      'communication',
      'reports',
      'administration',
      'system'
    ]
  },
  
  category: {
    type: String,
    required: true,
    enum: [
      'core',           // Essential functionality
      'enhanced',       // Improved operations
      'advanced',       // Premium features
      'analytics',      // Data analysis
      'automation',     // Automated processes
      'integration',    // Third-party integrations
      'customization'   // Custom features
    ]
  },
  
  // Subscription requirements
  subscription_level: {
    type: String,
    required: true,
    enum: ['basic', 'standard', 'premium', 'enterprise'],
    default: 'basic'
  },
  
  // Feature dependencies
  dependencies: [{
    feature_id: {
      type: String,
      required: true
    },
    required: {
      type: Boolean,
      default: true
    }
  }],
  
  // Technical implementation details
  api_endpoints: [{
    method: {
      type: String,
      enum: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH'],
      required: true
    },
    path: {
      type: String,
      required: true
    },
    description: String
  }],
  
  ui_components: [{
    component_name: String,
    component_path: String,
    description: String
  }],
  
  permissions_required: [{
    module: String,
    permission: String
  }],
  
  // Feature status and configuration
  status: {
    type: String,
    enum: ['active', 'inactive', 'deprecated', 'beta', 'coming_soon'],
    default: 'active'
  },
  
  is_enabled_globally: {
    type: Boolean,
    default: true
  },
  
  // Usage and analytics
  usage_tracking: {
    enabled: {
      type: Boolean,
      default: true
    },
    metrics: [{
      metric_name: String,
      description: String
    }]
  },
  
  // Feature limits and quotas
  limits: {
    daily_usage: {
      type: Number,
      default: null // null means unlimited
    },
    monthly_usage: {
      type: Number,
      default: null
    },
    concurrent_users: {
      type: Number,
      default: null
    }
  },
  
  // Rollout configuration
  rollout: {
    percentage: {
      type: Number,
      min: 0,
      max: 100,
      default: 100
    },
    target_schools: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'School'
    }],
    exclude_schools: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: 'School'
    }]
  },
  
  // Documentation and help
  documentation: {
    help_url: String,
    video_tutorial_url: String,
    user_guide_url: String
  },
  
  // Metadata
  created_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  last_modified_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User'
  },
  
  version: {
    type: String,
    default: '1.0.0'
  },
  
  tags: [String]
  
}, {
  timestamps: true,
  collection: 'feature_registry'
});

// Indexes for performance
FeatureRegistrySchema.index({ module: 1, category: 1 });
FeatureRegistrySchema.index({ subscription_level: 1 });
FeatureRegistrySchema.index({ status: 1, is_enabled_globally: 1 });

// Static methods for feature management
FeatureRegistrySchema.statics.getFeaturesBySubscriptionLevel = function(subscriptionLevel) {
  const levelHierarchy = {
    'basic': ['basic'],
    'standard': ['basic', 'standard'],
    'premium': ['basic', 'standard', 'premium'],
    'enterprise': ['basic', 'standard', 'premium', 'enterprise']
  };
  
  return this.find({
    subscription_level: { $in: levelHierarchy[subscriptionLevel] || ['basic'] },
    status: 'active',
    is_enabled_globally: true
  });
};

FeatureRegistrySchema.statics.getFeaturesByModule = function(module, subscriptionLevel = 'basic') {
  const levelHierarchy = {
    'basic': ['basic'],
    'standard': ['basic', 'standard'],
    'premium': ['basic', 'standard', 'premium'],
    'enterprise': ['basic', 'standard', 'premium', 'enterprise']
  };
  
  return this.find({
    module: module,
    subscription_level: { $in: levelHierarchy[subscriptionLevel] || ['basic'] },
    status: 'active',
    is_enabled_globally: true
  });
};

FeatureRegistrySchema.statics.checkFeatureAccess = function(featureId, subscriptionLevel) {
  return this.findOne({
    feature_id: featureId,
    status: 'active',
    is_enabled_globally: true
  }).then(feature => {
    if (!feature) return { hasAccess: false, reason: 'feature_not_found' };
    
    const levelHierarchy = {
      'basic': ['basic'],
      'standard': ['basic', 'standard'],
      'premium': ['basic', 'standard', 'premium'],
      'enterprise': ['basic', 'standard', 'premium', 'enterprise']
    };
    
    const allowedLevels = levelHierarchy[subscriptionLevel] || ['basic'];
    const hasAccess = allowedLevels.includes(feature.subscription_level);
    
    return {
      hasAccess,
      feature,
      reason: hasAccess ? null : 'insufficient_subscription',
      required_level: feature.subscription_level
    };
  });
};

const FeatureRegistry = mongoose.models.FeatureRegistry || mongoose.model('FeatureRegistry', FeatureRegistrySchema);

module.exports = FeatureRegistry;
