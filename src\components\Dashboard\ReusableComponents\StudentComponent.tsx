'use client';
import React, { useEffect, useState, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import CircularLoader from '@/components/widgets/CircularLoader';
import { CreditCard, GraduationCap, ListFilter, Loader2, Printer, Upload, UserCheck, UserPlus, X, AlertTriangle } from 'lucide-react';
import { deleteStudent, getStudentsBySchool, importStudentsFromCSV, deleteMultipleStudents, deleteAllStudents } from '@/app/services/StudentServices';
import { getSchoolBy_id, getSchools } from '@/app/services/SchoolServices';
import { getUsers, verifyPassword } from '@/app/services/UserServices';
import BulkDeleteModal from '@/components/modals/BulkDeleteModal';
import { getClassLevels, getClassLevelsBySchoolId } from '@/app/services/ClassLevels';
import { StudentSchema } from '@/app/models/StudentModel';
import { SchoolSchema } from '@/app/models/SchoolModel';
import { UserSchema } from '@/app/models/UserModel';
import { ClassLevelSchema } from '@/app/models/ClassLevel';
import DataTableFix from '@/components/utils/TableFix';
import SubmissionFeedback from "@/components/widgets/SubmissionFeedback";
import { pdf } from '@react-pdf/renderer';
import ClassListPDF from '@/components/utils/ClassListPDF';
import { motion } from 'framer-motion';
import { AcademicYearSchema } from '@/app/models/AcademicYear';
import { getAcademicYears } from '@/app/services/AcademicYearServices';
import IDCardsPDF from '@/components/utils/IDCards';
import DeleteStudentModal from '@/app/(dashboards)/super-admin/students/components/DeleteStudentModal';
import useAcademicYear from '@/components/utils/useAcademicYear';
import { ClassSchema } from '@/app/models/ClassModel';
import { getClassesBySchool } from '@/app/services/ClassServices';
import { useAcademicYearContext } from '@/context/AcademicYearContext';
import { getRegistrationDrafts } from '@/app/services/RegistrationDraftServices';
import { RegistrationDraftSchema } from '@/app/models/RegistrationDraft';
import IDCardPrinter from '@/components/utils/IDCardPrinter';


const BASE_URL = '/school-admin';

interface ManageStudentComponentProps {
  user: UserSchema;
}

const ManageStudentsPage: React.FC<ManageStudentComponentProps> = ({ user }) => {
  const searchParams = useSearchParams();
  const schoolId = user.school_ids?.[0] ?? null;
  const { allAcademicYears, currentAcademicYear } = useAcademicYearContext();

  const router = useRouter();

  const [students, setStudents] = useState<StudentSchema[]>([]);
  const [school, setSchool] = useState<SchoolSchema | null>(null);
  const [parentsMap, setParentsMap] = useState<Record<string, string>>({});
  const [classes, setClasses] = useState<ClassSchema[]>([]);
  const [classesLevelMap, setClassesLevelMap] = useState<Record<string, string>>({});
  const [classesLevels, setClassesLevels] = useState<ClassLevelSchema[]>([]);
  const [selectedClass, setSelectedClass] = useState<string>('all');
  const [selectedClassId, setSelectedClassId] = useState<string>('all');
  const [filteredClasses, setFilteredClasses] = useState<ClassSchema[]>([]);


  const [loading, setLoading] = useState(false);
  const [cvsLoading, setCvsLoading] = useState(false)
  const [studentToDelete, setStudentToDelete] = useState<StudentSchema | null>(null);
  const [registrationDraft, setRegistrationDraft] = useState<RegistrationDraftSchema | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<"success" | "failure" | null>(null);
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false); // État pour le modal de suppression

  const [feedbackStatus, setFeedbackStatus] = useState<"success" | "failure" | null>(null);
  const [feedbackMessage, setFeedbackMessage] = useState<string>("");
  const [selectedStudent, setSelectedStudent] = useState<StudentSchema[]>([]);

  const [isNotificationCard, setIsNotificationCard] = useState(false);
  const [notificationMessage, setNotificationMessage] = useState("");
  const [notificationType, setNotificationType] = useState("success");

  // Bulk delete modal states
  const [isBulkDeleteModalOpen, setIsBulkDeleteModalOpen] = useState(false);
  const [bulkDeleteType, setBulkDeleteType] = useState<"selected" | "all">("selected");
  const [selectedStudentIds, setSelectedStudentIds] = useState<string[]>([]);
  // Key to force DataTable re-render and clear selection
  const [tableKey, setTableKey] = useState(0);

  const fetchData = async () => {
    setLoading(true);
    try {
      const [studentsData, schoolData, usersDataRaw, classLevelData, classData] = await Promise.all([
        getStudentsBySchool(schoolId as string, {
          page: 1,
          limit: 10000,
          classLevelId: "",
          statusFilter: ""
        }),
        getSchoolBy_id(schoolId as string),
        getUsers(),
        getClassLevelsBySchoolId(schoolId as string),
        getClassesBySchool(schoolId as string)
      ]);
      setSchool(schoolData);
      setStudents(studentsData);
      setClassesLevels(classLevelData);
      setClasses(classData)

      // Explicitly type usersData as UserSchema[]
      const usersData = usersDataRaw as unknown as UserSchema[];

      // Build classesMap here using classData directly
      const classMap: Record<string, string> = {};
      classLevelData.forEach((cls: ClassLevelSchema) => {
        classMap[cls._id] = cls.name;
      });
      setClassesLevelMap(classMap);

      const parentUsers = usersData.filter((user: UserSchema) => user.role === 'parent');
      const parentMap: Record<string, string> = {};
      parentUsers.forEach((parent: { _id: string | number; name: string; }) => {
        parentMap[parent._id] = parent.name;
      });
      setParentsMap(parentMap);
    } catch (err) {
      console.error('Failed to fetch data', err);
    } finally {
      setLoading(false);
    }
  };

  const fetchDraft = async () => {
    if (!schoolId) {
      return
    }
    const drafts = await getRegistrationDrafts(schoolId as string, user._id as string);

    // Check if a draft exists, and set it
    if (drafts && drafts.length > 0) {
      setRegistrationDraft(drafts[0]); // Assume you want the first draft
    }
  };

  useEffect(() => {
    fetchData();
    fetchDraft();
  }, [schoolId]);
  useEffect(() => {
    if (selectedClass === 'all') {
      setFilteredClasses(classes); // Show all classes if no level is selected
    } else {
      const filtered = classes.filter(cls => {
        let classLevelId: string;

        if (
          typeof cls.class_level === 'object' &&
          cls.class_level !== null &&
          '$oid' in cls.class_level &&
          typeof (cls.class_level as any).$oid === 'string'
        ) {
          classLevelId = (cls.class_level as { $oid: string }).$oid;
        } else {
          classLevelId = cls.class_level as string;
        }

        return classLevelId === selectedClass;
      });

      setFilteredClasses(filtered);
    }
  }, [selectedClass, classes]);


  // Filter students by selected class
  const filteredStudents = students.filter((student) => {
    // Filter by Class Level
    let classLevelId: string;
    if (
      typeof student.class_level === 'object' &&
      student.class_level !== null &&
      '$oid' in student.class_level &&
      typeof (student.class_level as any).$oid === 'string'
    ) {
      classLevelId = (student.class_level as { $oid: string }).$oid;
    } else {
      classLevelId = student.class_level as string;
    }

    const classLevelMatch =
      selectedClass === 'all' || classLevelId === selectedClass;

    // Filter by Class (optional)
    let classId: string;
    if (
      typeof student.class_id === 'object' &&
      student.class_id !== null &&
      '$oid' in student.class_id &&
      typeof (student.class_id as any).$oid === 'string'
    ) {
      classId = (student.class_id as { $oid: string }).$oid;
    } else {
      classId = student.class_id as string;
    }

    const classMatch =
      selectedClassId === 'all' || classId === selectedClassId;

    return classLevelMatch && classMatch;
  });


  const columns = [
    { header: 'Student ID', accessor: (row: StudentSchema) => row.student_id },
    { header: 'Student Name', accessor: (row: StudentSchema) => row.name },
    // {
    //   header: 'Age',
    //   accessor: (row: StudentSchema) => {
    //     if (!row.date_of_birth) return 'N/A';
    //     const dob = new Date(row.date_of_birth);
    //     if (isNaN(dob.getTime())) return 'N/A';

    //     const diffMs = Date.now() - dob.getTime();
    //     const ageDt = new Date(diffMs);
    //     return Math.abs(ageDt.getUTCFullYear() - 1970);
    //   },
    // },
    {
      header: 'Birthday',
      accessor: (row: StudentSchema) => {
        if (!row.date_of_birth) return 'N/A';
        const dob = new Date(row.date_of_birth);
        return dob.toLocaleDateString(); // formats as MM/DD/YYYY by default
      },
    },
    {
      header: 'Place of Birth',
      accessor: (row: StudentSchema) => row.place_of_birth || 'N/A',
    },
    {
      header: 'Class Level',
      accessor: (row: StudentSchema) => {
        let id: string;
        if (
          typeof row.class_level === 'object' &&
          row.class_level !== null &&
          '$oid' in row.class_level &&
          typeof (row.class_level as any).$oid === 'string'
        ) {
          id = (row.class_level as { $oid: string }).$oid;
        } else {
          id = row.class_level as string;
        }
        return classesLevelMap[id] ?? 'No class Level';
      },
    },
    {
      header: 'Class',
      accessor: (row: StudentSchema) => {
        let classId: string;
        if (
          typeof row.class_id === 'object' &&
          row.class_id !== null &&
          '$oid' in row.class_id &&
          typeof (row.class_id as any).$oid === 'string'
        ) {
          classId = (row.class_id as { $oid: string }).$oid;
        } else {
          classId = row.class_id as string;
        }
        return classes.find((cls) => cls._id === classId)?.name || 'Unknown Class';
      },
    },
    {
      header: 'Parent(s) Name',
      accessor: (row: StudentSchema) => {
        const guardianNames = (row.guardian_id ?? [])
          .map((id) => parentsMap[id] || id)
          .join(', ');
        return guardianNames || 'No guardian';
      },
    },
    {
      header: 'Gender',
      accessor: (row: StudentSchema) => {
        if (row.gender === 'Male') return 'M';
        if (row.gender === 'Female') return 'F';
        if (row.gender === 'Other') return 'O';
        return 'N/A';
      },
    },
    {
      header: 'Registered',
      accessor: (row: StudentSchema) => (
        <span
          className={`px-2 py-1 rounded-full text-white text-sm font-medium ${row.registered ? 'bg-green-600' : 'bg-gray-400'
            }`}
        >
          {row.registered ? 'Yes' : 'No'}
        </span>
      ),
    },
  ];

  const actions = [
    {
      label: "View",
      onClick: (std: StudentSchema) => {
        router.push(`${BASE_URL}/students/manage/view?studentId=${std._id}&schoolId=${schoolId}`);
      },
    },
    {
      label: "Delete",
      onClick: (std: StudentSchema) => {
        setStudentToDelete(std);
        setIsDeleteModalOpen(true); // Ouvrir le modal de suppression

      },
    },
  ];

  const getClassLevelNames = (ids?: string[]) =>
    ids
      ?.map((id) =>
        classesLevels.find((c) => c._id === id && c.school_id === schoolId)?.name || "Unknown"
      )
      .join(", ") || "No class";

  const handleExportCSV = () => {
    const headers = [
      "Student ID",
      "Student Name",
      "Birthday",
      "Place of Birth",
      "Class Level",
      "Parent(s) Name",
      "Gender",
      "Registered"
    ];

    const rows = filteredStudents.map((student) => {
      const dob = student.date_of_birth
        ? new Date(student.date_of_birth).toLocaleDateString()
        : "N/A";

      let classLevelId: string;
      if (
        typeof student.class_level === "object" &&
        student.class_level !== null &&
        "$oid" in student.class_level &&
        typeof (student.class_level as any).$oid === "string"
      ) {
        classLevelId = (student.class_level as { $oid: string }).$oid;
      } else {
        classLevelId = student.class_level as string;
      }

      const classLevelName = classesLevelMap[classLevelId] ?? "No class";

      const guardianNames = (student.guardian_id ?? [])
        .map((id) => parentsMap[id] || id)
        .join(", ");

      const gender = student.gender === "Male" ? "M"
        : student.gender === "Female" ? "F"
          : student.gender === "Other" ? "O"
            : "N/A";

      return [
        student.student_id,
        student.name,
        dob,
        student.place_of_birth || "N/A",
        classLevelName,
        guardianNames || "No guardian",
        gender,
        student.registered ? "Yes" : "No"
      ];
    });

    const csvContent =
      "data:text/csv;charset=utf-8," +
      [headers.join(","), ...rows.map((r) => r.map((v) => `"${v}"`).join(","))].join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "students.csv");
    document.body.appendChild(link); // Required for FF
    link.click();
    document.body.removeChild(link);
  };


  //   const handlePrintIDCards = async (event: React.MouseEvent<HTMLButtonElement>) => {
  //     const printWindow = window.open('', '_blank');

  //     if (!printWindow) return;

  //     // Provide a default QR code URL if student.qrCode is not available

  //     const logoURL = school?.logo || `${window.location.origin}/assets/images/school-logo.jpg`;

  //     const html = `
  // <html>
  //   <head>
  //     <title>Student ID Cards</title>
  //     <style>
  //       @media print {
  //         body {
  //           margin: 0;
  //           -webkit-print-color-adjust: exact;
  //           print-color-adjust: exact;
  //         }
  //         .page {
  //           page-break-after: always;
  //         }
  //       }

  //       body {
  //         margin: 0;
  //         font-family: 'Segoe UI', sans-serif;
  //         background: #f5f5f5;
  //         padding: 20px;
  //         display: flex;
  //         flex-direction: column;
  //         align-items: center;
  //       }

  //       .page {
  //         display: flex;
  //         flex-wrap: wrap;
  //         justify-content: center;
  //         gap: 20px;
  //         margin-bottom: 40px;
  //       }

  //       .card {
  //         width: 280px;
  //         height: 440px;
  //         background: #ffffff;
  //         border-radius: 12px;
  //         box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
  //         padding: 12px;
  //         display: flex;
  //         flex-direction: column;
  //         align-items: center;
  //         justify-content: space-between;
  //       }

  //       .republic {
  //         text-align: center;
  //         font-size: 10px;
  //         font-weight: bold;
  //         color: #1E3D59;
  //       }

  //       .motto {
  //         text-align: center;
  //         font-size: 8px;
  //         font-style: italic;
  //         color: #1E3D59;
  //         margin-bottom: 4px;
  //       }

  //       .school-header {
  //         text-align: center;
  //         margin-bottom: 4px;
  //       }

  //       .school-name {
  //         font-size: 14px;
  //         font-weight: bold;
  //         color: #1E3D59;
  //       }

  //       .card-title {
  //         font-size: 11px;
  //         font-weight: bold;
  //         margin: 4px 0 6px;
  //         color: #000;
  //       }

  //       .photo {
  //         width: 180px;
  //         height: 180px;
  //         border-radius: 20px;
  //         margin-bottom: 10px;
  //       }

  //       .photo img {
  //         width: 100%;
  //         height: 100%;
  //         object-fit: cover;
  //       }

  //       .info-grid {
  //         display: flex;
  //         justify-content: space-between;
  //         width: 100%;
  //         font-size: 10px;
  //         gap: 8px;
  //       }

  //       .info-column {
  //         width: 30%;
  //         line-height: 1.5;
  //       }

  //       .info-column p {
  //         margin: 4px 0;
  //       }

  //       .info-column strong {
  //         display: block;
  //         font-weight: bold;
  //         color: #1E3D59;
  //       }

  //       .qr-logo-column {
  //         display: flex;
  //         flex-direction: column;
  //         align-items: center;
  //         justify-content: center;
  //         gap: 8px;
  //         width: 40%;
  //       }

  //       .qr {
  //         width: 60px;
  //         height: 60px;
  //       }

  //       .school-logo {
  //         width: 60px;
  //         height: 60px;
  //         object-fit: contain;
  //       }

  //       .footer {
  //         font-size: 8px;
  //         color: #555;
  //         text-align: center;
  //         width: 100%;
  //         border-top: 1px solid #eee;
  //         margin-top: 8px;
  //         padding-top: 4px;
  //       }
  //     </style>
  //   </head>
  //   <body>
  //     <div class="page">
  //       ${filteredStudents.map(student => {
  //       const dob = new Date(student.date_of_birth ?? "");
  //       const formattedDOB = dob.toLocaleDateString('en-GB'); // e.g., 01/06/2025
  //       const baseVerifyUrl = "https://google.com/verify-student";
  //       const qrCodeURL = `https://api.qrserver.com/v1/create-qr-code/?data=${encodeURIComponent(
  //         `${baseVerifyUrl}/${student._id}`
  //       )}&size=60x60`;
  //       return `
  //         <div class="card">
  //           <div class="republic">REPUBLIC OF CAMEROON</div>
  //           <div class="motto">Peace - Work - Fatherland</div>
  //           <div class="school-header">
  //             <div class="school-name">${school?.name || "SCHOOL NAME"}</div>
  //           </div>
  //           <div class="card-title">STUDENT ID CARD</div>
  //           <div class="photo">
  //             <img src="${student.avatar || `${window.location.origin}/assets/images/student.jpg`}" alt="Student Photo" />
  //           </div>
  //           <div class="info-grid">
  //             <div class="info-column">
  //               <p><strong>Name</strong> <em>${student.first_name} ${student.last_name}</em></p>
  //               <p><strong>School</strong> <em>${school?.name || "N/A"}</em></p>
  //               <p><strong>Student ID</strong> <em>${student.student_id || "N/A"}</em></p>
  //             </div>
  //             <div class="info-column">
  //               <p><strong>Gender</strong> <em>${student.gender || "N/A"}</em></p>
  //               <p><strong>Nationality</strong> <em>${student.nationality || "N/A"}</em></p>
  //               <p><strong>Class</strong> <em>${getClassLevelNames([student.class_level ?? ""])}</em></p>
  //               <p><strong>Date Of Birth</strong> <em>${formattedDOB}</em></p>
  //               <p><strong>Academic Year</strong> <em>${acadamicYear || "N/A"}</em></p>
  //             </div>
  //             <div class="qr-logo-column">
  //               <img src="${student.qrCode || qrCodeURL}" alt="QR Code" class="qr" />
  //               <img src="${logoURL}" alt="School Logo" class="school-logo" />
  //             </div>
  //           </div>
  //         </div>`;
  //     }).join('')}
  //     </div>
  //     <script>
  //       window.onload = function () {
  //         window.print();
  //         window.onafterprint = function () {
  //           window.close();
  //         };
  //       };
  //     </script>
  //   </body>
  // </html>
  // `;

  //     printWindow.document.open();
  //     printWindow.document.write(html);
  //     printWindow.document.close();
  //   };

  const handleRegister = () => {
    router.replace(`${BASE_URL}/students/manage/register?schoolId=${schoolId}`);
  };
  const handleEnroll = () => {
    router.replace(`${BASE_URL}/students/manage/enroll?schoolId=${schoolId}`);
  };

  const handlePrint = async (event: React.MouseEvent<HTMLButtonElement>) => {
    event.preventDefault();

    if (!school) return;

    // Compose the props expected by ClassListPDF
    const schoolInfo = {
      name: school.name,
      logoUrl: typeof school.logoUrl === 'string' ? school.logoUrl : '', // ensure string type
      year: currentAcademicYear,
    };

    const classNameDisplay = selectedClass === 'all' ? 'All Levels' : classesLevelMap[selectedClass] || '';
    const selectedClassName = selectedClassId === 'all' ? 'All Classes' : classes.find((cls) => cls._id === selectedClassId)?.name || 'Unknown Class';
    // Convert date_of_birth to string for each student
    const studentsForPDF = filteredStudents.map((student) => ({
      ...student,
      date_of_birth: student.date_of_birth instanceof Date
        ? student.date_of_birth.toISOString()
        : student.date_of_birth,
      class_level:
        student.class_level === undefined
          ? null
          : student.class_level,
    }));

    // Create the PDF document instance
    const doc = <ClassListPDF
      school={schoolInfo}
      className={classNameDisplay}
      selectedClassName={selectedClassName}
      students={studentsForPDF}
      currentAcademicYear={currentAcademicYear}
    />;

    // Generate PDF blob
    const blob = await pdf(doc).toBlob();

    // Open PDF in a new browser tab
    const url = URL.createObjectURL(blob);
    const newTab = window.open('', '_blank');
    if (newTab) {
      newTab.location.href = url;
    } else {
      alert('Popup blocked! Please allow popups to view the student list.');
    }
  };
  const handleFileUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    console.log(file)
    if (!file) return;
    setCvsLoading(true);
    setFeedbackStatus(null);
    setFeedbackMessage("");

    try {
      const response = await importStudentsFromCSV(schoolId as string, file);
      setFeedbackStatus("success");
      setFeedbackMessage(
        `Import complete: ${response.successful}/${response.total} students imported.`
      );
    } catch (err: any) {
      setFeedbackStatus("failure");
      setFeedbackMessage(err.message || "Import failed.");
    } finally {
      setCvsLoading(false);
      event.target.value = ""; // Reset file input
      fetchData(); // Refresh student list after import
    }
  };

  const handleDeleteSelected = () => {
    if (selectedStudent.length === 0) return;
    setIsModalOpen(true);
  };

  const handleDelete = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);
    // Simuler une vérification de mot de passe (dans un vrai projet, fais une requête API)
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    //console.log("passwordVerified", passwordVerified);
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);

      // ✅ Fix: Reset loading/submitting states even when password fails
      setIsSubmitting(false);
      setSubmitStatus("failure");
      setTimeout(() => {
        setStudentToDelete(null); // ✅ Close delete modal properly
        setSubmitStatus(null);
      }, 10000);
      return;
    }
    try {
      if (studentToDelete) {
        await deleteStudent(studentToDelete.student_id);
        fetchData();
        setSubmitStatus("success");
        setNotificationMessage("Student Deleted successfully!");
        setNotificationType("success");
        setIsNotificationCard(true);

        setTimeout(() => {
          setStudentToDelete(null); // ✅ Close delete modal properly
          setSubmitStatus(null);
        }, 10000);
      }
    } catch (error) {
      console.error("Error Deleting Student:", error);

      setSubmitStatus("failure");
      const errorMessage =
        error instanceof Error
          ? error.message
          : "An unknown error occurred while deleting the Student.";

      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
    } finally {
      setIsSubmitting(false);
      // setLoadingData(false);
    }
  };

  // Handle opening bulk delete modal for selected items
  const handleDeleteMultiple = async (selectedIds: string[]) => {
    if (selectedIds.length === 0) {
      alert("Please select at least one student to delete.");
      return;
    }
    setSelectedStudentIds(selectedIds);
    setBulkDeleteType("selected");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle opening bulk delete modal for all items
  const handleDeleteAll = async () => {
    if (filteredStudents.length === 0) {
      alert("No students to delete.");
      return;
    }
    setBulkDeleteType("all");
    setIsBulkDeleteModalOpen(true);
  };

  // Handle the actual bulk deletion with password confirmation
  const handleBulkDeleteConfirm = async (password: string) => {
    setIsSubmitting(true);
    setSubmitStatus(null);

    // Verify password
    const passwordVerified = user ? await verifyPassword(password, user.email) : false;
    if (!passwordVerified) {
      setNotificationMessage("Invalid Password!");
      setNotificationType("error");
      setIsNotificationCard(true);
      setIsSubmitting(false);
      setSubmitStatus("failure");
      return;
    }

    try {
      if (bulkDeleteType === "all") {
        await deleteAllStudents();
        setNotificationMessage("All students deleted successfully!");
      } else {
        await deleteMultipleStudents(selectedStudentIds);
        setNotificationMessage(`${selectedStudentIds.length} student(s) deleted successfully!`);
      }

      setNotificationType("success");
      setIsNotificationCard(true);
      setSubmitStatus("success");
      fetchData(); // Refresh the data
      setSelectedStudent([]); // Clear selection
      setTableKey(prev => prev + 1); // Force table re-render to clear selection

      // Close modal after success
      setTimeout(() => {
        setIsBulkDeleteModalOpen(false);
        setSubmitStatus(null);
      }, 2000);

    } catch (error) {
      console.error("Error in bulk deletion:", error);
      const errorMessage = error instanceof Error ? error.message : "Failed to delete students";
      setNotificationMessage(errorMessage);
      setNotificationType("error");
      setIsNotificationCard(true);
      setSubmitStatus("failure");
    } finally {
      setIsSubmitting(false);
    }
  };


  return (
    <div className="md:py-6 flex flex-col gap-6">
      {/* Container for all top-level action buttons */}
      <div className="flex flex-col sm:flex-row sm:flex-wrap justify-between items-center gap-4 p-4 bg-widget rounded-lg shadow-md border border-gray-300 darK:border dark:border-gray-800">

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 w-full mt-6">

          {/* 📋 Student Management */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 200 }}
            className="p-5 rounded-2xl shadow-md bg-background border border-gray-200 dark:border-gray-800 flex flex-col gap-4"
          >
            <div>
              <h4 className="font-semibold text-foreground text-lg">Student Management</h4>
              <p className="text-sm text-gray-500 mt-1">Quickly register or enroll students into the system.</p>
            </div>
            <button
              onClick={handleRegister}
              className={`flex items-center gap-2 px-4 py-2 rounded-md text-sm ${registrationDraft
                ? "bg-yellow-500 hover:bg-yellow-600 text-white" // Warning color when draft exists
                : "bg-teal hover:bg-green-600 text-white" // Default color for registration
                }`}
            >
              {registrationDraft ? (
                <AlertTriangle size={18} /> // Warning icon when registration draft exists
              ) : (
                <UserPlus size={18} /> // User icon when registering
              )}
              {registrationDraft ? "Continue Registration" : "Register Student"}
            </button>
            <button
              onClick={handleEnroll}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-blue-500 text-white hover:bg-blue-600 text-sm"
            >
              <UserCheck size={18} /> Enroll Student
            </button>
          </motion.div>

          {/* 📁 Import & Export */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 200 }}
            className="p-5 rounded-2xl shadow-md bg-background border border-gray-200 dark:border-gray-800 flex flex-col gap-4"
          >
            <div>
              <h4 className="font-semibold text-foreground text-lg">Data Import/Export</h4>
              <p className="text-sm text-gray-500 mt-1">Upload student lists via CSV or export data for records.</p>
            </div>
            <input
              type="file"
              accept=".csv"
              id="student-csv-upload"
              className="hidden"
              onChange={handleFileUpload}
            />
            <button
              onClick={() => document.getElementById("student-csv-upload")?.click()}
              disabled={cvsLoading}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-purple-500 text-white hover:bg-purple-600 text-sm disabled:opacity-60"
            >
              {cvsLoading ? <Loader2 className="animate-spin h-5 w-5" /> : <Upload size={18} />}
              Upload CSV
            </button>
            <button
              onClick={handleExportCSV}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-indigo-500 text-white hover:bg-indigo-600 text-sm"
            >
              <Upload size={18} /> Export CSV
            </button>
          </motion.div>

          {/* 🖨️ Print Options */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 200 }}
            className="p-5 rounded-2xl shadow-md bg-background border border-gray-200 dark:border-gray-800 flex flex-col gap-4"
          >
            <div>
              <h4 className="font-semibold text-foreground text-lg">Printing & Reports</h4>
              <p className="text-sm text-gray-500 mt-1">Generate printable lists or ID cards for enrolled students.</p>
            </div>
            <button
              onClick={handlePrint}
              className="flex items-center gap-2 px-4 py-2 rounded-md bg-green-500 text-white hover:bg-green-600 text-sm"
            >
              <Printer size={18} /> Print Student List
            </button>

            <IDCardPrinter
              students={filteredStudents} // Pass your filtered students
              school={school || undefined} // Pass your school data
              acadamicYear={currentAcademicYear} // Pass the current academic year
              getClassLevelNames={getClassLevelNames} // Pass your helper function
            />
          </motion.div>

          {/* 🔍 Filters */}
          <motion.div
            whileHover={{ scale: 1.02 }}
            transition={{ type: "spring", stiffness: 200 }}
            className="p-5 rounded-2xl shadow-md bg-background border border-gray-200 dark:border-gray-800 flex flex-col gap-4 md:col-span-2 lg:col-span-1"
          >
            <div>
              <h4 className="font-semibold text-foreground text-lg">Filters</h4>
              <p className="text-sm text-gray-500 mt-1">Filter student list by class level and specific class.</p>
            </div>
            <div className="flex items-center gap-2">
              <ListFilter size={20} className="text-gray-600" />
              <select
                className="px-3 py-2 border rounded-md text-sm bg-background text-foreground w-full"
                value={selectedClass}
                onChange={(e) => setSelectedClass(e.target.value)}
              >
                <option value="all">All Levels</option>
                {classesLevels.map((cls) => (
                  <option key={cls._id} value={cls._id}>
                    {cls.name}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex items-center gap-2">
              <ListFilter size={20} className="text-gray-600" />
              <select
                className="px-3 py-2 border rounded-md text-sm bg-background text-foreground w-full"
                value={selectedClassId}
                onChange={(e) => setSelectedClassId(e.target.value)}
              >
                <option value="all">All Classes</option>
                {filteredClasses.map((cls) => (
                  <option key={cls._id} value={cls._id}>
                    {cls.name}
                  </option>
                ))}
              </select>
            </div>
          </motion.div>
        </div>

      </div>

      {isDeleteModalOpen && studentToDelete && (
        <DeleteStudentModal
          studentName={studentToDelete.name} // or IDs if preferred
          onClose={() => {
            setIsDeleteModalOpen(false);
            setStudentToDelete(null);
            setSubmitStatus(null);
          }}
          onDelete={handleDelete}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
        />
      )}
      {/* Table */}
      <DataTableFix
        key={tableKey} // Force re-render to clear selection
        columns={columns}
        data={filteredStudents}
        defaultItemsPerPage={5}
        loading={loading}
        onLoadingChange={setLoading}
        onSelectionChange={setSelectedStudent}
        actions={actions}
        enableBulkActions={true}
        handleDeleteMultiple={handleDeleteMultiple}
        handleDeleteAll={handleDeleteAll}
        idAccessor="_id"
      />

      {/* Bulk Delete Modal */}
      {isBulkDeleteModalOpen && (
        <BulkDeleteModal
          isOpen={isBulkDeleteModalOpen}
          onClose={() => {
            setIsBulkDeleteModalOpen(false);
            setSubmitStatus(null);
          }}
          onConfirm={handleBulkDeleteConfirm}
          title={bulkDeleteType === "all" ? "Delete All Students" : "Delete Selected Students"}
          message={
            bulkDeleteType === "all"
              ? `Are you sure you want to delete ALL ${filteredStudents.length} students? This action cannot be undone.`
              : `Are you sure you want to delete ${selectedStudentIds.length} selected student(s)? This action cannot be undone.`
          }
          itemCount={bulkDeleteType === "all" ? filteredStudents.length : selectedStudentIds.length}
          itemType="students"
          isDeleteAll={bulkDeleteType === "all"}
          isSubmitting={isSubmitting}
          submitStatus={submitStatus}
          requirePassword={true}
        />
      )}
      {feedbackStatus && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
          <div className="bg-white rounded-xl p-6 shadow-lg max-w-sm w-full">
            <div className="flex justify-end mt-4">
              <button
                onClick={() => {
                  setFeedbackStatus(null);
                  setFeedbackMessage('');
                }}
                className="text-gray-500 hover:text-gray-700"
              >
                <X size={20} />
              </button>
            </div>
            <SubmissionFeedback status={feedbackStatus} message={feedbackMessage} />
          </div>
        </div>
      )}

    </div>
  );
}

export default function StudentComponent({ user }: { user: UserSchema }): JSX.Element {
  return (
    <Suspense
      fallback={
        <div className="flex justify-center items-center h-screen">
          <CircularLoader size={32} color="teal" />
        </div>
      }
    >
      <ManageStudentsPage user={user} />
    </Suspense>
  );
}
