const express = require('express');
const router = express.Router();

const {
  createJustification,
  getJustificationById,
  getJustificationsBySchool,
  getJustificationsByStudent,
  reviewJustification,
} = require('../controllers/JustificationController');

const {
  authenticate,
  authorize,
  checkSubscription,
} = require("../middleware/middleware");
const { uploadJustificationFile } = require('../utils/uploaders');

/**
 * Routes:
 * POST /          - Create justification (parents & staff)
 * GET /:id        - Get justification by ID (parents & staff)
 * GET /school/:school_id  - Get justifications by school (staff only)
 * GET /student/:student_id - Get justifications by student (staff only)
 * PATCH /:id/review - Review justification status (staff only)
 */

// Create justification (parents)
router.post(
  '/',
  authenticate, authorize, checkSubscription, authorize(['parent']),
  uploadJustificationFile.single('file'), // accepts file upload with field name 'file'
  createJustification
);

// Get justifications by school (staff only)
router.get(
  '/school/:school_id',
  authenticate,
  authorize(['teacher', 'admin', 'school_admin', 'bursar']),
  getJustificationsBySchool
);

// Review justification (accept/reject) - staff only
router.patch(
  '/:id/review',
  authenticate,
  authorize(['teacher', 'admin', 'school_admin', 'bursar']),
  reviewJustification
);

// Get justification by ID (parents and staff)
// router.get(
//   'get-by-id/:id',
//   authorize(['parent', 'teacher', 'admin', 'school_admin', 'bursar']),
//   getJustificationById
// );


// Get justifications by student (staff only)
// router.get(
//   '/student/:student_id',
//   authorize(['teacher', 'admin', 'school_admin', 'bursar']),
//   getJustificationsByStudent
// );



module.exports = router;
