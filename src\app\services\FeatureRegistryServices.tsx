import { getTokenFromCookie } from './UserServices';

const BASE_API_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8001/api';

export interface FeatureAccess {
  hasAccess: boolean;
  reason?: string;
  required_level?: string;
  current_level?: string;
  feature?: {
    feature_id: string;
    name: string;
    description: string;
    subscription_level: string;
    module: string;
    category: string;
  };
}

export interface FeatureInfo {
  feature_id: string;
  name: string;
  description: string;
  module: string;
  category: string;
  subscription_level: string;
  status: string;
  api_endpoints?: Array<{
    method: string;
    path: string;
    description?: string;
  }>;
  ui_components?: Array<{
    component_name: string;
    component_path: string;
    description?: string;
  }>;
  permissions_required?: Array<{
    module: string;
    permission: string;
  }>;
}

export interface FeatureStatistics {
  total: number;
  by_module: Array<{ _id: string; count: number }>;
  by_subscription_level: Array<{ _id: string; count: number }>;
  by_category: Array<{ _id: string; count: number }>;
}

/**
 * Service pour la gestion du registre des fonctionnalités
 */
export class FeatureRegistryServices {
  
  /**
   * Vérifier l'accès à une fonctionnalité pour une école
   */
  static async checkFeatureAccess(featureId: string, schoolId: string): Promise<FeatureAccess> {
    try {
      const token = getTokenFromCookie("idToken");
      const response = await fetch(`${BASE_API_URL}/feature-registry/check/${featureId}/school/${schoolId}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return { hasAccess: true, ...data.data };
      } else {
        return {
          hasAccess: false,
          reason: data.error_code,
          required_level: data.subscription_required,
          current_level: data.current_subscription
        };
      }
    } catch (error) {
      console.error('Erreur lors de la vérification d\'accès à la fonctionnalité:', error);
      return { hasAccess: false, reason: 'error' };
    }
  }

  /**
   * Obtenir les fonctionnalités par niveau d'abonnement
   */
  static async getFeaturesBySubscriptionLevel(
    level: string, 
    module?: string, 
    useCache: boolean = true
  ): Promise<FeatureInfo[]> {
    try {
      const token = getTokenFromCookie("idToken");
      let url = `${BASE_API_URL}/feature-registry/subscription/${level}`;
      
      const params = new URLSearchParams();
      if (module) params.append('module', module);
      if (!useCache) params.append('useCache', 'false');
      
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des fonctionnalités');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des fonctionnalités par niveau:', error);
      throw error;
    }
  }

  /**
   * Obtenir les fonctionnalités groupées par module
   */
  static async getFeaturesByModuleGrouped(
    level: string = 'basic', 
    useCache: boolean = true
  ): Promise<Record<string, FeatureInfo[]>> {
    try {
      const token = getTokenFromCookie("idToken");
      const params = new URLSearchParams();
      params.append('level', level);
      if (!useCache) params.append('useCache', 'false');

      const response = await fetch(`${BASE_API_URL}/feature-registry/grouped?${params.toString()}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des fonctionnalités groupées');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des fonctionnalités groupées:', error);
      throw error;
    }
  }

  /**
   * Obtenir les détails d'une fonctionnalité
   */
  static async getFeatureById(featureId: string, useCache: boolean = true): Promise<FeatureInfo> {
    try {
      const token = getTokenFromCookie("idToken");
      const params = new URLSearchParams();
      if (!useCache) params.append('useCache', 'false');

      let url = `${BASE_API_URL}/feature-registry/${featureId}`;
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Fonctionnalité non trouvée');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des détails de la fonctionnalité:', error);
      throw error;
    }
  }

  /**
   * Obtenir les statistiques des fonctionnalités
   */
  static async getFeatureStatistics(level?: string): Promise<FeatureStatistics> {
    try {
      const token = getTokenFromCookie("idToken");
      const params = new URLSearchParams();
      if (level) params.append('level', level);

      let url = `${BASE_API_URL}/feature-registry/statistics`;
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la récupération des statistiques');
      }
    } catch (error) {
      console.error('Erreur lors de la récupération des statistiques:', error);
      throw error;
    }
  }

  /**
   * Vérifier l'accès à plusieurs fonctionnalités
   */
  static async checkMultipleFeatures(
    featureIds: string[], 
    schoolId: string
  ): Promise<Record<string, FeatureAccess>> {
    try {
      const results: Record<string, FeatureAccess> = {};
      
      // Exécuter les vérifications en parallèle
      const promises = featureIds.map(async (featureId) => {
        const access = await this.checkFeatureAccess(featureId, schoolId);
        return { featureId, access };
      });

      const accessResults = await Promise.all(promises);
      
      accessResults.forEach(({ featureId, access }) => {
        results[featureId] = access;
      });

      return results;
    } catch (error) {
      console.error('Erreur lors de la vérification multiple des fonctionnalités:', error);
      throw error;
    }
  }

  /**
   * Créer ou mettre à jour une fonctionnalité (Super Admin uniquement)
   */
  static async createOrUpdateFeature(featureData: Partial<FeatureInfo>): Promise<FeatureInfo> {
    try {
      const token = getTokenFromCookie("idToken");
      const isUpdate = !!featureData.feature_id;
      
      const url = isUpdate 
        ? `${BASE_API_URL}/feature-registry/${featureData.feature_id}`
        : `${BASE_API_URL}/feature-registry`;

      const response = await fetch(url, {
        method: isUpdate ? 'PUT' : 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(featureData)
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors de la sauvegarde de la fonctionnalité');
      }
    } catch (error) {
      console.error('Erreur lors de la création/mise à jour de la fonctionnalité:', error);
      throw error;
    }
  }

  /**
   * Activer/désactiver une fonctionnalité (Super Admin uniquement)
   */
  static async toggleFeature(featureId: string, enabled: boolean): Promise<FeatureInfo> {
    try {
      const token = getTokenFromCookie("idToken");
      const response = await fetch(`${BASE_API_URL}/feature-registry/${featureId}/toggle`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ enabled })
      });

      const data = await response.json();

      if (response.ok && data.success) {
        return data.data;
      } else {
        throw new Error(data.message || 'Erreur lors du basculement de la fonctionnalité');
      }
    } catch (error) {
      console.error('Erreur lors du basculement de la fonctionnalité:', error);
      throw error;
    }
  }

  /**
   * Vider le cache des fonctionnalités (Super Admin uniquement)
   */
  static async clearCache(featureId?: string, module?: string): Promise<void> {
    try {
      const token = getTokenFromCookie("idToken");
      const params = new URLSearchParams();
      if (featureId) params.append('featureId', featureId);
      if (module) params.append('module', module);

      let url = `${BASE_API_URL}/feature-registry/cache`;
      if (params.toString()) {
        url += `?${params.toString()}`;
      }

      const response = await fetch(url, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (!response.ok || !data.success) {
        throw new Error(data.message || 'Erreur lors de la suppression du cache');
      }
    } catch (error) {
      console.error('Erreur lors de la suppression du cache:', error);
      throw error;
    }
  }
}

export default FeatureRegistryServices;
