"use client";

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import {
  GraduationCap,
  BookOpen,
  Users,
  Calendar,
  BarChart3,
  FileText,
  CheckCircle,
  ArrowRight,
  Clock,
  Award,
  Bookmark,
  Settings,
  Eye,
  Edit,
  Plus,
  Trash2
} from 'lucide-react';
import SharedNavigation from '@/components/layout/SharedNavigation';
import SharedFooter from '@/components/layout/SharedFooter';

export default function AcademicManagementPage() {
  const router = useRouter();
  const [activeTab, setActiveTab] = useState('overview');

  const handleNavigation = (path: string) => {
    router.push(path);
  };

  const tabs = [
    { id: 'overview', name: 'Overview', icon: Eye },
    { id: 'classes', name: 'Classes', icon: Users },
    { id: 'subjects', name: 'Subjects', icon: BookOpen },
    { id: 'grades', name: 'Grades', icon: Award },
    { id: 'calendar', name: 'Calendar', icon: Calendar },
    { id: 'reports', name: 'Reports', icon: BarChart3 },
  ];

  const features = [
    {
      title: 'Class Management',
      description: 'Create and manage classes with detailed student rosters and teacher assignments.',
      icon: Users,
      benefits: [
        'Create multiple classes with different subjects',
        'Assign teachers to specific classes',
        'Manage student enrollment and transfers',
        'Track class capacity and availability',
        'Generate class schedules automatically',
        'Monitor class performance metrics'
      ]
    },
    {
      title: 'Subject Management',
      description: 'Organize and manage subjects with curriculum planning and assessment tools.',
      icon: BookOpen,
      benefits: [
        'Define subject categories and levels',
        'Create detailed curriculum plans',
        'Set up assessment criteria',
        'Track subject-specific performance',
        'Manage subject prerequisites',
        'Generate subject reports'
      ]
    },
    {
      title: 'Grade Management',
      description: 'Comprehensive grade tracking with customizable grading systems and reporting.',
      icon: Award,
      benefits: [
        'Customizable grading scales',
        'Multiple assessment types',
        'Grade calculation automation',
        'Progress tracking over time',
        'Grade history and trends',
        'Parent grade notifications'
      ]
    },
    {
      title: 'Academic Calendar',
      description: 'Manage academic events, holidays, and important dates with calendar integration.',
      icon: Calendar,
      benefits: [
        'Academic year planning',
        'Holiday and break management',
        'Exam schedule coordination',
        'Event notifications',
        'Calendar synchronization',
        'Multi-campus calendar support'
      ]
    },
    {
      title: 'Performance Analytics',
      description: 'Advanced analytics and reporting for academic performance tracking.',
      icon: BarChart3,
      benefits: [
        'Student performance dashboards',
        'Class comparison analytics',
        'Trend analysis and forecasting',
        'Custom report generation',
        'Data export capabilities',
        'Real-time performance monitoring'
      ]
    },
    {
      title: 'Document Management',
      description: 'Centralized storage and management of academic documents and records.',
      icon: FileText,
      benefits: [
        'Digital document storage',
        'Version control and history',
        'Secure access controls',
        'Document templates',
        'Automated archiving',
        'Search and retrieval tools'
      ]
    }
  ];

  const quickActions = [
    {
      title: 'Create New Class',
      description: 'Set up a new class with students and teachers',
      icon: Plus,
      action: () => console.log('Create class')
    },
    {
      title: 'Add Subject',
      description: 'Create a new subject with curriculum details',
      icon: BookOpen,
      action: () => console.log('Add subject')
    },
    {
      title: 'Generate Report',
      description: 'Create academic performance reports',
      icon: BarChart3,
      action: () => console.log('Generate report')
    },
    {
      title: 'Manage Grades',
      description: 'Update and review student grades',
      icon: Award,
      action: () => console.log('Manage grades')
    }
  ];

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      <SharedNavigation showBackButton={true} backButtonText="Back to Features" />
      
      {/* Hero Section */}
      <section className="pt-20 sm:pt-24 pb-8 sm:pb-12 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <div className="text-center">
            <div className="flex justify-center mb-4 sm:mb-6">
              <div className="p-3 sm:p-4 bg-teal-100 dark:bg-teal-900/30 rounded-full">
                <GraduationCap className="w-8 h-8 sm:w-12 sm:h-12 text-teal-600 dark:text-teal-400" />
              </div>
            </div>
            <h1 className="text-3xl sm:text-4xl lg:text-5xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
              Academic Management
            </h1>
            <p className="text-lg sm:text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-4">
              Comprehensive tools for managing classes, subjects, grades, and academic records. 
              Streamline your academic operations with powerful management capabilities.
            </p>
          </div>
        </div>
      </section>

      {/* Tab Navigation */}
      <section className="px-4 sm:px-6 lg:px-8 pb-6 sm:pb-8">
        <div className="max-w-7xl mx-auto">
          <div className="flex flex-wrap justify-center gap-2 sm:gap-4">
            {tabs.map((tab) => {
              const Icon = tab.icon;
              return (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`flex items-center space-x-2 px-3 sm:px-4 py-2 rounded-lg font-medium transition-all duration-200 text-sm sm:text-base ${
                    activeTab === tab.id
                      ? 'bg-teal-600 text-white shadow-lg'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 hover:bg-teal-50 dark:hover:bg-gray-700 border border-gray-200 dark:border-gray-700'
                  }`}
                >
                  <Icon className="w-4 h-4 sm:w-5 sm:h-5" />
                  <span>{tab.name}</span>
                </button>
              );
            })}
          </div>
        </div>
      </section>

      {/* Content Sections */}
      <section className="px-4 sm:px-6 lg:px-8 pb-12 sm:pb-16">
        <div className="max-w-7xl mx-auto">
          {activeTab === 'overview' && (
            <div className="space-y-8 sm:space-y-12">
              {/* Features Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 sm:gap-8">
                {features.map((feature, index) => {
                  const Icon = feature.icon;
                  return (
                    <div
                      key={index}
                      className="bg-white dark:bg-gray-800 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 dark:border-gray-700 overflow-hidden"
                    >
                      <div className="p-4 sm:p-6">
                        <div className="flex items-center mb-4">
                          <div className="p-2 sm:p-3 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-3 sm:mr-4">
                            <Icon className="w-5 h-5 sm:w-6 sm:h-6 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">
                            {feature.title}
                          </h3>
                        </div>
                        
                        <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mb-4">
                          {feature.description}
                        </p>
                        
                        <ul className="space-y-1 sm:space-y-2">
                          {feature.benefits.map((benefit, idx) => (
                            <li key={idx} className="flex items-center text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                              <CheckCircle className="w-3 h-3 sm:w-4 sm:h-4 text-teal-500 mr-2 flex-shrink-0" />
                              <span className="line-clamp-2">{benefit}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  );
                })}
              </div>

              {/* Quick Actions */}
              <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
                <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                  Quick Actions
                </h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6">
                  {quickActions.map((action, index) => {
                    const Icon = action.icon;
                    return (
                      <button
                        key={index}
                        onClick={action.action}
                        className="p-4 sm:p-6 bg-gray-50 dark:bg-gray-700 rounded-lg hover:bg-teal-50 dark:hover:bg-gray-600 transition-all duration-200 text-left group"
                      >
                        <div className="flex items-center mb-2 sm:mb-3">
                          <div className="p-2 bg-teal-100 dark:bg-teal-900/30 rounded-lg mr-2 sm:mr-3">
                            <Icon className="w-4 h-4 sm:w-5 sm:h-5 text-teal-600 dark:text-teal-400" />
                          </div>
                          <h4 className="font-semibold text-gray-900 dark:text-white group-hover:text-teal-600 dark:group-hover:text-teal-400 text-sm sm:text-base">
                            {action.title}
                          </h4>
                        </div>
                        <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                          {action.description}
                        </p>
                      </button>
                    );
                  })}
                </div>
              </div>
            </div>
          )}

          {activeTab === 'classes' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Class Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Class Creation
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Set class name and description</li>
                      <li>• Assign teachers and subjects</li>
                      <li>• Define class capacity</li>
                      <li>• Set academic year and term</li>
                      <li>• Configure grading policies</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Student Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Enroll students in classes</li>
                      <li>• Track attendance records</li>
                      <li>• Monitor performance metrics</li>
                      <li>• Generate class reports</li>
                      <li>• Manage transfers</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'subjects' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Subject Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Subject Setup
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Create subject categories</li>
                      <li>• Define subject levels</li>
                      <li>• Set credit hours</li>
                      <li>• Configure prerequisites</li>
                      <li>• Assign teachers</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Curriculum Planning
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Design curriculum structure</li>
                      <li>• Set learning objectives</li>
                      <li>• Plan assessment methods</li>
                      <li>• Track progress</li>
                      <li>• Generate reports</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'grades' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Grade Management
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Grading System
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Customizable grading scales</li>
                      <li>• Multiple assessment types</li>
                      <li>• Weighted grade calculations</li>
                      <li>• Grade point averages</li>
                      <li>• Academic standing</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Assessment Tools
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Exam management</li>
                      <li>• Assignment tracking</li>
                      <li>• Participation scoring</li>
                      <li>• Progress monitoring</li>
                      <li>• Report generation</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'calendar' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Academic Calendar
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Calendar Management
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Academic year planning</li>
                      <li>• Term and semester setup</li>
                      <li>• Holiday management</li>
                      <li>• Event scheduling</li>
                      <li>• Notification system</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Scheduling Tools
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Exam scheduling</li>
                      <li>• Parent-teacher meetings</li>
                      <li>• School events</li>
                      <li>• Room allocation</li>
                      <li>• Conflict detection</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}

          {activeTab === 'reports' && (
            <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg p-6 sm:p-8">
              <h3 className="text-xl sm:text-2xl font-bold text-gray-900 dark:text-white mb-4 sm:mb-6">
                Reports & Analytics
              </h3>
              <div className="space-y-6">
                <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Performance Reports
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Student performance reports</li>
                      <li>• Class comparison analytics</li>
                      <li>• Grade distribution analysis</li>
                      <li>• Progress tracking</li>
                      <li>• Trend analysis</li>
                    </ul>
                  </div>
                  <div>
                    <h4 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">
                      Custom Reports
                    </h4>
                    <ul className="space-y-2 text-sm sm:text-base text-gray-600 dark:text-gray-400">
                      <li>• Custom report builder</li>
                      <li>• Data export capabilities</li>
                      <li>• Scheduled report generation</li>
                      <li>• Interactive dashboards</li>
                      <li>• Real-time analytics</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-teal-600 dark:bg-teal-600 text-white py-12 sm:py-16 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h2 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 sm:mb-6">
            Ready to Transform Your Academic Management?
          </h2>
          <p className="text-lg sm:text-xl mb-6 sm:mb-8 text-teal-100 dark:text-teal-100 max-w-3xl mx-auto">
            Start using these powerful academic management tools to streamline your school operations.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <button
              onClick={() => handleNavigation('/docs/getting-started')}
              className="bg-white text-teal-600 hover:bg-gray-100 dark:bg-white dark:text-teal-600 dark:hover:bg-gray-100 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Get Started
            </button>
            <button
              onClick={() => handleNavigation('/contact')}
              className="border-2 border-white text-white hover:bg-white hover:text-teal-600 dark:border-white dark:text-white dark:hover:bg-white dark:hover:text-teal-600 px-6 sm:px-8 py-3 rounded-lg font-medium transition-colors"
            >
              Contact Sales
            </button>
          </div>
        </div>
      </section>

      <SharedFooter />
    </div>
  );
} 