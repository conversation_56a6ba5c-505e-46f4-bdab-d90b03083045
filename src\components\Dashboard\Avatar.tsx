import React from 'react';
import { LogOut, Loader2 } from 'lucide-react';
import useAuth from '@/app/hooks/useAuth';

// Define the types for the props
interface AvatarProps {
  avatarUrl: string; // URL for the avatar image
  name: string; // User's name
  role: string; // User's role (e.g., 'Admin', 'User')
}

export default function Avatar({
  avatarUrl,
  name,
  role
}: AvatarProps) {
  const { logout, isLoggingOut } = useAuth();
  return (
    <div className="flex items-center justify-between">
      {/* Left section: Avatar image and user info */}
      <div className="flex items-center gap-3">
        <div className="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center">
          {/* Avatar Image */}
          <img src={avatarUrl} alt="Avatar" className="w-full h-full rounded-full" />
        </div>
        <div className="flex flex-col">
          {/* Name and Role */}
          <span className="font-semibold">{name}</span>
          <span className="text-sm text-foreground">{role}</span>
        </div>
      </div>

      {/* Right section: Log out icon */}
      <div
        className={`${isLoggingOut ? 'cursor-not-allowed opacity-50' : 'cursor-pointer'}`}
        onClick={isLoggingOut ? undefined : () => logout()}
      >
        {isLoggingOut ? (
          <Loader2 className='text-teal animate-spin' size={20} />
        ) : (
          <LogOut className='text-teal' />
        )}
      </div>
    </div>
  );
}
