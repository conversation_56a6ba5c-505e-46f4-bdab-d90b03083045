"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Lock, 
  Crown, 
  Zap, 
  ArrowRight, 
  Star,
  CheckCircle,
  AlertTriangle
} from 'lucide-react';
import { useTranslation } from '@/hooks/useTranslation';

interface FeatureGateProps {
  featureId: string;
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showUpgradePrompt?: boolean;
  className?: string;
  requiredLevel?: 'basic' | 'standard' | 'premium' | 'enterprise';
  currentLevel?: 'basic' | 'standard' | 'premium' | 'enterprise';
  onUpgradeClick?: () => void;
}

interface FeatureAccessResult {
  hasAccess: boolean;
  reason?: string;
  required_level?: string;
  current_level?: string;
  feature?: {
    name: string;
    description: string;
    subscription_level: string;
  };
}

/**
 * FeatureGate Component
 * Controls access to features based on subscription level
 */
const FeatureGate: React.FC<FeatureGateProps> = ({
  featureId,
  children,
  fallback,
  showUpgradePrompt = true,
  className = '',
  requiredLevel,
  currentLevel,
  onUpgradeClick
}) => {
  const { t } = useTranslation();
  const [accessResult, setAccessResult] = useState<FeatureAccessResult | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    checkFeatureAccess();
  }, [featureId]);

  const checkFeatureAccess = async () => {
    try {
      setLoading(true);
      setError(null);

      // Get school ID from localStorage or context
      const schoolId = localStorage.getItem('school_id') || 
                      JSON.parse(localStorage.getItem('user') || '{}').school_ids?.[0];

      if (!schoolId) {
        setError('School information not found');
        return;
      }

      const response = await fetch(`/api/feature-registry/check/${featureId}/school/${schoolId}`, {
        headers: {
          'Authorization': `Bearer ${localStorage.getItem('token')}`,
          'Content-Type': 'application/json'
        }
      });

      const data = await response.json();

      if (response.ok && data.success) {
        setAccessResult({ hasAccess: true, ...data.data });
      } else {
        setAccessResult({
          hasAccess: false,
          reason: data.error_code,
          required_level: data.subscription_required || requiredLevel,
          current_level: data.current_subscription || currentLevel
        });
      }
    } catch (err) {
      console.error('Error checking feature access:', err);
      setError('Failed to check feature access');
      // Fallback to props if API fails
      if (requiredLevel && currentLevel) {
        const levelHierarchy = { basic: 1, standard: 2, premium: 3, enterprise: 4 };
        const hasAccess = levelHierarchy[currentLevel] >= levelHierarchy[requiredLevel];
        setAccessResult({
          hasAccess,
          required_level: requiredLevel,
          current_level: currentLevel
        });
      }
    } finally {
      setLoading(false);
    }
  };

  const handleUpgradeClick = () => {
    if (onUpgradeClick) {
      onUpgradeClick();
    } else {
      // Default upgrade action
      const upgradeUrl = `/subscription/upgrade?plan=${accessResult?.required_level}&feature=${featureId}`;
      window.location.href = upgradeUrl;
    }
  };

  const getSubscriptionIcon = (level: string) => {
    switch (level) {
      case 'premium':
      case 'enterprise':
        return <Crown className="w-5 h-5 text-yellow-500" />;
      case 'standard':
        return <Zap className="w-5 h-5 text-blue-500" />;
      default:
        return <Star className="w-5 h-5 text-gray-500" />;
    }
  };

  const getSubscriptionColor = (level: string) => {
    switch (level) {
      case 'premium':
        return 'from-yellow-500 to-orange-500';
      case 'enterprise':
        return 'from-purple-500 to-pink-500';
      case 'standard':
        return 'from-blue-500 to-cyan-500';
      default:
        return 'from-gray-500 to-gray-600';
    }
  };

  if (loading) {
    return (
      <div className={`animate-pulse ${className}`}>
        <div className="bg-gray-200 rounded-lg h-20 w-full"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className={`p-4 bg-red-50 border border-red-200 rounded-lg ${className}`}>
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-5 h-5 text-red-500" />
          <span className="text-red-700">{error}</span>
        </div>
      </div>
    );
  }

  // If user has access, render children
  if (accessResult?.hasAccess) {
    return <div className={className}>{children}</div>;
  }

  // If fallback is provided, use it
  if (fallback) {
    return <div className={className}>{fallback}</div>;
  }

  // If no upgrade prompt should be shown, render nothing
  if (!showUpgradePrompt) {
    return null;
  }

  // Render upgrade prompt
  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        exit={{ opacity: 0, y: -20 }}
        className={`relative overflow-hidden rounded-xl border border-gray-200 bg-white shadow-sm ${className}`}
      >
        {/* Background gradient */}
        <div className={`absolute inset-0 bg-gradient-to-br ${getSubscriptionColor(accessResult?.required_level || 'standard')} opacity-5`} />
        
        <div className="relative p-6">
          <div className="flex items-start space-x-4">
            <div className="flex-shrink-0">
              <div className="flex items-center justify-center w-12 h-12 bg-gray-100 rounded-full">
                <Lock className="w-6 h-6 text-gray-600" />
              </div>
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center space-x-2 mb-2">
                {getSubscriptionIcon(accessResult?.required_level || 'standard')}
                <h3 className="text-lg font-semibold text-gray-900">
                  {t('subscription.feature_locked')}
                </h3>
              </div>
              
              <p className="text-gray-600 mb-4">
                {t('subscription.upgrade_required', {
                  plan: accessResult?.required_level || 'standard'
                })}
              </p>
              
              {accessResult?.feature && (
                <div className="mb-4 p-3 bg-gray-50 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-1">
                    {accessResult.feature.name}
                  </h4>
                  <p className="text-sm text-gray-600">
                    {accessResult.feature.description}
                  </p>
                </div>
              )}
              
              <div className="flex items-center justify-between">
                <div className="text-sm text-gray-500">
                  {t('subscription.current_plan')}: 
                  <span className="font-medium ml-1 capitalize">
                    {accessResult?.current_level || 'basic'}
                  </span>
                </div>
                
                <motion.button
                  whileHover={{ scale: 1.02 }}
                  whileTap={{ scale: 0.98 }}
                  onClick={handleUpgradeClick}
                  className={`inline-flex items-center px-4 py-2 bg-gradient-to-r ${getSubscriptionColor(accessResult?.required_level || 'standard')} text-white font-medium rounded-lg hover:shadow-lg transition-all duration-200`}
                >
                  {t('subscription.upgrade_now')}
                  <ArrowRight className="w-4 h-4 ml-2" />
                </motion.button>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </AnimatePresence>
  );
};

export default FeatureGate;
